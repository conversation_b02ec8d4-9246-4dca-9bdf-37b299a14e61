<?xml version="1.0" encoding="utf-8"?>
<network-security-config>
    <!-- 开发环境配置：允许本地服务器的HTTP流量 -->
    <domain-config cleartextTrafficPermitted="true">
        <!-- 本地开发服务器 -->
        <domain includeSubdomains="false">**************</domain>
        <domain includeSubdomains="false">localhost</domain>
        <domain includeSubdomains="false">127.0.0.1</domain>
        <domain includeSubdomains="false">********</domain>
        <!-- 添加其他可能的本地IP地址 -->
        <domain includeSubdomains="true">***********/16</domain>
        <domain includeSubdomains="true">10.0.0.0/8</domain>
        <domain includeSubdomains="true">**********/12</domain>
    </domain-config>
    
    <!-- 生产环境配置：仅允许HTTPS -->
    <base-config cleartextTrafficPermitted="false">
        <trust-anchors>
            <!-- 信任系统证书 -->
            <certificates src="system"/>
        </trust-anchors>
    </base-config>
</network-security-config>
