package com.lfb.android.footprint.geofence

import android.content.Context
import com.google.android.gms.common.ConnectionResult
import com.google.android.gms.common.GoogleApiAvailability
import com.lfb.android.footprint.Manager.LocalLogManager
/**
 * Google Play Services 检测工具类
 * 用于检查设备是否支持地理围栏功能
 */
object GooglePlayServicesChecker {

    /**
     * 检查Google Play Services是否可用
     * @param context 应用上下文
     * @return true表示可用，false表示不可用
     */
    fun isGooglePlayServicesAvailable(context: Context): Boolean {
        val localLogManager = LocalLogManager.getInstance(context)
        
        return try {
            val googleApiAvailability = GoogleApiAvailability.getInstance()
            val resultCode = googleApiAvailability.isGooglePlayServicesAvailable(context)
            
            when (resultCode) {
                ConnectionResult.SUCCESS -> {
                    localLogManager.logSync("GooglePlayServicesChecker: Google Play Services可用")
                    true
                }
                ConnectionResult.SERVICE_MISSING -> {
                    localLogManager.logSync("GooglePlayServicesChecker: Google Play Services未安装")
                    false
                }
                ConnectionResult.SERVICE_VERSION_UPDATE_REQUIRED -> {
                    localLogManager.logSync("GooglePlayServicesChecker: Google Play Services需要更新")
                    false
                }
                ConnectionResult.SERVICE_DISABLED -> {
//                    localLogManager.logSync("GooglePlayServicesChecker: Google Play Services已禁用")
                    false
                }
                ConnectionResult.SERVICE_INVALID -> {
                    localLogManager.logSync("GooglePlayServicesChecker: Google Play Services版本无效")
                    false
                }
                else -> {
                    localLogManager.logSync("GooglePlayServicesChecker: Google Play Services不可用，错误代码: $resultCode")
                    false
                }
            }
        } catch (e: Exception) {
            localLogManager.logSync("GooglePlayServicesChecker: 检查Google Play Services时发生异常 - ${e.message}")
            false
        }
    }

    /**
     * 获取Google Play Services状态的详细描述
     * @param context 应用上下文
     * @return 状态描述字符串
     */
    fun getGooglePlayServicesStatusDescription(context: Context): String {
        return try {
            val googleApiAvailability = GoogleApiAvailability.getInstance()
            val resultCode = googleApiAvailability.isGooglePlayServicesAvailable(context)
            
            when (resultCode) {
                ConnectionResult.SUCCESS -> 
                    "Google Play Services可用"
                ConnectionResult.SERVICE_MISSING -> 
                    "Google Play Services未安装"
                ConnectionResult.SERVICE_VERSION_UPDATE_REQUIRED -> 
                    "Google Play Services需要更新"
                ConnectionResult.SERVICE_DISABLED -> 
                    "Google Play Services已禁用"
                ConnectionResult.SERVICE_INVALID -> 
                    "Google Play Services版本无效"
                else -> 
                    "Google Play Services不可用 (错误代码: $resultCode)"
            }
        } catch (e: Exception) {
            "检查Google Play Services状态失败: ${e.message}"
        }
    }

    /**
     * 检查是否支持地理围栏功能
     * 这是对isGooglePlayServicesAvailable的语义化封装
     * @param context 应用上下文
     * @return true表示支持地理围栏，false表示不支持
     */
    fun isGeofenceSupported(context: Context): Boolean {
        val isAvailable = isGooglePlayServicesAvailable(context)
        val localLogManager = LocalLogManager.getInstance(context)
        
        if (isAvailable) {
            localLogManager.logSync("GooglePlayServicesChecker: 设备支持地理围栏功能")
        } else {
//            localLogManager.logSync("GooglePlayServicesChecker: 设备不支持地理围栏功能，将禁用地理围栏")
        }
        
        return isAvailable
    }

    /**
     * 获取不支持地理围栏的原因说明
     * @param context 应用上下文
     * @return 原因说明
     */
    fun getGeofenceUnsupportedReason(context: Context): String {
        return if (isGooglePlayServicesAvailable(context)) {
            "地理围栏功能正常可用"
        } else {
            buildString {
                append("地理围栏功能不可用，原因：")
                append(getGooglePlayServicesStatusDescription(context))
                append("\n\n")
                append("说明：地理围栏功能依赖Google Play Services来实现后台唤醒。")
                append("没有Google Play Services时，即使实现自定义地理围栏逻辑，")
                append("也无法在应用被系统杀死后重新唤醒应用。")
                append("因此在此情况下禁用地理围栏功能。")
            }
        }
    }

    /**
     * 检查是否支持 FusedLocationProvider
     * 这是对isGooglePlayServicesAvailable的语义化封装
     * @param context 应用上下文
     * @return true表示支持 FusedLocationProvider，false表示不支持
     */
    fun isFusedLocationProviderSupported(context: Context): Boolean {
        val isAvailable = isGooglePlayServicesAvailable(context)
        val localLogManager = LocalLogManager.getInstance(context)

        if (isAvailable) {
            localLogManager.logSync("GooglePlayServicesChecker: 设备支持 FusedLocationProvider")
        } else {
            localLogManager.logSync("GooglePlayServicesChecker: 设备不支持 FusedLocationProvider，将使用系统 LocationManager")
        }

        return isAvailable
    }

    /**
     * 获取不支持 FusedLocationProvider 的原因说明
     * @param context 应用上下文
     * @return 原因说明
     */
    fun getFusedLocationProviderUnsupportedReason(context: Context): String {
        return if (isGooglePlayServicesAvailable(context)) {
            "FusedLocationProvider 功能正常可用"
        } else {
            buildString {
                append("FusedLocationProvider 不可用，原因：")
                append(getGooglePlayServicesStatusDescription(context))
                append("\n\n")
                append("说明：FusedLocationProvider 依赖 Google Play Services。")
                append("没有 Google Play Services 时，将自动降级使用系统 LocationManager。")
            }
        }
    }
}
