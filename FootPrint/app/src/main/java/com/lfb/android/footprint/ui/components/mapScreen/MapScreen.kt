package com.lfb.android.footprint.ui.components.mapScreen

import android.content.Intent
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.tween
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.platform.LocalLifecycleOwner
import androidx.compose.ui.text.font.FontWeight
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.lfb.android.footprint.ui.theme.rememberMapThemeConfig
import com.lfb.android.footprint.Manager.RealmModelManager
import com.mapbox.geojson.Point
import com.mapbox.maps.CameraOptions
import com.lfb.android.footprint.R
import com.lfb.android.footprint.SettingsActivity
import com.lfb.android.footprint.location.LocationManager
import com.lfb.android.footprint.location.LocationDataRecorder
import com.lfb.android.footprint.model.StepDataRealmModel
import com.lfb.android.footprint.prefs.AppPrefs
import com.lfb.android.footprint.ui.components.CustomToast
import com.lfb.android.footprint.ui.components.DetailDataPanel
import com.mapbox.maps.extension.compose.animation.viewport.rememberMapViewportState
import kotlinx.coroutines.*
import kotlinx.datetime.*
import kotlin.coroutines.coroutineContext
import kotlin.math.abs

@Composable
fun MapScreen(
    locationManager: LocationManager,
    locationDataRecorder: LocationDataRecorder,
    showRecordModeDialog: (callback: (Int) -> Unit) -> Unit
) {
    // 地图视口状态
    val mapViewportState = rememberMapViewportState {
        val point = Point.fromLngLat(
            AppPrefs.sharedInstance.lastLocationlongitude,
            AppPrefs.sharedInstance.lastLocationlatitude
        )
        setCameraOptions {
            center(point)
            zoom(15.0)
        }
    }

    // 核心状态
    val currentLocation = locationManager.locationFlow.collectAsState(initial = locationManager.lastLocation).value
    var runningModel by remember { mutableStateOf(AppPrefs.sharedInstance.runningModel) }

    // 监听生命周期变化，在Activity恢复时更新runningModel
    val lifecycleOwner = LocalLifecycleOwner.current
    DisposableEffect(lifecycleOwner) {
        val observer = LifecycleEventObserver { _, event ->
            if (event == Lifecycle.Event.ON_RESUME) {
                runningModel = AppPrefs.sharedInstance.runningModel
            }
        }
        lifecycleOwner.lifecycle.addObserver(observer)
        onDispose {
            lifecycleOwner.lifecycle.removeObserver(observer)
        }
    }
    var selectedFilter by remember { mutableStateOf(2) }
    var showLocationToast by remember { mutableStateOf(false) }
    var mapStyleLoaded by remember { mutableStateOf(false) }

    // 地图配置状态 - 使其响应式
    var mapDisplayType by remember { mutableStateOf(AppPrefs.sharedInstance.mapDisplayType) }
    var mapShowAddressName by remember { mutableStateOf(AppPrefs.sharedInstance.mapShowAdressName) }
    var mapDrawLineAlpha by remember { mutableStateOf(AppPrefs.sharedInstance.mapDrawLineAlpha) }
    var mapDrawSpotAlpha by remember { mutableStateOf(AppPrefs.sharedInstance.mapDrawSpotAlpha) }
    var mapDrawLineWidth by remember { mutableStateOf(AppPrefs.sharedInstance.mapDrawLineWidth) }
    var mapDrawLineColor by remember { mutableStateOf(AppPrefs.sharedInstance.mapDrawLineColor) }

    // 轨迹相关状态
    var trackPoints by remember { mutableStateOf(mutableListOf<Point>()) }
    var trackDrawVersion by remember { mutableStateOf(0) }

    var drawPointAnimationType by remember { mutableStateOf(DrawPointAnimationType.FREE_MODE) }
    var detailTrackDrawVersion by remember { mutableStateOf(0) }

    // 原始轨迹数据状态
//    var rawTrackPoints by remember { mutableStateOf<List<Point>>(emptyList()) }
//    var rawTrackDrawVersion by remember { mutableStateOf(0) }
//    var showRawTrack by remember { mutableStateOf(true) } // 默认显示原始轨迹

    // 多日轨迹数据状态（按天分组）
    var multiDayTrackPoints by remember { mutableStateOf<Map<String, List<Point>>>(emptyMap()) }
    var multiDayTrackDrawVersion by remember { mutableStateOf(0) }
    // 距离过滤后的多日轨迹数据（按天分组，每天内按距离分段）
    var multiDayTrackSegments by remember { mutableStateOf<Map<String, List<List<Point>>>>(emptyMap()) }
    // 距离过滤参数状态，用于监听变化
    var mapDrawLineDistanceFilter by remember { mutableStateOf(AppPrefs.sharedInstance.mapDrawLineDistanceFilter) }

    // 面板状态
    var showConfigPanel by remember { mutableStateOf(false) }
    var showDetailPanel by remember { mutableStateOf(false) }
    var showCustomTimeSelector by remember { mutableStateOf(false) }
    var showTodayHomeCard by remember { mutableStateOf(true) } // 默认显示今日首页卡片
    var didShowTodayHomeCard by remember { mutableStateOf(false) } // 标识今日首页卡片是否已经展示过

    var showMapControlButtons by remember { mutableStateOf(false) } // 默认不显示


    // 自定义时间范围状态 - 默认结束时间为今日，开始时间为一个月前
    val now = Clock.System.now().toLocalDateTime(TimeZone.currentSystemDefault()).date
    val defaultEndTime = (now.plus(DatePeriod(days = 1))).atStartOfDayIn(TimeZone.currentSystemDefault()).epochSeconds
    val defaultStartTime = (now.minus(DatePeriod(months = 1))).atStartOfDayIn(TimeZone.currentSystemDefault()).epochSeconds

    var customStartTime by remember { mutableStateOf(defaultStartTime) }
    var customEndTime by remember { mutableStateOf(defaultEndTime) }

    // 详细面板相关状态
    var detailPanelTrackPoints by remember { mutableStateOf<List<Point>>(emptyList()) }
    var selectedDataPoint by remember { mutableStateOf<Point?>(null) }
    var selectedDataPointInfo by remember { mutableStateOf<StepDataRealmModel?>(null) }
    var detailPanelHeight by remember { mutableStateOf(600) }
    var preDeleteDataPoints by remember { mutableStateOf<List<StepDataRealmModel>>(emptyList()) }
    var originalDayDataPoints by remember { mutableStateOf<List<StepDataRealmModel>>(emptyList()) }

    // 一生模式相关状态
    var lifetimeDataPoints by remember { mutableStateOf<List<Point>>(emptyList()) }
    var lifetimeDataVersion by remember { mutableStateOf(0) }
    var currentMapBounds by remember { mutableStateOf<Pair<Pair<Double, Double>, Pair<Double, Double>>?>(null) }
    var lastLoadedBounds by remember { mutableStateOf<Pair<Pair<Double, Double>, Pair<Double, Double>>?>(null) }
    var lastLoadedZoom by remember { mutableStateOf<Double?>(null) }
    var isLoadingLifetimeData by remember { mutableStateOf(false) }
    var pendingViewportUpdate by remember { mutableStateOf<(() -> Unit)?>(null) }
    var lifetimeTotalDataCount by remember { mutableStateOf(0L) }
    var lifetimeTotalDistance by remember { mutableStateOf(0.0) }

    // 取消机制相关状态
    var currentDataLoadingJob by remember { mutableStateOf<Job?>(null) }
    var currentLoadingTaskId by remember { mutableStateOf(0L) }

    // 获取屏幕密度和尺寸
    val density = LocalDensity.current.density
    val context = LocalContext.current
    val displayMetrics = context.resources.displayMetrics
    val screenHeightPx = displayMetrics.heightPixels
    val screenHeight = screenHeightPx.toDouble()

    val coroutineScope = rememberCoroutineScope()

    // 地图视口变化处理函数（带防抖机制）
    val handleViewportChanged = { minLat: Double, maxLat: Double, minLng: Double, maxLng: Double, screenWidth: Int, screenHeight: Int, zoomLevel: Double ->
        if (selectedFilter == 0) {
            // 检查是否需要重新加载数据
            val shouldReload = lastLoadedBounds?.let { lastBounds ->
                // 检查缩放级别变化
                val zoomChanged = lastLoadedZoom?.let { lastZoom ->
                    abs(zoomLevel - lastZoom) >= 1.0 // 缩放级别变化超过1级
                } ?: true

                if (zoomChanged) {
                    true // 缩放级别变化超过1级，需要重新加载
                } else {
                    // 计算当前视口中心点
                    val currentCenterLat = (minLat + maxLat) / 2
                    val currentCenterLng = (minLng + maxLng) / 2

                    // 计算上次加载的视口中心点
                    val lastCenterLat = (lastBounds.first.first + lastBounds.first.second) / 2
                    val lastCenterLng = (lastBounds.second.first + lastBounds.second.second) / 2

                    // 计算移动距离（以视口大小的比例计算）
                    val latRange = maxLat - minLat
                    val lngRange = maxLng - minLng
                    val latMovement = abs(currentCenterLat - lastCenterLat) / latRange
                    val lngMovement = abs(currentCenterLng - lastCenterLng) / lngRange

                    // 如果移动距离超过半个屏幕，则需要重新加载
                    latMovement > 0.25 || lngMovement > 0.25
                }
            } ?: true // 如果是第一次加载，则需要加载

            if (shouldReload) {
                // 取消之前的数据加载任务
                currentDataLoadingJob?.let { job ->
                    println("lifetime: 取消 previous data loading job, isActive: ${job.isActive}")
                    job.cancel()
                }
                currentDataLoadingJob = null

                // 取消之前的待处理更新
                pendingViewportUpdate?.let {
                    pendingViewportUpdate = null
                }

//                println("lifetime: 开始 new viewport data loading, taskId will be: ${currentLoadingTaskId + 1}")

                // 创建新的待处理更新
                val updateAction = {
                    // 生成新的任务ID（移除isLoadingLifetimeData检查，让取消机制处理并发）
                    val taskId = ++currentLoadingTaskId
                    println("lifetime: 创建新的 new data loading task with ID: $taskId")

                    val job = coroutineScope.launch {
                        try {
                            isLoadingLifetimeData = true
                            val realmManager = RealmModelManager.getInstance()

                            // 计算扩展后的边界（增加半个屏幕的缓冲区）
                            val latRange = maxLat - minLat
                            val lngRange = maxLng - minLng
                            val bufferLat = latRange * 0.25 // 半个屏幕的纬度范围
                            val bufferLng = lngRange * 0.25 // 半个屏幕的经度范围

                            val expandedMinLat = minLat - bufferLat
                            val expandedMaxLat = maxLat + bufferLat
                            val expandedMinLng = minLng - bufferLng
                            val expandedMaxLng = maxLng + bufferLng

                            println("lifetime: Task $taskId - Loading data for expanded bounds: lat[${expandedMinLat}, ${expandedMaxLat}], lng[${expandedMinLng}, ${expandedMaxLng}], screen: ${screenWidth}x${screenHeight}")

                            // 清空现有数据，准备接收新的批量数据
                            lifetimeDataPoints = emptyList()
                            val allBatchPoints = mutableListOf<Point>()

                            realmManager.getLifetimeDataPointsForViewportBatch(
                                taskId = taskId.toInt(),
                                minLat = expandedMinLat,
                                maxLat = expandedMaxLat,
                                minLng = expandedMinLng,
                                maxLng = expandedMaxLng,
                                screenWidthPx = screenWidth,
                                screenHeightPx = screenHeight,
                                onBatchReady = { batchPoints ->
                                    // 检查任务是否仍然有效
                                    if (currentLoadingTaskId == taskId) {
                                        // 每收到一批数据就立即更新UI
                                        allBatchPoints.addAll(batchPoints)
                                        lifetimeDataPoints = allBatchPoints.toList()
                                        lifetimeDataVersion++
                                        println("lifetime: Task $taskId - 收到数据: ${batchPoints.size} points, 总数: ${allBatchPoints.size}")
                                    } else {
                                        println("lifetime: Task $taskId - Batch ignored: taskId mismatch (current: $currentLoadingTaskId, expected: $taskId)")
                                    }
                                },
                                isCancelled = {
                                    val cancelled = currentLoadingTaskId != taskId
                                    if (cancelled) {
                                        println("lifetime: Task $taskId - 取消 check: taskId mismatch (current: $currentLoadingTaskId, expected: $taskId)")
                                    }
                                    cancelled
                                }
                            )

                            // 只有在任务仍然有效时才更新最终状态
                            if (currentLoadingTaskId == taskId) {
                                println("lifetime: Task $taskId - 完成 successfully, total points: ${lifetimeDataPoints.count()}")

                                // 更新最后加载的边界和缩放级别
                                lastLoadedBounds = Pair(Pair(minLat, maxLat), Pair(minLng, maxLng))
                                lastLoadedZoom = zoomLevel
                            } else {
                                println("lifetime: Task $taskId - 完成 忽略: taskId mismatch (current: $currentLoadingTaskId)")
                            }

                        } catch (e: Exception) {
                            if (e !is CancellationException) {
                                println("lifetime: Task $taskId - Failed to update viewport data: ${e.message}")
                            } else {
                                println("lifetime: Task $taskId - Cancelled via exception")
                            }
                        } finally {
                            // 只有在当前任务仍然有效时才重置状态
                            if (currentLoadingTaskId == taskId) {
                                println("lifetime: Task $taskId - 重置状态")
                                isLoadingLifetimeData = false
                                pendingViewportUpdate = null
                                currentDataLoadingJob = null
                            } else {
                                println("lifetime: Task $taskId - 重置状态 跳过 skipped: taskId mismatch (current: $currentLoadingTaskId)")
                            }
                        }
                    }

                    currentDataLoadingJob = job
                }

                pendingViewportUpdate = updateAction

                // 延迟执行，实现防抖效果
                coroutineScope.launch {
                    delay(300) // 300ms防抖延迟
                    if (pendingViewportUpdate == updateAction) {
                        updateAction()
                    }
                }
            }
        }
    }

    // 过滤器变化监听
    LaunchedEffect(selectedFilter) {
        // 取消正在进行的数据加载任务
        currentDataLoadingJob?.cancel()
        currentDataLoadingJob = null
        currentLoadingTaskId++ // 使所有正在进行的任务失效

        trackPoints.clear()
        multiDayTrackPoints = emptyMap() // 重置多日轨迹数据
        lifetimeDataPoints = emptyList()
        lastLoadedBounds = null // 重置加载边界
        lastLoadedZoom = null // 重置缩放级别
        isLoadingLifetimeData = false // 重置加载状态
        pendingViewportUpdate = null // 重置待处理更新
        lifetimeTotalDataCount = 0L // 重置一生数据统计
        lifetimeTotalDistance = 0.0
        
        // 只在今日模式下且未展示过时显示首页卡片
        showTodayHomeCard = (selectedFilter == 2 && !didShowTodayHomeCard)
        
        when (selectedFilter) {
            0 -> {
                // 一生模式 - 按地图比例过滤数据点
                showCustomTimeSelector = false

                coroutineScope.launch {
                    try {
                        val realmManager = RealmModelManager.getInstance()

                        // 获取所有数据的边界框用于地图初始定位
//                        val bounds = realmManager.getAllDataBounds()
//                        if (bounds != null) {
//                            currentMapBounds = bounds

                            // 获取一生数据统计
                            val (totalCount, totalDistance, _) = realmManager.getLifetimeDataStatsCompat()
                            lifetimeTotalDataCount = totalCount
                            lifetimeTotalDistance = totalDistance

                            // 注意：不在这里加载数据点，等待地图加载完成后通过onViewportChanged回调加载
                            // 这样可以使用当前地图的实际可视边界而不是所有数据的边界
//                        }
                    } catch (e: Exception) {
                        println("Failed to load lifetime data: ${e.message}")
                    }
                }
            }
            1 -> {
                // 自定义时间范围 - 显示时间选择控件并加载默认时间范围的数据（按天分组）
                showCustomTimeSelector = true
                val groupedData = RealmModelManager.getInstance().getStepDataByTimeRangeGroupedByDay(customStartTime, customEndTime)
                multiDayTrackPoints = groupedData.convertGroupedStepDataToPoints()
                multiDayTrackSegments = groupedData.convertGroupedStepDataToPointsWithDistanceFilter()
            }
            2 -> {
                showCustomTimeSelector = false
                trackPoints = locationDataRecorder.getTodayPoints().toMutableList()
//                trackPoints.addAll(RealmModelManager.getInstance().queryTodayData<StepDataRealmModel>().convertStepDataToPoints())
                // 加载今日原始轨迹数据
//                rawTrackPoints = locationDataRecorder.getTodayRawLocations()
            }
            3 -> {
                showCustomTimeSelector = false
                trackPoints.addAll(RealmModelManager.getInstance().queryYesterdayData<StepDataRealmModel>().convertStepDataToPoints())
                // 加载昨日原始轨迹数据
//                rawTrackPoints = locationDataRecorder.getYesterdayRawLocations()
            }
            4 -> {
                // 七日模式 - 按天分组加载数据
                showCustomTimeSelector = false
                val groupedData = RealmModelManager.getInstance().getWeekDataGroupedByDay()
                multiDayTrackPoints = groupedData.convertGroupedStepDataToPoints()
                multiDayTrackSegments = groupedData.convertGroupedStepDataToPointsWithDistanceFilter()
                // 加载七日原始轨迹数据
//                rawTrackPoints = locationDataRecorder.getLastSevenDaysRawLocations()
            }
        }

        // 非一生模式才更新版本号
        if (selectedFilter != 0) {
            if (selectedFilter == 1 || selectedFilter == 4) {
                // 多日模式更新多日轨迹版本
                multiDayTrackDrawVersion++
            } else {
                // 单日模式更新单日轨迹版本
                trackDrawVersion++
            }
            // 更新原始轨迹版本号
//            rawTrackDrawVersion++
            drawPointAnimationType = DrawPointAnimationType.SCALE_MODE
        }
    }

    // 自定义时间范围变化监听（仅在用户手动修改时间时触发）
    LaunchedEffect(customStartTime, customEndTime) {
        // 只有在自定义模式下且时间范围发生变化时才重新加载数据
        if (selectedFilter == 1 && customStartTime > 0 && customEndTime > 0) {
            val groupedData = RealmModelManager.getInstance().getStepDataByTimeRangeGroupedByDay(customStartTime, customEndTime)
            multiDayTrackPoints = groupedData.convertGroupedStepDataToPoints()
            multiDayTrackSegments = groupedData.convertGroupedStepDataToPointsWithDistanceFilter()
            multiDayTrackDrawVersion++
            drawPointAnimationType = DrawPointAnimationType.SCALE_MODE
        }
    }

    // 距离过滤参数变化监听
    LaunchedEffect(mapDrawLineDistanceFilter) {
        // 只有在多日模式下且有数据时才重新生成分段数据
        if ((selectedFilter == 1 || selectedFilter == 4) && multiDayTrackPoints.isNotEmpty()) {
            // 重新生成距离过滤后的分段数据
            val groupedData = if (selectedFilter == 1) {
                RealmModelManager.getInstance().getStepDataByTimeRangeGroupedByDay(customStartTime, customEndTime)
            } else {
                RealmModelManager.getInstance().getWeekDataGroupedByDay()
            }
            multiDayTrackSegments = groupedData.convertGroupedStepDataToPointsWithDistanceFilter()
            multiDayTrackDrawVersion++
        }
    }

    // 位置更新监听 - 已禁用地图跟随功能
    LaunchedEffect(currentLocation) {
        currentLocation?.let {
            if (selectedFilter != 2) return@let
//            val newPoint = Point.fromLngLat(currentLocation.longitude, currentLocation.latitude)
//            mapViewportState.flyTo(
//                CameraOptions.Builder().center(newPoint).zoom(14.0).build()
//            )

            trackPoints = locationDataRecorder.getTodayPoints().toMutableList()
            
            // 显示位置更新toast
            coroutineScope.launch {
                delay(300)
                showLocationToast = true
            }
        }
    }

    // Toast显示逻辑
    LaunchedEffect(showLocationToast) {
        if (showLocationToast) {
            // 2秒后自动隐藏toast状态
            delay(2000)
            showLocationToast = false
        }
    }

    // 获取当前主题配置
    val themeConfig = rememberMapThemeConfig()

    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(themeConfig.backgroundColor)
    ) {
        // 地图内容
        MapContent(
            mapViewportState = mapViewportState,
            trackPoints = trackPoints,
            trackDrawVersion = trackDrawVersion,
            multiDayTrackPoints = multiDayTrackPoints,
            multiDayTrackDrawVersion = multiDayTrackDrawVersion,
            multiDayTrackSegments = multiDayTrackSegments,
            detailPanelTrackPoints = detailPanelTrackPoints,
            detailTrackDrawVersion = detailTrackDrawVersion,
            selectedDataPoint = selectedDataPoint,
            selectedDataPointInfo = selectedDataPointInfo,
            showDetailPanel = showDetailPanel,
            drawPointAnimationType = drawPointAnimationType,
            detailPanelHeight = detailPanelHeight,
            screenHeight = screenHeight,
            // 一生模式相关参数
            selectedFilter = selectedFilter,
            lifetimeDataPoints = lifetimeDataPoints,
            lifetimeDataVersion = lifetimeDataVersion,
            // 地图配置参数
            mapDisplayType = mapDisplayType,
            mapShowAddressName = mapShowAddressName,
            mapDrawLineAlpha = mapDrawLineAlpha,
            mapDrawSpotAlpha = mapDrawSpotAlpha,
            mapDrawLineWidth = mapDrawLineWidth,
            mapDrawLineColor = mapDrawLineColor,
            onViewportChanged = handleViewportChanged,
            onMapStyleLoaded = { mapStyleLoaded = true },
            onDrawPointAnimationTypeChanged = { drawPointAnimationType = it }
        )
        if (!showTodayHomeCard) {
            // 数据信息卡片
            MapDataInfoCard(
                dataCount = if (selectedFilter == 0) lifetimeTotalDataCount.toInt() else trackPoints.count(),
                dataDistance = if (selectedFilter == 0) lifetimeTotalDistance else calculateTotalDistance(
                    trackPoints
                ).toDouble(),
                selectedIndex = selectedFilter,
                modifier = Modifier
                    .align(Alignment.BottomStart)
                    .padding(
                        end = 12.dp,
                        bottom = if (showCustomTimeSelector) 240.dp else 120.dp
                    )
            )
        }
        if (showMapControlButtons) {
            // 地图控制按钮
            MapControlButtons(
                onLocationClick = {
                    when (drawPointAnimationType) {
                        DrawPointAnimationType.FREE_MODE, DrawPointAnimationType.SCALE_MODE -> {
                            drawPointAnimationType = DrawPointAnimationType.FOLLOW_MODE
                            locationManager.lastLocation?.let {
                                val newPoint = Point.fromLngLat(it.longitude, it.latitude)
                                mapViewportState.flyTo(
                                    CameraOptions.Builder().center(newPoint)
                                        .bearing(0.0)
                                        .zoom(15.0).build()
                                )
                            }
                        }
                        DrawPointAnimationType.FOLLOW_MODE -> {
                            drawPointAnimationType = DrawPointAnimationType.SCALE_MODE
                        }
                    }
                },
                onConfigClick = { showConfigPanel = true },
                onDetailClick = { showDetailPanel = true },
                modifier = Modifier
                    .align(Alignment.BottomEnd)
                    .padding(end = 12.dp, bottom = 140.dp)
            )
        }



        // 自定义时间选择控件
        if (showCustomTimeSelector) {
            CustomTimeRangeSelector(
                startTime = customStartTime,
                endTime = customEndTime,
                onTimeRangeChanged = { startTime, endTime ->
                    customStartTime = startTime
                    customEndTime = endTime
                },
                modifier = Modifier
                    .align(Alignment.BottomStart)
                    .padding(start = 12.dp, bottom = 140.dp)
            )
        }


        // MapContentView - 一直显示，但初始状态由showTodayHomeCard控制
        MapContentView(
            visible = true, // 一直可见
            initialExpanded = showTodayHomeCard, // 初始是否展开
            panelStateChanged = { panelState ->
                if (panelState == PanelState.COLLAPSED) {
                    showTodayHomeCard = false
                    didShowTodayHomeCard = true // 标记已展示过
                    showMapControlButtons = true
                } else {
                    showTodayHomeCard = true
                    showMapControlButtons = false
                }
            },

            locationDataRecorder = locationDataRecorder,
            onFilterSelected = { selectedFilter = it },
            onLeftButtonClick = {
                // 跳转到设置页面
                val intent = Intent(context, SettingsActivity::class.java)
                context.startActivity(intent)
            },
            onRightButtonClick = {
                // 跳转到轨迹总览页面
                val intent = Intent(context, com.lfb.android.footprint.TrackOverviewActivity::class.java)
                context.startActivity(intent)
            },
            leftIconResId = R.drawable.menu,
            rightIconResId = R.drawable.overview,
            modifier = Modifier.fillMaxSize()
        )

        // 自定义Toast
        CustomToast(
            message = "+1",
            isVisible = showLocationToast,
            modifier = Modifier
                .align(Alignment.Center)
//                .padding(bottom = 120.dp)
        )

        // App Header
        AppHeader(
            gpsSignal = currentLocation?.accuracy?.toInt() ?: 0,
            runningModel = runningModel,
            modifier = Modifier
                .align(Alignment.TopStart)
                .padding(start = 16.dp, top = 38.dp)
                .clickable {
                    showRecordModeDialog { selectedMode ->
                        AppPrefs.sharedInstance.runningModel = selectedMode
                        runningModel = selectedMode
                        locationManager.startLocationUpdates()
                    }
                }
        )

        // 当前状态信息卡片
        MapCurrentStateInfoCard(
            altitude = currentLocation?.altitude?.toFloat() ?: 0f,
            speed = currentLocation?.speed ?: 0f,
            modifier = Modifier
                .align(Alignment.TopStart)
                .padding(start = 16.dp, top = 88.dp)
        )

        // 配置面板
        if (showConfigPanel) {
            ConfigPanel(
                onClose = { showConfigPanel = false },
                onMapDisplayTypeChanged = { mapDisplayType = it },
                onMapShowAddressNameChanged = { mapShowAddressName = it },
                onMapDrawLineAlphaChanged = { mapDrawLineAlpha = it },
                onMapDrawSpotAlphaChanged = { mapDrawSpotAlpha = it },
                onMapDrawLineWidthChanged = { mapDrawLineWidth = it },
                onMapDrawLineColorChanged = { mapDrawLineColor = it },
                onMapDrawLineDistanceFilterChanged = { mapDrawLineDistanceFilter = it }
            )
        }

        // 详细数据面板
        if (showDetailPanel) {
            DetailDataPanel(
                modifier = Modifier.align(Alignment.BottomCenter),
                onClose = {
                    showDetailPanel = false
                    detailPanelTrackPoints = emptyList()
                    selectedDataPoint = null
                    selectedDataPointInfo = null
                    preDeleteDataPoints = emptyList()
                    detailTrackDrawVersion++
                },
                onDayDataChanged = { dayDataPoints ->
                    originalDayDataPoints = dayDataPoints
                    detailPanelTrackPoints = dayDataPoints.convertStepDataToPoints()
                    detailTrackDrawVersion++
                    selectedDataPoint = null
                    selectedDataPointInfo = null
                },
                onDataPointSelected = { dataPoint ->
                    selectedDataPoint = Point.fromLngLat(dataPoint.longitude, dataPoint.latitude)
                    selectedDataPointInfo = dataPoint
                    detailTrackDrawVersion++
                },
                onHeightChanged = { heightInDp ->
                    detailPanelHeight = heightInDp
                },
                onPreDeleteDataPoints = { dataPointsToDelete ->
                    preDeleteDataPoints = dataPointsToDelete
                    val deleteDataTimes = dataPointsToDelete.map { it.dataTime }.toSet()
                    val remainingDataPoints = originalDayDataPoints.filter { dataPoint ->
                        !deleteDataTimes.contains(dataPoint.dataTime)
                    }
                    detailPanelTrackPoints = remainingDataPoints.convertStepDataToPoints()
                    selectedDataPoint = null
                    selectedDataPointInfo = null
                    detailTrackDrawVersion++
                },
                onCancelPreDelete = {
                    preDeleteDataPoints = emptyList()
                    detailPanelTrackPoints = originalDayDataPoints.convertStepDataToPoints()
                    detailTrackDrawVersion++
                },
                onConfirmDelete = { deletedDataPoints ->
                    preDeleteDataPoints = emptyList()
                    detailTrackDrawVersion++
                }
            )
        }
    }
}

// 绘制点动画类型枚举
enum class DrawPointAnimationType {
    FREE_MODE,
    FOLLOW_MODE,
    SCALE_MODE
}

// 扩展函数：将StepDataRealmModel列表转换为Point列表
fun List<StepDataRealmModel>.convertStepDataToPoints(): List<Point> {
    return this.map { Point.fromLngLat(it.longitude, it.latitude) }
}

// 扩展函数：将分组的StepDataRealmModel转换为分组的Point列表
fun Map<String, List<StepDataRealmModel>>.convertGroupedStepDataToPoints(): Map<String, List<Point>> {
    return this.mapValues { (_, stepDataList) ->
        stepDataList.convertStepDataToPoints()
    }
}

// 扩展函数：将分组的StepDataRealmModel转换为按距离过滤分组的Point列表
fun Map<String, List<StepDataRealmModel>>.convertGroupedStepDataToPointsWithDistanceFilter(): Map<String, List<List<Point>>> {
    val maxDistance = AppPrefs.sharedInstance.mapDrawLineDistanceFilter.toDouble() // 获取距离过滤参数（米）

    return this.mapValues { (_, stepDataList) ->
        if (stepDataList.isEmpty()) {
            emptyList()
        } else {
            splitTrackByDistance(stepDataList.convertStepDataToPoints(), maxDistance)
        }
    }
}

// 根据距离将轨迹点分组的函数
fun splitTrackByDistance(points: List<Point>, maxDistance: Double): List<List<Point>> {
    if (points.size <= 1) return if (points.isEmpty()) emptyList() else listOf(points)

    val result = mutableListOf<List<Point>>()
    var currentGroup = mutableListOf<Point>()

    currentGroup.add(points[0])

    for (i in 1 until points.size) {
        val prevPoint = points[i - 1]
        val currentPoint = points[i]

        // 计算两点间距离（使用Haversine公式）
        val distance = haversineDistance(
            prevPoint.latitude(), prevPoint.longitude(),
            currentPoint.latitude(), currentPoint.longitude()
        )

        if (distance <= maxDistance) {
            // 距离在允许范围内，添加到当前组
            currentGroup.add(currentPoint)
        } else {
            // 距离超过阈值，结束当前组并开始新组
            if (currentGroup.isNotEmpty()) {
                result.add(currentGroup.toList())
            }
            currentGroup = mutableListOf(currentPoint)
        }
    }

    // 添加最后一组
    if (currentGroup.isNotEmpty()) {
        result.add(currentGroup.toList())
    }

    return result
}

// Haversine 公式计算两点间距离（米）
private fun haversineDistance(lat1: Double, lon1: Double, lat2: Double, lon2: Double): Double {
    val R = 6371000.0 // 地球半径（米）
    val dLat = Math.toRadians(lat2 - lat1)
    val dLon = Math.toRadians(lon2 - lon1)
    val a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
            Math.cos(Math.toRadians(lat1)) * Math.cos(Math.toRadians(lat2)) *
            Math.sin(dLon / 2) * Math.sin(dLon / 2)
    val c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a))
    return R * c
}

// 计算总距离的函数
fun calculateTotalDistance(points: List<Point>): Float {
    if (points.size < 2) return 0f

    var totalDistance = 0.0
    for (i in 1 until points.size) {
        val prevPoint = points[i - 1]
        val currentPoint = points[i]

        // 使用 Haversine 公式计算两点间距离
        val distance = haversineDistance(
            prevPoint.latitude(), prevPoint.longitude(),
            currentPoint.latitude(), currentPoint.longitude()
        )
        totalDistance += distance
    }

    return (totalDistance / 1000).toFloat() // 转换为公里
}
