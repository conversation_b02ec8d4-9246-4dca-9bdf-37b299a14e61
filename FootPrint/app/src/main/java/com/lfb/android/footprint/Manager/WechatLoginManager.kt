package com.lfb.android.footprint.Manager

import android.content.Context
import android.util.Log
import com.lfb.android.footprint.api.ApiService
import com.lfb.android.footprint.config.WechatConfig
import com.lfb.android.footprint.model.UserModel
import com.lfb.android.footprint.model.SubscriptionInfo
import com.tencent.mm.opensdk.modelmsg.SendAuth
import com.tencent.mm.opensdk.openapi.IWXAPI
import com.tencent.mm.opensdk.openapi.WXAPIFactory
import kotlinx.coroutines.suspendCancellableCoroutine
import org.json.JSONObject
import kotlin.coroutines.resume
import kotlin.coroutines.resumeWithException

/**
 * 微信登录管理器
 */
class WechatLoginManager private constructor(private val context: Context) {
    
    companion object {
        // 从配置文件获取微信配置
        const val APP_ID = WechatConfig.APP_ID

        private const val TAG = "WechatLoginManager"

        @Volatile
        private var INSTANCE: WechatLoginManager? = null

        fun getInstance(context: Context): WechatLoginManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: WechatLoginManager(context.applicationContext).also { INSTANCE = it }
            }
        }
    }
    
    private val wxApi: IWXAPI = WXAPIFactory.createWXAPI(context, APP_ID, true)
    
    // 登录回调
    private var loginCallback: ((Result<UserModel>) -> Unit)? = null
    
    init {
        // 注册到微信
        wxApi.registerApp(APP_ID)
    }
    
    /**
     * 检查微信是否安装
     */
    fun isWXAppInstalled(): Boolean {
        return wxApi.isWXAppInstalled
    }
    
    /**
     * 检查微信版本是否支持
     */
    fun isWXAppSupportAPI(): Boolean {
        return wxApi.wxAppSupportAPI >= 0
    }
    
    /**
     * 发起微信登录
     */
    suspend fun loginWithWechat(): UserModel = suspendCancellableCoroutine { continuation ->
        // 检查配置是否完整
        if (!WechatConfig.isConfigured()) {
            continuation.resumeWithException(Exception("微信登录配置未完成，请联系开发者"))
            return@suspendCancellableCoroutine
        }
        
        if (!isWXAppInstalled()) {
            continuation.resumeWithException(Exception("未安装微信"))
            return@suspendCancellableCoroutine
        }
        
        if (!isWXAppSupportAPI()) {
            continuation.resumeWithException(Exception("微信版本过低，不支持登录功能"))
            return@suspendCancellableCoroutine
        }
        
        // 设置回调
        loginCallback = { result ->
            if (result.isSuccess) {
                continuation.resume(result.getOrThrow())
            } else {
                continuation.resumeWithException(result.exceptionOrNull() ?: Exception("登录失败"))
            }
        }
        
        // 发起登录请求
        val req = SendAuth.Req()
        req.scope = "snsapi_userinfo"
//        req.state = "wechat_login"
        
        val success = wxApi.sendReq(req)
        if (!success) {
            loginCallback = null
            continuation.resumeWithException(Exception("发起微信登录失败"))
        }
        
        // 设置取消回调
        continuation.invokeOnCancellation {
            loginCallback = null
        }
    }
    
    /**
     * 处理授权成功
     */
    fun handleAuthSuccess(code: String) {
//        Log.d(TAG, "微信授权成功，开始服务端登录，code: $code")

        // 在后台线程调用服务端登录接口
        Thread {
            try {
                // 获取设备ID
                val deviceId = com.lfb.android.footprint.utils.DeviceUtils.getDeviceId(context)
                
                // 获取APP版本号
                val appVersion = VersionManager.getCurrentVersionName(context)
                
                // 1. 调用服务端微信登录接口
                val loginResponse = ApiService.wechatLogin(code, deviceId, appVersion)

//                // 2. 获取用户订阅信息（可选，因为登录响应中已包含VIP状态）
//                var subscriptionInfo: SubscriptionInfo? = null
//                try {
//                    val subscriptionJson = ApiService.getUserSubscription(loginResponse.bearerToken)
//                    subscriptionJson?.let { json ->
//                        subscriptionInfo = SubscriptionInfo(
//                            id = json.getLong("id").toInt(),
//                            planName = json.getString("plan_name"),
//                            planCode = json.optString("plan_code", ""),
//                            status = json.getString("status"),
//                            startTime = json.getLong("start_time"),
//                            endTime = json.getLong("end_time"),
//                            isActive = json.getBoolean("is_active"),
//                            daysRemaining = 0 // 可以后续计算
//                        )
//                    }
//                } catch (e: Exception) {
//                    Log.w(TAG, "获取订阅详细信息失败: ${e.message}")
//                }

                // 3. 构建用户模型（优先使用服务端返回的VIP状态）
                val user = UserModel(
                    id = loginResponse.user.id,
                    openid = loginResponse.user.openid,
                    nickname = loginResponse.user.nickname ?: "",
                    avatar = loginResponse.user.avatarUrl ?: "",
                    isVip = loginResponse.user.isVip,
                    vipExpireTime = loginResponse.user.vipExpireTime,
                    loginTime = System.currentTimeMillis(),
                    bearerToken = loginResponse.bearerToken,
                    refreshToken = loginResponse.refreshToken,
                    tokenType = loginResponse.tokenType,
                    expiresIn = loginResponse.expiresIn,
                    refreshExpiresIn = loginResponse.refreshExpiresIn
                )

//                Log.d(TAG, "服务端登录成功，用户ID: ${user.id}, VIP状态: ${user.isVip}")
                loginCallback?.invoke(Result.success(user))

            } catch (e: Exception) {
                Log.e(TAG, "服务端登录失败", e)
                loginCallback?.invoke(Result.failure(e))
            } finally {
                loginCallback = null
            }
        }.start()
    }
    
    /**
     * 处理授权取消
     */
    fun handleAuthCancel() {
        loginCallback?.invoke(Result.failure(Exception("用户取消授权")))
        loginCallback = null
    }
    
    /**
     * 处理授权拒绝
     */
    fun handleAuthDenied() {
        loginCallback?.invoke(Result.failure(Exception("用户拒绝授权")))
        loginCallback = null
    }
    
    /**
     * 处理授权错误
     */
    fun handleAuthError(error: String) {
        Log.e(TAG, "微信授权错误: $error")
        loginCallback?.invoke(Result.failure(Exception(error)))
        loginCallback = null
    }
    
    /**
     * 验证用户Token是否有效
     */
    fun verifyUserTokenAndUpdate(user: UserModel) {
        Thread {
            try {

                val req = SendAuth.Req()
                req.scope = "snsapi_userinfo"


                val success = wxApi.sendReq(req)
                if (!success) {
                    loginCallback = null
                }

                val userInfo = ApiService.verifyToken(user.bearerToken)

                // 更新用户信息
                user.copy(
                    nickname = userInfo.nickname ?: user.nickname,
                    avatar = userInfo.avatarUrl ?: user.avatar,
                    isVip = userInfo.isVip,
                    vipExpireTime = userInfo.vipExpireTime
                )
            } catch (e: Exception) {
                Log.e(TAG, "Token验证失败", e)
                null
            }
        }
    }
}