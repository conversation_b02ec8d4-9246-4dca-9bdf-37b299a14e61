package com.lfb.android.footprint.Manager

import android.annotation.SuppressLint
import android.content.Context
import android.util.Log
import com.lfb.android.footprint.api.ApiService
import com.lfb.android.footprint.prefs.Preference
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

/**
 * 会员过期检查管理器
 * 负责定期检查会员状态，包括本地和联网检查
 */
class MembershipCheckManager private constructor(private val context: Context) {
    
    companion object {
        private const val TAG = "MembershipCheckManager"
        
        @SuppressLint("StaticFieldLeak")
        @Volatile
        private var INSTANCE: MembershipCheckManager? = null
        
        fun getInstance(context: Context): MembershipCheckManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: MembershipCheckManager(context.applicationContext).also { INSTANCE = it }
            }
        }
        
        // 检查间隔：3天
        private const val CHECK_INTERVAL_DAYS = 3
        private const val CHECK_INTERVAL_MS = CHECK_INTERVAL_DAYS * 24 * 60 * 60 * 1000L
        
        // 连续失败次数阈值
        private const val MAX_CONSECUTIVE_FAILURES = 3
    }
    
    // 持久化存储
    private var lastCheckTime by Preference(context, "membership_last_check_time", 0L)
    private var consecutiveFailures by Preference(context, "membership_consecutive_failures", 0)
    
    /**
     * 检查是否需要进行会员状态检查
     */
    fun shouldCheckMembership(): Boolean {
        val currentTime = System.currentTimeMillis()
        val timeSinceLastCheck = currentTime - lastCheckTime
        
        Log.d(TAG, "检查是否需要校验会员状态 - 上次检查: $lastCheckTime, 当前时间: $currentTime, 间隔: ${timeSinceLastCheck / 1000 / 60 / 60}小时")
        
        return timeSinceLastCheck >= CHECK_INTERVAL_MS
    }
    
    /**
     * 执行会员状态检查
     * @param onResult 检查结果回调 (success: Boolean, needRelogin: Boolean, message: String)
     * @param onVersionCheck 版本检查回调 (versionConfig: VersionConfig)
     */
    fun checkMembershipStatus(
        onResult: (success: Boolean, needRelogin: Boolean, message: String) -> Unit,
        onVersionCheck: ((VersionConfig) -> Unit)? = null
    ) {
        CoroutineScope(Dispatchers.IO).launch {
            try {
                val userManager = UserManager.getInstance(context)
                
                // 1. 检查是否已登录
                if (!userManager.isLoggedIn()) {
                    withContext(Dispatchers.Main) {
                        onResult(true, false, "用户未登录")
                    }
                    return@launch
                }
                
                val currentUser = userManager.getCurrentUserOrNull()
                if (currentUser == null) {
                    withContext(Dispatchers.Main) {
                        onResult(true, false, "用户信息不存在")
                    }
                    return@launch
                }

                if (!currentUser.isVip) {
                    withContext(Dispatchers.Main) {
                        onResult(true, false, "用户非会员")
                    }
                    return@launch
                }

                // 2. 本地检查：检查会员是否过期
                val localCheckResult = checkLocalMembershipExpiry(currentUser)
//                Log.d(TAG, "本地会员检查结果: isVip=${currentUser.isVip}, expired=${localCheckResult.isExpired}, message=${localCheckResult.message}")
                
                // 3. 联网检查：从服务器获取最新会员状态
                val remoteCheckResult = checkRemoteMembershipStatus(currentUser.bearerToken)
                
                if (remoteCheckResult.success) {
                    // 联网检查成功
//                    Log.d(TAG, "联网会员检查成功: isVip=${remoteCheckResult.isVip}, expireTime=${remoteCheckResult.vipExpireTime}, lastDeviceId=${remoteCheckResult.lastDeviceId}")
                    
                    // 3.1 检查版本更新
                    remoteCheckResult.versionConfig?.let { versionConfig ->
                        withContext(Dispatchers.Main) {
                            onVersionCheck?.invoke(versionConfig)
                        }
                    }
                    
                    // 4. 检查设备ID是否匹配
                    val currentDeviceId = com.lfb.android.footprint.utils.DeviceUtils.getDeviceId(context)
                    if (remoteCheckResult.lastDeviceId != null && 
                        remoteCheckResult.lastDeviceId.isNotEmpty() && 
                        remoteCheckResult.lastDeviceId != currentDeviceId) {
                        // 设备ID不匹配，用户在其他设备登录
                        Log.w(TAG, "设备ID不匹配: 当前=$currentDeviceId, 服务器=$remoteCheckResult.lastDeviceId")
                        withContext(Dispatchers.Main) {
                            com.lfb.android.footprint.Manager.DropdownAlertManager.showMessage("当前账号已在其他设备登录，请重新登录")
                        }

                        userManager.logout()
                        
                        withContext(Dispatchers.Main) {
                            onResult(false, true, "当前账号已在其他设备登录，请重新登录")
                        }
                        return@launch
                    }
                    
                    // 重置失败计数
                    consecutiveFailures = 0
                    
                    // 更新本地会员状态
                    userManager.updateVipStatus(remoteCheckResult.isVip, remoteCheckResult.vipExpireTime)

                    if (remoteCheckResult.isVip) {
                        // 是会员更新最后检查时间
                        lastCheckTime = System.currentTimeMillis()
                        
                        // 检查是否即将过期（3天内）
                        val currentTime = System.currentTimeMillis()
                        val expireTime = remoteCheckResult.vipExpireTime
                        if (expireTime > 0) {
                            val daysUntilExpiry = ((expireTime - currentTime) / (24 * 60 * 60 * 1000)).toInt()
                            Log.d(TAG, "会员过期检查: 当前时间=$currentTime, 过期时间=$expireTime, 剩余天数=$daysUntilExpiry")
                            
                            if (daysUntilExpiry == 0) {
                                withContext(Dispatchers.Main) {
                                    com.lfb.android.footprint.Manager.DropdownAlertManager.showMessage("您的会员将在今日过期")
                                }
                            } else if (daysUntilExpiry in 1..3) {
                                withContext(Dispatchers.Main) {
                                    com.lfb.android.footprint.Manager.DropdownAlertManager.showMessage(
                                        "您的会员将在${daysUntilExpiry}天后过期"
                                    )
                                }
                            } else {
                                Log.d(TAG, "会员状态正常，剩余${daysUntilExpiry}天，无需提醒")
                            }
                        } else {
                            Log.d(TAG, "会员过期时间无效: $expireTime")
                        }

                    } else {
                        lastCheckTime = 0
                        com.lfb.android.footprint.Manager.DropdownAlertManager.showMessage("您的会员已过期")
                    }
                    
                    withContext(Dispatchers.Main) {
                        onResult(true, false, "会员状态已更新")
                    }
                } else {
                    // 联网检查失败
                    consecutiveFailures++
                    Log.w(TAG, "联网会员检查失败 (${consecutiveFailures}/$MAX_CONSECUTIVE_FAILURES): ${remoteCheckResult.message}")
                    
                    if (consecutiveFailures >= MAX_CONSECUTIVE_FAILURES) {
                        // 连续失败3次，强制退出登录
                        Log.e(TAG, "连续${MAX_CONSECUTIVE_FAILURES}次检查失败，强制退出登录")
                        userManager.logout()

                        com.lfb.android.footprint.Manager.DropdownAlertManager.showMessage("登录信息已过期，请重新登录")

                        withContext(Dispatchers.Main) {
                            onResult(false, true, "登录信息已过期，请重新登录")
                        }
                    } else {
                        // 还未达到失败阈值，使用本地检查结果
                        withContext(Dispatchers.Main) {
                            onResult(true, false, "使用本地会员状态 (网络检查失败 ${consecutiveFailures}/$MAX_CONSECUTIVE_FAILURES)")
                        }
                    }
                }
                
            } catch (e: Exception) {
                Log.e(TAG, "会员状态检查异常", e)
                withContext(Dispatchers.Main) {
                    onResult(false, false, "检查异常: ${e.message}")
                }
            }
        }
    }
    
    /**
     * 本地检查会员是否过期
     */
    private fun checkLocalMembershipExpiry(user: com.lfb.android.footprint.model.UserModel): LocalCheckResult {
        if (!user.isVip) {
            return LocalCheckResult(
                isExpired = true,
                message = "非会员用户"
            )
        }
        
        val currentTime = System.currentTimeMillis()
        val isExpired = user.vipExpireTime > 0 && currentTime > user.vipExpireTime
        
        return LocalCheckResult(
            isExpired = isExpired,
            message = if (isExpired) "会员已过期" else "会员有效"
        )
    }
    
    /**
     * 联网检查会员状态
     */
    private suspend fun checkRemoteMembershipStatus(token: String): RemoteCheckResult {
        return try {
            val response = ApiService.getMemberStatus(token)
            
            val isVip = response.optBoolean("is_vip", false)
            val vipExpireTime = response.optLong("vip_expire_time", 0L)
            val subscription = response.optJSONObject("subscription")
            // 服务器返回用户最后登录的device_id
            val lastDeviceId = response.optString("last_device_id", null)
            
            // 解析版本配置
            val versionConfig = response.optJSONObject("version_config")?.let { config ->
                VersionConfig(
                    latestVersionCode = config.optInt("latest_version_code", 1),
                    minVersionCode = config.optInt("min_version_code", 1),
                    downloadUrl = config.optString("download_url", "")
                )
            }
            
            RemoteCheckResult(
                success = true,
                isVip = isVip,
                vipExpireTime = vipExpireTime,
                subscriptionInfo = subscription?.toString(),
                lastDeviceId = lastDeviceId,
                versionConfig = versionConfig,
                message = "检查成功"
            )
        } catch (e: Exception) {
            Log.e(TAG, "联网检查会员状态失败", e)
            RemoteCheckResult(
                success = false,
                isVip = false,
                vipExpireTime = 0L,
                subscriptionInfo = null,
                lastDeviceId = null,
                versionConfig = null,
                message = e.message ?: "网络请求失败"
            )
        }
    }
    
    /**
     * 重置失败计数（用于用户手动登录后）
     */
    fun resetFailureCount() {
        consecutiveFailures = 0
    }
    
    /**
     * 强制立即检查（忽略时间间隔）
     */
    fun forceCheck(
        onResult: (success: Boolean, needRelogin: Boolean, message: String) -> Unit,
        onVersionCheck: ((VersionConfig) -> Unit)? = null
    ) {
        lastCheckTime = 0L // 重置最后检查时间
        checkMembershipStatus(onResult, onVersionCheck)
    }

    /**
     * 本地检查结果
     */
    private data class LocalCheckResult(
        val isExpired: Boolean,
        val message: String
    )
    
    /**
     * 远程检查结果
     */
    private data class RemoteCheckResult(
        val success: Boolean,
        val isVip: Boolean,
        val vipExpireTime: Long,
        val subscriptionInfo: String?,
        val lastDeviceId: String?,
        val message: String,
        val versionConfig: VersionConfig? = null
    )
    
    /**
     * 版本配置
     */
    data class VersionConfig(
        val latestVersionCode: Int,
        val minVersionCode: Int,
        val downloadUrl: String
    )
}
