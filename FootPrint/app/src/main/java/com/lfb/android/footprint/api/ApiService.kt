package com.lfb.android.footprint.api

import android.annotation.SuppressLint
import android.util.Log
import org.json.JSONObject
import java.io.BufferedReader
import java.io.InputStreamReader
import java.io.OutputStreamWriter
import java.net.HttpURLConnection
import java.net.URL
import javax.net.ssl.HttpsURLConnection
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import com.lfb.android.footprint.model.SubscriptionOrder
import com.lfb.android.footprint.model.SubscriptionInfo
import android.content.Context
import com.lfb.android.footprint.Manager.UserManager
import com.lfb.android.footprint.model.LoginState

/**
 * Token过期异常
 */
class TokenExpiredException(message: String) : Exception(message)

/**
 * API服务类，用于与FootPrint服务端通信
 */
@SuppressLint("StaticFieldLeak")
object ApiService {

    private const val TAG = "ApiService"

    // 用于获取Context的变量
    private var context: Context? = null

    /**
     * 初始化ApiService
     */
    fun init(context: Context) {
        this.context = context.applicationContext
    }

    /**
     * 获取存储的JWT Token（用于用户身份验证）
     * 注意：这不是微信OAuth的accessToken，而是我们应用自己的JWT token
     */
    private fun getStoredToken(): String? {
        val ctx = context ?: return null
        val userManager = UserManager.getInstance(ctx)
        val loginState = userManager.loginState.value

        return when (loginState) {
            is LoginState.LoggedIn -> loginState.user.bearerToken
            else -> null
        }
    }

    /**
     * 获取存储的 Refresh Token
     */
    private fun getStoredRefreshToken(): String? {
        val ctx = context ?: return null
        val userManager = UserManager.getInstance(ctx)
        val loginState = userManager.loginState.value

        return when (loginState) {
            is LoginState.LoggedIn -> loginState.user.refreshToken
            else -> null
        }
    }

    // 服务端基础URL - 根据实际部署地址修改
    private const val BASE_URL = "http://192.168.31.197:8000"
    
    /**
     * 微信登录请求
     */
    data class WechatLoginRequest(
        val code: String,
        val deviceId: String,
        val appVersion: String
    )
    
    /**
     * 登录响应数据
     */
    data class LoginResponse(
        val bearerToken: String,
        val refreshToken: String,
        val tokenType: String,
        val expiresIn: Int,
        val refreshExpiresIn: Int,
        val user: UserInfo
    )
    
    /**
     * 用户信息
     */
    data class UserInfo(
        val id: String,
        val openid: String,
        val nickname: String?,
        val avatarUrl: String?,
        val isVip: Boolean = false,
        val vipExpireTime: Long = 0L
    )
    
    /**
     * 调用微信登录接口
     */
    fun wechatLogin(code: String, deviceId: String, appVersion: String): LoginResponse {

        val url = "$BASE_URL/auth/wechat/login"
        val requestBody = JSONObject().apply {
            put("code", code)
            put("device_id", deviceId)
            put("app_version", appVersion)
        }
        
        val response = makePostRequest(url, requestBody)
        
        // 解析响应
        val bearerToken = response.getString("bearerToken")
        val refreshToken = response.getString("refreshToken")
        val tokenType = response.getString("token_type")
        val expiresIn = response.getInt("expires_in")
        val refreshExpiresIn = response.getInt("refresh_expires_in")
        
        val userJson = response.getJSONObject("user")
        val user = UserInfo(
            id = userJson.getString("id"),
            openid = userJson.getString("openid"),
            nickname = userJson.optString("nickname"),
            avatarUrl = userJson.optString("avatar_url"),
            isVip = userJson.optBoolean("is_vip", false),
            vipExpireTime = userJson.optLong("vip_expire_time", 0L)
        )
        
        return LoginResponse(bearerToken, refreshToken, tokenType, expiresIn, refreshExpiresIn, user)
    }
    
    /**
     * 验证JWT Token
     */
    fun verifyToken(token: String): UserInfo {

        val url = "$BASE_URL/auth/verify"
        val response = makePostRequestWithAuth(url, JSONObject(), token)
        
        val userJson = response.getJSONObject("user")
        return UserInfo(
            id = userJson.getString("id"),
            openid = userJson.getString("openid"),
            nickname = userJson.optString("nickname"),
            avatarUrl = userJson.optString("avatar_url"),
            isVip = userJson.optBoolean("is_vip", false),
            vipExpireTime = userJson.optLong("vip_expire_time", 0L)
        )
    }

    /**
     * 刷新访问令牌
     * 使用 refresh token 获取新的 access token 和 refresh token
     */
    fun refreshToken(refreshToken: String): LoginResponse {
        val url = "$BASE_URL/auth/refresh"
        val requestBody = JSONObject().apply {
            put("refresh_token", refreshToken)
        }
        
        val response = makePostRequest(url, requestBody)
        
        // 解析响应（与登录响应格式相同）
        val bearerToken = response.getString("bearerToken")
        val newRefreshToken = response.getString("refreshToken")
        val tokenType = response.getString("token_type")
        val expiresIn = response.getInt("expires_in")
        val refreshExpiresIn = response.getInt("refresh_expires_in")
        
        val userJson = response.getJSONObject("user")
        val user = UserInfo(
            id = userJson.getString("id"),
            openid = userJson.getString("openid"),
            nickname = userJson.optString("nickname"),
            avatarUrl = userJson.optString("avatar_url"),
            isVip = userJson.optBoolean("is_vip", false),
            vipExpireTime = userJson.optLong("vip_expire_time", 0L)
        )

        return LoginResponse(bearerToken, newRefreshToken, tokenType, expiresIn, refreshExpiresIn, user)
    }
    
    /**
     * 获取用户订阅状态
     */
    fun getUserSubscription(token: String): JSONObject? {
        return try {
            val url = "$BASE_URL/billing/subscriptions"
            val response = makeGetRequestWithAuth(url, token)
            response.optJSONObject("subscription")
        } catch (e: Exception) {
            Log.w(TAG, "获取订阅状态失败: ${e.message}")
            null
        }
    }
    
    /**
     * 获取会员状态（用于定期检查）
     */
    fun getMemberStatus(token: String): JSONObject {
        val url = "$BASE_URL/billing/member/status"
        return makeGetRequestWithAuth(url, token)
    }
    
    /**
     * 发起POST请求
     */
    private fun makePostRequest(urlString: String, requestBody: JSONObject): JSONObject {
        return makeRequest(urlString, "POST", requestBody, null)
    }
    
    /**
     * 发起带认证的POST请求
     */
    private fun makePostRequestWithAuth(urlString: String, requestBody: JSONObject, token: String): JSONObject {
        return makeRequest(urlString, "POST", requestBody, token)
    }
    
    /**
     * 发起带认证的GET请求
     */
    private fun makeGetRequestWithAuth(urlString: String, token: String): JSONObject {
        return makeRequest(urlString, "GET", null, token)
    }
    
    /**
     * 通用HTTP请求方法
     */
    private fun makeRequest(
        urlString: String, 
        method: String, 
        requestBody: JSONObject?, 
        authToken: String?
    ): JSONObject {
        val url = URL(urlString)
        val connection = if (urlString.startsWith("https")) {
            url.openConnection() as HttpsURLConnection
        } else {
            url.openConnection() as HttpURLConnection
        }
        
        try {
            connection.requestMethod = method
            connection.connectTimeout = 15000
            connection.readTimeout = 15000
            connection.setRequestProperty("Content-Type", "application/json")
            connection.setRequestProperty("User-Agent", "FootPrint-Android")
            
            // 添加认证头
            if (authToken != null) {
                connection.setRequestProperty("Authorization", "Bearer $authToken")
            }
            
            // 发送请求体（如果有）
            if (requestBody != null && method != "GET") {
                connection.doOutput = true
                val writer = OutputStreamWriter(connection.outputStream, "UTF-8")
                writer.write(requestBody.toString())
                writer.flush()
                writer.close()
            }
            
            val responseCode = connection.responseCode
            if (responseCode in 200..299) {
                val reader = BufferedReader(InputStreamReader(connection.inputStream, "UTF-8"))
                val response = reader.readText()
                reader.close()

                return JSONObject(response)
            } else {
                // 读取错误响应
                val errorReader = BufferedReader(
                    InputStreamReader(connection.errorStream ?: connection.inputStream, "UTF-8")
                )
                val errorResponse = errorReader.readText()
                errorReader.close()
                
                Log.e(TAG, "HTTP请求失败，响应码: $responseCode, 错误信息: $errorResponse")
                
                // 特殊处理401未授权错误（JWT token过期）
                if (responseCode == 401) {
                    throw TokenExpiredException("Token已过期")
                }
                
                // 尝试解析错误响应
                try {
                    val errorJson = JSONObject(errorResponse)
                    val errorMessage = errorJson.optString("message", "请求失败")
                    throw Exception(errorMessage)
                } catch (e: Exception) {
                    throw Exception("HTTP请求失败，响应码: $responseCode")
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "HTTP请求异常", e)
            throw e
        } finally {
            connection.disconnect()
        }
    }

    /**
     * 带自动刷新token功能的请求方法
     * 当遇到401错误时，自动尝试刷新token并重试请求
     */
    private fun makeRequestWithAutoRefresh(
        urlString: String,
        method: String,
        requestBody: JSONObject?,
        authToken: String?
    ): JSONObject {
        try {
            // 首次尝试请求
            return makeRequest(urlString, method, requestBody, authToken)
        } catch (e: TokenExpiredException) {
            // Token过期，尝试自动刷新
            
            // 获取refresh token
            val refreshToken = getStoredRefreshToken()
            if (refreshToken.isNullOrEmpty()) {
                Log.e(TAG, "没有可用的refresh token，需要重新登录")
                // 清除登录状态
                context?.let { ctx ->
                    val userManager = UserManager.getInstance(ctx)
                    userManager.logout()
                }
                throw Exception("登录已过期，请重新登录")
            }
            
            try {
                // 刷新token
                val loginResponse = refreshToken(refreshToken)
                
                // 更新本地存储的token（包括新的refresh token）
                context?.let { ctx ->
                    val userManager = UserManager.getInstance(ctx)
                    userManager.updateTokens(
                        bearerToken = loginResponse.bearerToken,
                        refreshToken = loginResponse.refreshToken,
                        expiresIn = loginResponse.expiresIn,
                        refreshExpiresIn = loginResponse.refreshExpiresIn
                    )
                }
                
                // 使用新token重试请求
                // Token刷新成功，重试请求
                return makeRequest(urlString, method, requestBody, loginResponse.bearerToken)
            } catch (refreshError: Exception) {
                Log.e(TAG, "刷新token失败: ${refreshError.message}")
                // 刷新失败，清除登录状态
                context?.let { ctx ->
                    val userManager = UserManager.getInstance(ctx)
                    userManager.logout()
                }
                throw Exception("登录已过期，请重新登录")
            }
        }
    }

    /**
     * 创建订阅订单（获取预支付ID）
     * 注意：这里使用的是JWT token进行用户身份验证，不是微信OAuth的accessToken
     * 微信支付不需要用户的OAuth token，服务端会使用商户证书进行支付
     */
    @SuppressLint("StaticFieldLeak")
    suspend fun createSubscriptionOrder(planId: Int): Result<SubscriptionOrder> = withContext(Dispatchers.IO) {
        try {
            // 获取JWT token用于用户身份验证
            val jwtToken = getStoredToken()
            if (jwtToken.isNullOrEmpty()) {
                return@withContext Result.failure(Exception("用户未登录"))
            }

            val url = URL("${BASE_URL}/billing/subscription/order")
            val connection = url.openConnection() as HttpURLConnection

            try {
                connection.requestMethod = "POST"
                connection.setRequestProperty("Authorization", "Bearer $jwtToken")
                connection.setRequestProperty("Content-Type", "application/json")
                connection.doOutput = true
                connection.connectTimeout = 30000
                connection.readTimeout = 30000

                // 构建请求体
                val requestBody = JSONObject().apply {
                    put("plan_id", planId)
                }

                // 发送请求
                connection.outputStream.use { outputStream ->
                    outputStream.write(requestBody.toString().toByteArray())
                }

                val responseCode = connection.responseCode
                val responseBody = if (responseCode == HttpURLConnection.HTTP_CREATED) {
                    connection.inputStream.bufferedReader().use { it.readText() }
                } else {
                    connection.errorStream?.bufferedReader()?.use { it.readText() } ?: ""
                }

                if (responseCode == HttpURLConnection.HTTP_CREATED) {
                    val jsonResponse = JSONObject(responseBody)
                    val order = SubscriptionOrder(
                        orderId = jsonResponse.optInt("order_id", 0),
                        outTradeNo = jsonResponse.optString("out_trade_no", ""),
                        prepayId = jsonResponse.optString("prepay_id", ""),
                        planId = planId
                    )
                    Result.success(order)
                } else {
                    val errorMessage = try {
                        val errorJson = JSONObject(responseBody)
                        errorJson.optString("message", "创建订单失败")
                    } catch (e: Exception) {
                        "创建订单失败"
                    }
                    Result.failure(Exception(errorMessage))
                }
            } finally {
                connection.disconnect()
            }
        } catch (e: Exception) {
            Log.e(TAG, "Create subscription order failed", e)
            Result.failure(e)
        }
    }

    /**
     * 查询订单支付状态并更新会员信息
     * 使用JWT token进行用户身份验证
     */
    suspend fun queryOrderPaymentStatus(outTradeNo: String): Result<PaymentStatusResult> = withContext(Dispatchers.IO) {
        try {
            // 获取JWT token用于用户身份验证
            val jwtToken = getStoredToken()
            if (jwtToken.isNullOrEmpty()) {
                return@withContext Result.failure(Exception("用户未登录"))
            }

            val url = URL("${BASE_URL}/billing/orders/${outTradeNo}/status")
            val connection = url.openConnection() as HttpURLConnection

            try {
                connection.requestMethod = "GET"
                connection.setRequestProperty("Authorization", "Bearer $jwtToken")
                connection.connectTimeout = 30000
                connection.readTimeout = 30000

                val responseCode = connection.responseCode
                val responseBody = if (responseCode == HttpURLConnection.HTTP_OK) {
                    connection.inputStream.bufferedReader().use { it.readText() }
                } else {
                    connection.errorStream?.bufferedReader()?.use { it.readText() } ?: ""
                }

                if (responseCode == HttpURLConnection.HTTP_OK) {
                    val jsonResponse = JSONObject(responseBody)
                    
                    // 解析订单信息
                    val orderJson = jsonResponse.getJSONObject("order")
                    val paymentInfoJson = jsonResponse.getJSONObject("payment_info")
                    val subscriptionUpdated = jsonResponse.optBoolean("subscription_updated", false)
                    
                    val result = PaymentStatusResult(
                        orderStatus = orderJson.optString("status", ""),
                        tradeState = paymentInfoJson.optString("trade_state", ""),
                        tradeStateDesc = paymentInfoJson.optString("trade_state_desc", ""),
                        transactionId = paymentInfoJson.optString("transaction_id", ""),
                        successTime = paymentInfoJson.optString("success_time", ""),
                        subscriptionUpdated = subscriptionUpdated,
                        isPaid = orderJson.optString("status", "") == "PAID",
                        closed_at = orderJson.optString("closed_at", ""),
                    )
                    
                    Result.success(result)
                } else {
                    val errorMessage = try {
                        val errorJson = JSONObject(responseBody)
                        errorJson.optString("message", "查询支付状态失败")
                    } catch (e: Exception) {
                        "查询支付状态失败"
                    }
                    Result.failure(Exception(errorMessage))
                }
            } finally {
                connection.disconnect()
            }
        } catch (e: Exception) {
            Log.e(TAG, "Query payment status failed", e)
            Result.failure(e)
        }
    }

    /**
     * 支付状态查询结果
     */
    data class PaymentStatusResult(
        val orderStatus: String,
        val tradeState: String,
        val tradeStateDesc: String,
        val transactionId: String,
        val successTime: String,
        val subscriptionUpdated: Boolean,
        val isPaid: Boolean,
        val closed_at: String
    )
}
