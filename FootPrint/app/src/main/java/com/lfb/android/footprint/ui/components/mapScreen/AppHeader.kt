package com.lfb.android.footprint.ui.components.mapScreen

import android.content.Context
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.dp
import androidx.compose.ui.draw.clip
import androidx.compose.material3.Text
import androidx.compose.ui.draw.blur
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.zIndex
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.unit.sp
import com.google.android.material.dialog.MaterialAlertDialogBuilder
import com.lfb.android.footprint.R
import com.lfb.android.footprint.ui.theme.mainRedColor
import com.lfb.android.footprint.ui.theme.rememberMapThemeConfig
import com.lfb.android.footprint.prefs.AppPrefs

@Composable
fun AppHeader(
    gpsSignal: Int,
    runningModel: Int,
    modifier: Modifier = Modifier
) {
    val themeConfig = rememberMapThemeConfig()


    Box(modifier = modifier) {

        // 文字内容和背景
        Row(
            modifier = Modifier
                .padding(start = 18.dp)  // 让背景从logo中间开始
                .shadow(
                    elevation = 2.dp,
                    shape = RoundedCornerShape(8.dp),
                    spotColor = themeConfig.shadowColor
                )
                .background(
                    color = themeConfig.backgroundColor,
                    shape = RoundedCornerShape(8.dp)
                )
                .padding(6.dp)
                .padding(start = 10.dp, end = 22.dp),  // 给文字额外的左边距，让它在logo右边

            verticalAlignment = Alignment.CenterVertically
        ) {
            Column {
                Text(
                    text = "一生足迹 App",
                    style = TextStyle(
                        fontSize = 10.sp,
                        color = if (themeConfig.isDarkTheme) mainRedColor else mainRedColor
                    )
                )

                Spacer(modifier = Modifier.padding(top = 2.dp)) // 设置你想要的间距


                Text(
                    text = buildAnnotatedString {
                        if (runningModel == 0) {
                            append("普通模式")
                        } else if (runningModel == 1) {
                            append("耗电模式")
                        } else if (runningModel == 2) {
                            append("省电模式")
                        }

                        append(" GPS")
                        if (gpsSignal > 0 && gpsSignal < 1000) {
                            append("正常")
                        } else {
                            append("信号弱")
                        }

                        append(" - $gpsSignal")
                    },
                    style = TextStyle(
                        fontSize = 7.sp,
                        color = if (themeConfig.isDarkTheme) mainRedColor else mainRedColor
                    )
                )
            }
        }

        // Logo - 放在最后以确保显示在最上层
        Image(
            painter = painterResource(id = R.drawable.app_logo),
            contentDescription = "App Logo",
            modifier = Modifier
                .size(32.dp)
                .shadow(
                    elevation = 2.dp,
                    shape = RoundedCornerShape(8.dp)
                )
                .clip(RoundedCornerShape(8.dp))
                .align(Alignment.CenterStart)
                .zIndex(2f)  // 确保Logo显示在阴影之上，比背景阴影更高
        )

    }
}


fun showRecordModeDialog(
    context: Context,
    onSelect: (String) -> Unit
) {
    val modes = arrayOf("普通模式", "省电模式", "耗电模式")
    MaterialAlertDialogBuilder(context)
        .setTitle("切换记录模式")
        .setItems(modes) { dialog, which ->
            onSelect(modes[which])
            dialog.dismiss()
        }
        .show()
}