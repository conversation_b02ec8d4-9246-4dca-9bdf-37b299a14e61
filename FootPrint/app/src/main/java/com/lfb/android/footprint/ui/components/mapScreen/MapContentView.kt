package com.lfb.android.footprint.ui.components.mapScreen

import android.annotation.SuppressLint
import androidx.compose.animation.*
import androidx.compose.animation.core.*
import androidx.compose.animation.core.FastOutSlowInEasing
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.gestures.detectVerticalDragGestures
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.KeyboardArrowDown
import androidx.compose.material.icons.filled.KeyboardArrowUp
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import android.location.Location
import androidx.compose.foundation.clickable
import com.lfb.android.footprint.R
import com.lfb.android.footprint.location.LocationDataRecorder
import com.lfb.android.footprint.Manager.RealmModelManager
import com.lfb.android.footprint.model.StepDataRealmModel
import com.lfb.android.footprint.ui.theme.mainRedColor
import com.lfb.android.footprint.ui.theme.rememberMapThemeConfig
import kotlinx.coroutines.launch
import java.util.Calendar
import java.util.Date
import kotlin.math.roundToInt

// 面板状态枚举
enum class PanelState {
    EXPANDED,    // 完全展开状态（BottomControlBar隐藏）
    COLLAPSED    // 收缩状态（BottomControlBar显示）
}

/**
 * 地图内容视图容器 - 参考iOS MapContentView
 * 集成BottomControlBar，支持双状态手势交互
 */
@SuppressLint("UnusedBoxWithConstraintsScope")
@Composable
fun MapContentView(
    visible: Boolean,
    initialExpanded: Boolean,
    panelStateChanged: (PanelState) -> Unit,
    locationDataRecorder: LocationDataRecorder,
    onFilterSelected: (Int) -> Unit,
    onLeftButtonClick: () -> Unit,
    onRightButtonClick: () -> Unit,
    leftIconResId: Int,
    rightIconResId: Int,
    modifier: Modifier = Modifier
) {
    val themeConfig = rememberMapThemeConfig()
    // 面板状态管理 - 根据initialExpanded设置初始状态
    var panelState by remember { 
        mutableStateOf(if (initialExpanded) PanelState.EXPANDED else PanelState.COLLAPSED) 
    }
    var dragOffsetY by remember { mutableStateOf(0f) }
    val coroutineScope = rememberCoroutineScope()
    
    // 使用Animatable来控制状态偏移动画
    val stateOffsetAnimatable = remember { Animatable(0f) }
    
    // 存储收缩状态的目标偏移量
    var collapsedTargetOffsetPx by remember { mutableStateOf(0f) }
    
    // 计算BottomControlBar的高度
    val controlBarHeight = 120.dp
    val controlBarHeightPx = with(androidx.compose.ui.platform.LocalDensity.current) { controlBarHeight.toPx() }
    
    // 获取密度
    val density = androidx.compose.ui.platform.LocalDensity.current
    
    // 加载今日数据
    var hourlyDistance by remember { mutableStateOf(FloatArray(24) { 0f }) }
    var hourlyAltitude by remember { mutableStateOf(FloatArray(24) { 0f }) }
    var totalDistance by remember { mutableStateOf(0.0) }
    var totalPoints by remember { mutableStateOf(0) }
    
    LaunchedEffect(visible) {
        if (visible) {
            // 从 LocationDataRecorder 获取今日数据
            val todayLocations = locationDataRecorder.getTodayLocations()
            
            // iOS: int hourSteps[24], double hourDis[24]
            val hourDist = FloatArray(24) { 0f }
            
            // iOS: 获取当前小时
            val nowHour = Calendar.getInstance().get(Calendar.HOUR_OF_DAY)
            
            // iOS: 获取默认海拔（第一个数据点的海拔）
            val defaultAltitude = if (todayLocations.isNotEmpty()) {
                todayLocations.first().altitude.toFloat()
            } else {
                0f
            }
            
            // iOS: 创建海拔数据字典
            val altitudeDataDic = mutableMapOf<Int, Float>()
            for (location in todayLocations) {
                val hour = getHourFromTimestamp(location.time / 1000)
                if (hour in 0..23) {
                    // 只保存每小时的海拔值（iOS会覆盖，这里取最后一个）
                    altitudeDataDic[hour] = location.altitude.toFloat()
                }
            }
            
            // iOS: 遍历今日数据计算距离
            todayLocations.forEachIndexed { index, location ->
                if (index > 0) {
                    val hour = getHourFromTimestamp(location.time / 1000)
                    if (hour in 0..23) {
                        val prevLocation = todayLocations[index - 1]
                        val distance = prevLocation.distanceTo(location)
                        // iOS: if ([DataHelper canAddDistence:each dataRealmArray:todayAllSteps])
                        if (distance < 1000) {
                            hourDist[hour] += distance
                        }
                    }
                }
            }
            
            // iOS: 填充海拔数据数组
            val hourAlt = FloatArray(24) { 0f }
            var altitudeData = defaultAltitude
            for (hour in 0..23) {
                if (hour <= nowHour) {
                    // iOS: 当前时间之前，使用字典中的值或保持上一个值
                    val altitudeNum = altitudeDataDic[hour]
                    if (altitudeNum != null) {
                        altitudeData = altitudeNum
                    }
                } else {
                    // iOS: 当前时间之后，设为0
                    altitudeData = 0f
                }
                hourAlt[hour] = altitudeData
            }
            
            // 计算总距离和总点数
            var totalDist = 0.0
            todayLocations.forEachIndexed { index, location ->
                if (index > 0) {
                    val prevLocation = todayLocations[index - 1]
                    val distance = prevLocation.distanceTo(location)
                    if (distance < 1000) {
                        totalDist += distance
                    }
                }
            }
            
            hourlyDistance = hourDist
            hourlyAltitude = hourAlt
            totalDistance = totalDist
            totalPoints = todayLocations.size
        }
    }
    
    AnimatedVisibility(
        visible = visible,
        enter = fadeIn(animationSpec = tween(durationMillis = 300)),
        exit = fadeOut(animationSpec = tween(durationMillis = 300))
    ) {
        BoxWithConstraints(
            modifier = modifier.fillMaxSize()
        ) {
            // 获取容器的实际高度
            val containerHeightPx = with(density) { maxHeight.toPx() }
            val controlBarHeightPx = with(density) { controlBarHeight.toPx() }

            // 计算收缩状态下的目标偏移（向下偏移，使得只露出BottomControlBar）
            collapsedTargetOffsetPx = containerHeightPx - controlBarHeightPx

            // 当前状态的目标偏移
            val targetOffsetPx = when (panelState) {
                PanelState.EXPANDED -> 0f
                PanelState.COLLAPSED -> collapsedTargetOffsetPx
            }

            // 监听状态变化，启动动画
            LaunchedEffect(panelState, targetOffsetPx) {
                println("MapContentView - 状态切换动画: panelState=$panelState, targetOffsetPx=$targetOffsetPx, currentValue=${stateOffsetAnimatable.value}")
                stateOffsetAnimatable.animateTo(
                    targetValue = targetOffsetPx,
                    animationSpec = tween(
                        durationMillis = 200,
                        easing = FastOutSlowInEasing
                    )
                )
            }

            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .offset {
                        val finalOffset = (stateOffsetAnimatable.value + dragOffsetY).roundToInt()
                        println("MapContentView offset - panelState: $panelState, dragOffsetY: $dragOffsetY, stateOffset: ${stateOffsetAnimatable.value}, finalOffset: $finalOffset")
                        IntOffset(0, finalOffset)
                    }
                    .pointerInput(Unit) {
                    detectVerticalDragGestures(
                        onDragEnd = {
                            println("MapContentView onDragEnd - panelState: $panelState, dragOffsetY: $dragOffsetY")
                            coroutineScope.launch {
                                when (panelState) {
                                    PanelState.EXPANDED -> {
                                        if (dragOffsetY > 100) {
                                            println("MapContentView - 切换到收缩状态 (dragOffsetY: $dragOffsetY > 100)")
                                            // 下拉超过阈值，切换到收缩状态
                                            // 从当前位置（dragOffsetY）动画到目标位置（collapsedTargetOffsetPx）
                                            val currentPosition = dragOffsetY
                                            dragOffsetY = 0f
                                            panelState = PanelState.COLLAPSED
                                            // 启动动画，从当前拖拽位置开始
                                            stateOffsetAnimatable.snapTo(currentPosition)
                                            panelStateChanged(panelState)
                                        } else {
                                            println("MapContentView - 保持展开状态 (dragOffsetY: $dragOffsetY <= 100)")
                                            // 直接重置
                                            dragOffsetY = 0f
                                        }
                                    }
                                    PanelState.COLLAPSED -> {
                                        if (dragOffsetY < -100) {
                                            println("MapContentView - 切换到展开状态 (dragOffsetY: $dragOffsetY < -100)")
                                            // 上拉超过阈值，切换到展开状态
                                            // 当前实际位置 = collapsedTargetOffsetPx + dragOffsetY
                                            // 需要从这个位置动画到0
                                            val currentActualPosition = collapsedTargetOffsetPx + dragOffsetY
                                            println("MapContentView - currentActualPosition: $currentActualPosition, collapsedTargetOffsetPx: $collapsedTargetOffsetPx, dragOffsetY: $dragOffsetY")
                                            dragOffsetY = 0f
                                            panelState = PanelState.EXPANDED
                                            // 启动动画，从当前实际位置开始
                                            stateOffsetAnimatable.snapTo(currentActualPosition)
                                            panelStateChanged(panelState)
                                        } else {
                                            println("MapContentView - 保持收缩状态 (dragOffsetY: $dragOffsetY >= -100)")
                                            // 直接重置
                                            dragOffsetY = 0f
                                        }
                                    }
                                }
                            }
                        },
                        onVerticalDrag = { _, dragAmount ->
                            when (panelState) {
                                PanelState.EXPANDED -> {
                                    // 展开状态只允许向下拖拽
                                    if (dragAmount > 0 || dragOffsetY > 0) {
                                        dragOffsetY = (dragOffsetY + dragAmount).coerceAtLeast(0f)
                                    }
                                }
                                PanelState.COLLAPSED -> {
                                    // 收缩状态允许上下拖拽
                                    // 向上拖拽时，最多可以拖到展开状态的位置（-collapsedTargetOffsetPx）
                                    // 向下拖拽时，限制在合理范围内
                                    dragOffsetY = (dragOffsetY + dragAmount).coerceIn(-collapsedTargetOffsetPx, 300f)
                                }
                            }
                        }
                    )
                    }

        ) {
            // 顶部半透明黑色背景
            Box(
                modifier = Modifier
                    .fillMaxSize()
//                    .background(Color.Black.copy(alpha = 0.4f))
            )
            
            // 统一的布局结构
            Column(modifier = Modifier.fillMaxSize()) {
                // 顶部BottomControlBar - 只在收缩状态显示，根据拖拽进度调整透明度
                if (panelState == PanelState.COLLAPSED) {
                    // 计算透明度：
                    // 屏幕2/3高度对应的偏移量 = collapsedTargetOffsetPx * 2/3
                    // 当前实际位置 = stateOffsetAnimatable.value + dragOffsetY
                    // 当实际位置从 collapsedTargetOffsetPx（底部）到 collapsedTargetOffsetPx/3（屏幕2/3高度）时，alpha从1到0
                    val controlBarAlpha = if (collapsedTargetOffsetPx > 0) {
                        val currentActualPosition = stateOffsetAnimatable.value + dragOffsetY
                        val twoThirdsPosition = collapsedTargetOffsetPx / 3f
                        val bottomPosition = collapsedTargetOffsetPx

                        // 计算从底部到2/3高度的进度
                        // currentActualPosition: bottomPosition -> twoThirdsPosition
                        // alpha: 1 -> 0
                        val progress = (bottomPosition - currentActualPosition) / (bottomPosition - twoThirdsPosition)
                        (1f - progress).coerceIn(0f, 1f)
                    } else {
                        1f
                    }
                    println("MapContentView - dragOffsetY: $dragOffsetY, stateOffset: ${stateOffsetAnimatable.value}, actualPos: ${stateOffsetAnimatable.value + dragOffsetY}, alpha: $controlBarAlpha")

                    IntegratedBottomControlBar(
                        onFilterSelected = onFilterSelected,
                        onLeftButtonClick = onLeftButtonClick,
                        onRightButtonClick = onRightButtonClick,
                        leftIconResId = leftIconResId,
                        rightIconResId = rightIconResId,
                        onExpandClick = {
                            // 点击横线时切换到展开状态
                            panelState = PanelState.EXPANDED
                            panelStateChanged(panelState)
                        },
                        modifier = Modifier
                            .fillMaxWidth()
                            .alpha(controlBarAlpha)
                    )
                }
                
                Spacer(modifier = Modifier.weight(1f))
                
                // 底部半屏容器（包含渐变遮罩）
                Box(modifier = Modifier.fillMaxWidth()) {
                    // 渐变遮罩层
                    Box(
                        modifier = Modifier
                            .matchParentSize()
                            .background(
                                brush = Brush.verticalGradient(
                                    colors = listOf(
                                        Color.Transparent,
                                        themeConfig.backgroundColor.copy(alpha = 0.3f),
                                        themeConfig.backgroundColor.copy(alpha = 0.7f),
                                        themeConfig.backgroundColor.copy(alpha = 0.98f)
                                    )
                                )
                        )
                    )

                    Column {
                        // 底部今日信息卡片
                        MapContentTodayInfoView(
                            hourlyDistance = hourlyDistance,
                            hourlyAltitude = hourlyAltitude,
                            totalDistance = totalDistance,
                            totalPoints = totalPoints,
                            modifier = Modifier.fillMaxWidth()
                        )

                        // 手势提示
                        GestureHintView(
                            panelState = panelState,
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(bottom = 24.dp)
                        )
                    }
                }
            }
        }
        }
    }
}

/**
 * 集成的BottomControlBar - 放在MapContentView顶部
 */
@Composable
private fun IntegratedBottomControlBar(
    onFilterSelected: (Int) -> Unit,
    onLeftButtonClick: () -> Unit,
    onRightButtonClick: () -> Unit,
    leftIconResId: Int,
    rightIconResId: Int,
    onExpandClick: () -> Unit = {},
    modifier: Modifier = Modifier
) {
    val themeConfig = rememberMapThemeConfig()
    
    Column(
        modifier = modifier
            .fillMaxWidth()
            .shadow(
                elevation = 4.dp,
                shape = RoundedCornerShape(topStart = 16.dp, topEnd = 16.dp)
            )
            .clip(RoundedCornerShape(topStart = 16.dp, topEnd = 16.dp))
            .background(
                color = themeConfig.backgroundColor,
                shape = RoundedCornerShape(topStart = 16.dp, topEnd = 16.dp)
            ) ,
    ) {
        // 顶部横线指示器 - 可点击展开
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(12.dp)
                .offset(y = 12.dp)
//                .background(Color.Gray)
                .clickable(
                    interactionSource = remember { androidx.compose.foundation.interaction.MutableInteractionSource() },
                    indication = null
                ) { onExpandClick() },
            contentAlignment = Alignment.Center
        ) {
            Box(
                modifier = Modifier
                    .width(24.dp)
                    .height(4.dp)
                    .clip(RoundedCornerShape(2.dp))
                    .background(themeConfig.blackColor)
            )
        }
        
        // 原有的控制栏内容
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(120.dp)
//                .background(Color.LightGray)
        ) {
            // TimePickerView in the center
            TimePickerView(
            modifier = Modifier
                .align(Alignment.Center)
                .offset(x = 4.dp)
                .offset(y = -12.dp)
//                .background(Color.Red)
                .width(180.dp),
            onItemSelected = onFilterSelected
        )

        // Left Button
        ControlButton(
            onClick = onLeftButtonClick,
            iconResId = leftIconResId,
            contentDescription = "Left Button",
            modifier = Modifier
                .align(Alignment.CenterStart)
                .padding(start = 16.dp)
                .offset(y = -12.dp)
        )

        // Right Button
        ControlButton(
            onClick = onRightButtonClick,
            iconResId = rightIconResId,
            contentDescription = "Right Button",
            modifier = Modifier
                .align(Alignment.CenterEnd)
                .padding(end = 16.dp)
                .offset(y = -12.dp)
        )

        }
    }
}

/**
 * 控制按钮组件
 */
@Composable
private fun ControlButton(
    onClick: () -> Unit,
    iconResId: Int,
    contentDescription: String,
    modifier: Modifier = Modifier
) {
    var isPressed by remember { mutableStateOf(false) }

    Box(
        modifier = modifier
            .size(44.dp)
            .pointerInput(Unit) {
                detectTapGestures(
                    onPress = {
                        isPressed = true
                        tryAwaitRelease()
                        isPressed = false
                    },
                    onTap = { onClick() }
                )
            },
        contentAlignment = Alignment.Center
    ) {
        Image(
            painter = painterResource(id = iconResId),
            contentDescription = contentDescription,
            modifier = Modifier
                .size(40.dp)
                .then(if (isPressed) Modifier else Modifier),
            colorFilter = ColorFilter.tint(
                if (isPressed) mainRedColor.copy(alpha = 0.5f) else mainRedColor
            )
        )
    }
}

/**
 * 手势提示视图
 */
@Composable
private fun GestureHintView(
    panelState: PanelState,
    modifier: Modifier = Modifier
) {
    val themeConfig = rememberMapThemeConfig()

    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = modifier
    ) {
        val (icon, text) = when (panelState) {
            PanelState.EXPANDED -> {
                Icons.Default.KeyboardArrowDown to "下拉显示时间选择器"
            }
            PanelState.COLLAPSED -> {
                Icons.Default.KeyboardArrowUp to "上拉查看完整内容"
            }
        }
        
        Icon(
            imageVector = icon,
            contentDescription = text,
            tint = themeConfig.normalTextColor.copy(alpha = 0.5f),
            modifier = Modifier.size(26.dp)
        )

        Text(
            text = text,
            style = TextStyle(
                color = themeConfig.normalTextColor.copy(alpha = 0.5f),
                fontSize = 8.sp
            )
        )
    }
}

/**
 * 今日信息视图 - 参考iOS MapContentTodayInfoView
 */
@Composable
private fun MapContentTodayInfoView(
    hourlyDistance: FloatArray,
    hourlyAltitude: FloatArray,
    totalDistance: Double,
    totalPoints: Int,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier
            .padding(top = 12.dp, bottom = 12.dp)
    ) {
        // 24小时折线图
        HourlyChartView(
            distanceData = hourlyDistance,
            altitudeData = hourlyAltitude,
            modifier = Modifier
                .fillMaxWidth()
                .height(100.dp)
                .padding(horizontal = 12.dp)
        )
        
        Spacer(modifier = Modifier.height(12.dp))
        
        // 今日数据统计
        TodayDataStatsView(
            distance = totalDistance,
            points = totalPoints,
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 12.dp)
        )
    }
}

private fun getHourFromTimestamp(timestamp: Long): Int {
    val calendar = java.util.Calendar.getInstance()
    calendar.timeInMillis = timestamp * 1000
    return calendar.get(java.util.Calendar.HOUR_OF_DAY)
}

private fun getTodayStartTimestamp(timeInMillis: Long): Long {
    val calendar = Calendar.getInstance()
    calendar.timeInMillis = timeInMillis
    calendar.set(Calendar.HOUR_OF_DAY, 0)
    calendar.set(Calendar.MINUTE, 0)
    calendar.set(Calendar.SECOND, 0)
    calendar.set(Calendar.MILLISECOND, 0)
    return calendar.timeInMillis / 1000
}
