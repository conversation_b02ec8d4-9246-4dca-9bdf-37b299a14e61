package com.lfb.android.footprint.prefs

// prefs/AppPrefs.kt
import android.content.Context
import android.location.Location
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import kotlinx.coroutines.flow.Flow
import prefs.PreferenceFlow
import java.util.Date

/**
 *  单例写法 —— 与 PAPreferences.sharedInstance 对应
 */
class AppPrefs private constructor(context: Context) {

    companion object {
        @Volatile private var INSTANCE: AppPrefs? = null

        fun init(context: Context) {
            if (INSTANCE == null) {
                synchronized(this) {
                    if (INSTANCE == null) INSTANCE = AppPrefs(context)
                }
            }
        }

        /** 拿不到的话说明忘了先在 Application.init() 里调用 init() */
        val sharedInstance: AppPrefs get() = INSTANCE ?: error("AppPrefs not initialized")
    }

    /* -------------------- 定义属性 ↓ -------------------- */
    var hasShowGuidPage by Preference(context, "hasShowGuid", false) // 是否展示过引导页

    var runningModel by Preference(context, "runningModel", 0) // 0:普通模式、1:耗电模式、2:省电模式

    var lastLocationlatitude by Preference(context, "lastLocationlatitude", 39.9163247)      // 上一次的定位
    var lastLocationlongitude by Preference(context, "lastLocationlongitude", 116.3905797)      // 上一次的定位

    var lastLocationTime by Preference(context, "lastLocationTime", 0)      // 上一次的定位时间

    var mapShowAdressName by Preference(context, "mapShowAdressName", false)                            // 地图显示地名
    var mapDisplayType by Preference(context, "mapDisplayType", 0)                            // 地图显示样式
    var mapDrawLineWidth by Preference(context, "mapDrawLineWidth", 2)                  // 轨迹线条宽度
    var mapDrawLineColor by Preference(context, "mapDrawLineColor", "0xFFFF0000")       // 轨迹颜色

    var mapDrawLineAlpha by Preference(context, "mapDrawLineAlpha", 0.8)                // 轨迹线条透明度
    var mapDrawSpotAlpha by Preference(context, "mapDrawSpotAlpha", 0.8)                // 轨迹点透明度
    var mapDrawLineDistanceFilter by Preference(context, "mapDrawLineDistanceFilter", 30000)     // 轨迹连线距离

    var dataCacheTotalTrackPoints by Preference(context, "dataCacheTotalTrackPoints", 0L)     // 数据缓存 轨迹总数量
    var dataCacheTotalDistance by Preference(context, "dataCacheTotalDistance", 0.0)         // 数据缓存 轨迹总距离
    var dataCacheTotalDays by Preference(context, "dataCacheTotalDays", 0L)                  // 数据缓存 总天数

    var lastCityDataParsedTime by Preference(context, "lastCityDataParsedTime", 0L)                  // 数据缓存 总天数

    // 用户相关偏好设置
    var userId by Preference(context, "user_id", "")
    var userNickname by Preference(context, "user_nickname", "")
    var userAvatar by Preference(context, "user_avatar", "")
    var userIsVip by Preference(context, "user_is_vip", false)
    var userVipExpireTime by Preference(context, "user_vip_expire_time", 0L)
    var userLoginTime by Preference(context, "user_login_time", 0L)

    // 版本更新相关
    var ignoredVersionCode by Preference(context, "ignored_version_code", 0)  // 被忽略的版本号

    fun restCachDatas() {
        lastCityDataParsedTime = 0
    }
}