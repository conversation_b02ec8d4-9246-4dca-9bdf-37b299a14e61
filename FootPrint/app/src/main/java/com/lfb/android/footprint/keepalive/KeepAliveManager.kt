package com.lfb.android.footprint.keepalive

import android.content.Context
import android.content.Intent
import android.os.Build
import com.lfb.android.footprint.service.LocationService
import com.lfb.android.footprint.Manager.LocalLogManager
import com.lfb.android.footprint.geofence.GeofenceInitializer
import com.lfb.android.footprint.utils.DozeOptimizationHelper
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

/**
 * 综合保活管理器
 * 统一管理应用的所有保活机制
 */
class KeepAliveManager private constructor(private val context: Context) {
    
    companion object {
        @Volatile
        private var INSTANCE: KeepAliveManager? = null
        
        fun getInstance(context: Context): KeepAliveManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: KeepAliveManager(context.applicationContext).also { INSTANCE = it }
            }
        }
    }
    
    private val localLogManager by lazy { LocalLogManager.getInstance(context) }
    private val batteryOptimizationManager by lazy { BatteryOptimizationManager(context) }
    private val autoStartManager by lazy { AutoStartManager(context) }
    private val dozeOptimizationHelper by lazy { DozeOptimizationHelper(context) }
    
    /**
     * 初始化所有保活机制
     */
    fun initializeKeepAlive(): Boolean {
        localLogManager.logSync("KeepAliveManager: 开始初始化保活机制")

        var success = true

        // 1. 启动LocationService
        if (!startLocationService()) {
            success = false
        }

        // 2. 初始化地理围栏系统
        if (!GeofenceInitializer.initialize(context)) {
            localLogManager.logSync("KeepAliveManager: 地理围栏初始化失败")
            // 地理围栏失败不算致命错误，继续执行其他保活机制
        }

        // 3. 检查并引导用户设置保活权限
        CoroutineScope(Dispatchers.IO).launch {
            checkAndSuggestKeepAliveSettings()
        }
        
        localLogManager.logSync("KeepAliveManager: 保活机制初始化${if (success) "成功" else "部分失败"}")
        return success
    }
    
    /**
     * 启动LocationService
     */
    private fun startLocationService(): Boolean {
        return try {
            // 检查是否有必要的权限
            if (!hasRequiredPermissions()) {
                localLogManager.logSync("KeepAliveManager: 缺少必要权限，跳过LocationService启动")
                return false
            }
            
            val intent = Intent(context, LocationService::class.java).apply {
                action = "KEEPALIVE_MANAGER_START"
            }
            
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                context.startForegroundService(intent)
                localLogManager.logSync("KeepAliveManager: 前台LocationService启动命令已发送")
            } else {
                context.startService(intent)
                localLogManager.logSync("KeepAliveManager: LocationService启动命令已发送")
            }
            true
        } catch (e: Exception) {
            localLogManager.logSync("KeepAliveManager: 启动LocationService失败 - ${e.message}")
            false
        }
    }

    /**
     * 检查是否有启动前台服务所需的权限
     */
    private fun hasRequiredPermissions(): Boolean {
        val fineLocationGranted = context.checkSelfPermission(android.Manifest.permission.ACCESS_FINE_LOCATION) == 
            android.content.pm.PackageManager.PERMISSION_GRANTED
        val coarseLocationGranted = context.checkSelfPermission(android.Manifest.permission.ACCESS_COARSE_LOCATION) == 
            android.content.pm.PackageManager.PERMISSION_GRANTED
        
        return fineLocationGranted || coarseLocationGranted
    }
    
    /**
     * 检查并建议保活设置
     */
    private fun checkAndSuggestKeepAliveSettings() {
        localLogManager.logSync("KeepAliveManager: 开始检查保活设置")
        
        // 检查电池优化白名单
        if (batteryOptimizationManager.needsBatteryOptimizationSetting()) {
            localLogManager.logSync("KeepAliveManager: 建议用户设置电池优化白名单")
        }
        
        // 检查自启动权限
        if (autoStartManager.isSupportedDevice()) {
            localLogManager.logSync("KeepAliveManager: 建议用户设置自启动权限")
        }
        
        localLogManager.logSync("KeepAliveManager: 保活设置检查完成")
    }
    
    /**
     * 获取保活状态
     */
    fun getKeepAliveStatus(): KeepAliveStatus {
        return KeepAliveStatus(
            isLocationServiceRunning = isLocationServiceRunning(),
            isGeofenceActive = GeofenceInitializer.isRunning(context),
            isBatteryOptimizationIgnored = batteryOptimizationManager.isIgnoringBatteryOptimizations(),
            batteryOptimizationDescription = batteryOptimizationManager.getBatteryOptimizationStatusDescription(),
            autoStartSuggestion = autoStartManager.getAutoStartSuggestion(),
            deviceManufacturer = autoStartManager.getDeviceManufacturer(),
            isSupportedDevice = autoStartManager.isSupportedDevice(),
            geofenceStatus = GeofenceInitializer.getStatus(context)
        )
    }
    
    /**
     * 检查LocationService是否运行
     */
    private fun isLocationServiceRunning(): Boolean {
        return try {
            val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as android.app.ActivityManager
            val runningServices = activityManager.getRunningServices(Integer.MAX_VALUE)
            
            runningServices.any { serviceInfo ->
                serviceInfo.service.className == LocationService::class.java.name
            }
        } catch (e: Exception) {
            localLogManager.logSync("KeepAliveManager: 检查LocationService状态失败 - ${e.message}")
            false
        }
    }
    
    /**
     * 引导用户设置电池优化白名单
     */
    fun guideBatteryOptimization(): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            batteryOptimizationManager.requestIgnoreBatteryOptimizations()
        } else {
            localLogManager.logSync("KeepAliveManager: 系统版本不支持电池优化设置")
            false
        }
    }
    
    /**
     * 引导用户设置自启动权限
     */
    fun guideAutoStartPermission(): Boolean {
        return autoStartManager.guideUserToAutoStartSettings()
    }
    
    /**
     * 获取需要设置的保活项目列表
     */
    fun getRequiredKeepAliveActions(): List<KeepAliveAction> {
        val actions = mutableListOf<KeepAliveAction>()
        
        // 电池优化白名单
        if (batteryOptimizationManager.needsBatteryOptimizationSetting()) {
            actions.add(
                KeepAliveAction(
                    type = KeepAliveActionType.BATTERY_OPTIMIZATION,
                    title = "电池优化白名单",
                    description = "将应用添加到电池优化白名单，防止系统清理后台运行",
                    suggestion = batteryOptimizationManager.getBatteryOptimizationSuggestion(),
                    isRequired = true,
                    priority = 1
                )
            )
        }
        
        // 自启动权限
        if (autoStartManager.isSupportedDevice()) {
            actions.add(
                KeepAliveAction(
                    type = KeepAliveActionType.AUTO_START,
                    title = "自启动权限",
                    description = "开启应用自启动权限，确保应用可以在后台自动运行",
                    suggestion = autoStartManager.getAutoStartSuggestion(),
                    isRequired = true,
                    priority = 2
                )
            )
        }
        
        // 检查LocationService状态
        if (!isLocationServiceRunning()) {
            actions.add(
                KeepAliveAction(
                    type = KeepAliveActionType.LOCATION_SERVICE,
                    title = "定位服务",
                    description = "定位服务未运行，点击重新启动",
                    suggestion = "定位服务是应用的核心功能，请确保服务正常运行",
                    isRequired = true,
                    priority = 0
                )
            )
        }
        
        // 检查地理围栏状态
        if (!GeofenceInitializer.isRunning(context)) {
            actions.add(
                KeepAliveAction(
                    type = KeepAliveActionType.GEOFENCE,
                    title = "地理围栏",
                    description = "地理围栏未激活，点击重新初始化",
                    suggestion = "地理围栏可以在应用被杀死后重新唤醒应用",
                    isRequired = false,
                    priority = 3
                )
            )
        }
        
        return actions.sortedBy { it.priority }
    }
    
    /**
     * 执行保活动作
     */
    fun performKeepAliveAction(action: KeepAliveAction): Boolean {
        return when (action.type) {
            KeepAliveActionType.BATTERY_OPTIMIZATION -> {
                guideBatteryOptimization()
            }
            KeepAliveActionType.AUTO_START -> {
                guideAutoStartPermission()
            }
            KeepAliveActionType.LOCATION_SERVICE -> {
                startLocationService()
            }
            KeepAliveActionType.GEOFENCE -> {
                GeofenceInitializer.initialize(context)
            }
        }
    }
    
    /**
     * 获取保活状态摘要
     */
    fun getKeepAliveStatusSummary(): String {
        val status = getKeepAliveStatus()

        return buildString {
            append("=== 保活状态摘要 ===\n")
            append("LocationService: ${if (status.isLocationServiceRunning) "运行中" else "未运行"}\n")
            append("地理围栏: ${if (status.isGeofenceActive) "已激活" else "未激活"}\n")
            append("电池优化: ${if (status.isBatteryOptimizationIgnored) "已忽略" else "未设置"}\n")
            append("设备厂商: ${status.deviceManufacturer}\n")
            append("是否支持自启动设置: ${if (status.isSupportedDevice) "是" else "否"}\n")
            append("\n")
            append(dozeOptimizationHelper.getPowerManagementStatusSummary())
        }
    }
    
    /**
     * 获取 Doze 模式优化建议
     */
    fun getDozeOptimizationSuggestions(): List<String> {
        return dozeOptimizationHelper.getOptimizationSuggestions()
    }
    
    /**
     * 检查并处理 Doze 模式相关设置
     */
    fun handleDozeOptimization(): Boolean {
        localLogManager.logSync("KeepAliveManager: 开始处理Doze模式优化")
        
        var success = true
        
        // 检查电池优化白名单
        if (dozeOptimizationHelper.shouldShowBatteryOptimizationPrompt()) {
            localLogManager.logSync("KeepAliveManager: 需要设置电池优化白名单")
            if (!dozeOptimizationHelper.requestIgnoreBatteryOptimizations()) {
                success = false
            }
        }
        
        // 记录当前 Doze 状态
        val isInDozeMode = dozeOptimizationHelper.isDeviceInDozeMode()
        localLogManager.logSync("KeepAliveManager: 当前Doze模式状态: $isInDozeMode")
        
        if (isInDozeMode) {
            localLogManager.logSync("KeepAliveManager: 设备处于Doze模式，定位服务可能受限")
            // 可以在这里触发特殊的处理逻辑
        }
        
        return success
    }
    
    /**
     * 重新初始化保活机制
     */
    fun reinitializeKeepAlive(): Boolean {
        localLogManager.logSync("KeepAliveManager: 重新初始化保活机制")

        // 重置地理围栏
        GeofenceInitializer.reset(context)

        // 重新初始化
        return initializeKeepAlive()
    }
    
    /**
     * 停止所有保活机制
     */
    fun stopKeepAlive() {
        localLogManager.logSync("KeepAliveManager: 停止所有保活机制")

        // 停止LocationService
        try {
            val intent = Intent(context, LocationService::class.java)
            context.stopService(intent)
        } catch (e: Exception) {
            localLogManager.logSync("KeepAliveManager: 停止LocationService失败 - ${e.message}")
        }

        // 重置地理围栏
        GeofenceInitializer.reset(context)
    }
}

/**
 * 保活状态数据类
 */
data class KeepAliveStatus(
    val isLocationServiceRunning: Boolean,
    val isGeofenceActive: Boolean,
    val isBatteryOptimizationIgnored: Boolean,
    val batteryOptimizationDescription: String,
    val autoStartSuggestion: String,
    val deviceManufacturer: String,
    val isSupportedDevice: Boolean,
    val geofenceStatus: String
)

/**
 * 保活动作数据类
 */
data class KeepAliveAction(
    val type: KeepAliveActionType,
    val title: String,
    val description: String,
    val suggestion: String,
    val isRequired: Boolean,
    val priority: Int
)

/**
 * 保活动作类型枚举
 */
enum class KeepAliveActionType {
    BATTERY_OPTIMIZATION,
    AUTO_START,
    LOCATION_SERVICE,
    GEOFENCE
} 