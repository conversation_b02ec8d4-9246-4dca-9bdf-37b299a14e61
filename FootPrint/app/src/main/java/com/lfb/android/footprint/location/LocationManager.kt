package com.lfb.android.footprint.location

import android.Manifest
import android.content.Context
import android.content.pm.PackageManager
import android.location.Location
import android.location.LocationProvider
import android.location.GnssStatus
import android.os.Build
import android.os.Looper
import android.util.Log
import android.annotation.SuppressLint
import android.icu.text.SimpleDateFormat
import com.google.android.gms.location.*
import com.lfb.android.footprint.Manager.AppNotificationManager
import com.lfb.android.footprint.Manager.LocalLogManager
import com.lfb.android.footprint.prefs.AppPrefs
import com.lfb.android.footprint.location.GpsOffsetFilter
import com.lfb.android.footprint.geofence.GeofenceManager
import com.lfb.android.footprint.geofence.GooglePlayServicesChecker
import io.realm.kotlin.internal.platform.currentTime
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import java.util.Locale
import kotlin.math.abs
import kotlin.math.sqrt
import kotlin.time.Duration
import kotlin.time.Duration.Companion.seconds

/**
 * 定位提供者类型枚举
 */
enum class LocationProviderType {
    FUSED,      // Google Play Services FusedLocationProvider
    SYSTEM      // 系统 LocationManager
}

class LocationManager private constructor(private val context: Context) {

    private val TAG = "LocationManager"

    // 当前使用的定位提供者类型
    private var currentProviderType: LocationProviderType = LocationProviderType.SYSTEM

    // FusedLocationManager 实例
    private val fusedLocationManager by lazy { FusedLocationManager.getInstance(context) }

    private val _locationFlow = MutableSharedFlow<Location>()
    val locationFlow = _locationFlow.asSharedFlow()

    private val _gpsSignalFlow = MutableStateFlow(0)
    val gpsSignalFlow = _gpsSignalFlow.asStateFlow()

    // 定位服务状态相关的StateFlow
    private val _isGpsEnabled = MutableStateFlow(false)
    val isGpsEnabled = _isGpsEnabled.asStateFlow()

    private val _isNetworkEnabled = MutableStateFlow(false)
    val isNetworkEnabled = _isNetworkEnabled.asStateFlow()

    private val _locationServiceStatus = MutableStateFlow("未知")
    val locationServiceStatus = _locationServiceStatus.asStateFlow()

    private val _satelliteCount = MutableStateFlow(0)
    val satelliteCount = _satelliteCount.asStateFlow()

    private val _locationAccuracy = MutableStateFlow(0f)
    val locationAccuracy = _locationAccuracy.asStateFlow()

    private val _isLocationUpdating = MutableStateFlow(false)
    val isLocationUpdating = _isLocationUpdating.asStateFlow()

    private val systemLocationManager: android.location.LocationManager by lazy {
        context.getSystemService(Context.LOCATION_SERVICE) as android.location.LocationManager
    }
    private val locationDataRecorder by lazy { LocationDataRecorder.getInstance(this.context) }
    private val gpsOffsetFilter by lazy { GpsOffsetFilter(context) }
    private val geofenceManager by lazy { GeofenceManager.getInstance(context) }

    var lastLocation: Location? = null

    // 地理围栏相关状态
    private var hasCreatedInitialGeofence = false
    private var lastGeofenceUpdateLocation: Location? = null

    // GPS重试相关状态
    private var gpsRetryCount = 0
    private var maxGpsRetryCount = 100000
    private var gpsRetryDelayMs = 60000L // 60秒延迟
    private var isGpsRetrying = false
    private var gpsRetryHandler: android.os.Handler? = null

    private var lastGPSUpdatetime: Long = 0
    private var lastNetUpdatetime: Long = 0
    // 定位服务状态监听器
    private val providerListener = object : android.location.LocationListener {
        override fun onProviderEnabled(provider: String) {
            updateProviderStatus(provider, true)
            updateLocationServiceStatus()
            LocalLogManager.getInstance(context).logSync("LocationManager: 定位提供者已启用 - $provider")
        }

        override fun onProviderDisabled(provider: String) {
            updateProviderStatus(provider, false)
            updateLocationServiceStatus()
            LocalLogManager.getInstance(context).logSync("LocationManager: 定位提供者已禁用 - $provider")
        }

        override fun onLocationChanged(location: Location) {
            // 空实现，实际定位更新由locationListener处理
        }
    }

    // GPS状态监听器 (Android 6.0及以上)
    private val gnssStatusCallback = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
        object : GnssStatus.Callback() {

            override fun onSatelliteStatusChanged(status: GnssStatus) {
                val satelliteCount = status.satelliteCount
                var usedInFix = 0
                for (i in 0 until satelliteCount) {
                    if (status.usedInFix(i)) {
                        usedInFix++
                    }
                }


                _satelliteCount.value = usedInFix

                val signalStrength = calculateGpsSignalStrength(status)
                if (_gpsSignalFlow.value != signalStrength) {
                    LocalLogManager.getInstance(context).logSync("LocationManager: 卫星状态更新 - 总数: $satelliteCount, 参与定位: $usedInFix")
                }

                _gpsSignalFlow.value = signalStrength
            }

            override fun onStarted() {
                LocalLogManager.getInstance(context).logSync("LocationManager: GPS已启动")
                updateLocationServiceStatus()
                
                // GPS成功启动，重置重试状态
                resetGpsRetryState()
            }

            override fun onStopped() {
                LocalLogManager.getInstance(context).logSync("LocationManager: GPS已停止")
                _satelliteCount.value = 0
                _gpsSignalFlow.value = 0
                updateLocationServiceStatus()
                
                // 如果正在进行定位更新且不在重试中，尝试重启GPS
                if (_isLocationUpdating.value && !isGpsRetrying) {
                    triggerGpsRetry()
                }
            }

            override fun onFirstFix(ttffMillis: Int) {
                LocalLogManager.getInstance(context).logSync("LocationManager: GPS首次定位成功，耗时: ${ttffMillis}ms")
            }
        }
    } else {
        null
    }

    init {
        lastLocation = locationDataRecorder.lastLocation

        // 初始化Handler用于GPS重试
        gpsRetryHandler = android.os.Handler(Looper.getMainLooper())

        // 检测并选择定位提供者
        detectAndSelectLocationProvider()

        // 设置 FusedLocationManager 的错误回调
        fusedLocationManager.setOnProviderFailedCallback {
            onFusedLocationProviderFailed()
        }

        // 初始化定位服务状态
        initializeLocationServiceStatus()

        // 注册定位提供者状态监听器
        registerProviderStatusListeners()
    }

    /**
     * 检测并选择定位提供者
     * 优先使用 FusedLocationProvider，如果不可用则降级到系统 LocationManager
     */
    private fun detectAndSelectLocationProvider() {
        val isFusedSupported = GooglePlayServicesChecker.isFusedLocationProviderSupported(context)

        currentProviderType = if (isFusedSupported) {
            LocalLogManager.getInstance(context).logSync(
                "LocationManager: 检测到 Google Play Services 可用，使用 FusedLocationProvider"
            )
            LocationProviderType.FUSED
        } else {
            LocalLogManager.getInstance(context).logSync(
                "LocationManager: Google Play Services 不可用，使用系统 LocationManager"
            )
            LocationProviderType.SYSTEM
        }

        LocalLogManager.getInstance(context).logSync(
            "LocationManager: 当前定位提供者类型 - ${getProviderTypeName()}"
        )
    }

    /**
     * 获取当前定位提供者类型名称
     */
    private fun getProviderTypeName(): String {
        return when (currentProviderType) {
            LocationProviderType.FUSED -> "FusedLocationProvider"
            LocationProviderType.SYSTEM -> "系统 LocationManager"
        }
    }

    /**
     * 当 FusedLocationProvider 失败时切换到系统 LocationManager
     * 这个方法会在 FusedLocationManager 检测到错误时被调用
     */
    fun onFusedLocationProviderFailed() {
        if (currentProviderType == LocationProviderType.FUSED) {
            LocalLogManager.getInstance(context).logSync(
                "LocationManager: FusedLocationProvider 失败，切换到系统 LocationManager"
            )

            // 停止 Fused 定位
            fusedLocationManager.stopLocationUpdates()

            // 切换到系统定位
            currentProviderType = LocationProviderType.SYSTEM

            // 如果正在更新定位，重新启动
            if (_isLocationUpdating.value) {
                startSystemLocationUpdates()
            }
        }
    }

    @SuppressLint("DefaultLocale")
    private val locationListener = android.location.LocationListener { location ->
        // 识别定位来源
        val provider = location.provider
        val providerName = when (provider) {
            android.location.LocationManager.GPS_PROVIDER -> "GPS"
            android.location.LocationManager.NETWORK_PROVIDER -> "网络"
            android.location.LocationManager.PASSIVE_PROVIDER -> "被动"
            else -> provider ?: "未知"
        }

        // 使用统一的定位更新处理方法
        handleLocationUpdate(location, "系统${providerName}")
    }

    /**
     * 统一的定位更新处理方法
     * 处理来自 FusedLocationProvider 或系统 LocationManager 的定位更新
     */
    private fun handleLocationUpdate(location: Location, sourceName: String) {
        LocalLogManager.getInstance(context).logSync(
            "LocationManager: 收到${sourceName}定位更新 - " +
            "位置: (${location.latitude}, ${location.longitude}), " +
            "精度: ${location.accuracy}m, " +
            "时间: ${location.time}"
        )

        var theLocationAllowed = false
        var locationDistance = 0f
        val previousLocation = this.lastLocation // 将 this.lastLocation 赋给一个局部变量，更清晰

        if (previousLocation != null) {
            // 判断是否是同一天
            val sameDay = SimpleDateFormat("yyyyMMdd", Locale.getDefault()).let { sdf ->
                sdf.format(location.time) == sdf.format(previousLocation.time)
            }
            if (sameDay == false) {
                // 新的一天
                theLocationAllowed = true

                // 地理围栏管理 - 基于原始GPS数据，不依赖过滤结果
                handleGeofenceManagement(location)

            } else {
                // 同一天
                val subTime = abs(location.time - previousLocation.time)
                if (subTime > 1000) {
                    var distanceAllow = 100.0
                    if (location.speed > 0) {

                        if (location.accuracy > 100) {
                            distanceAllow = location.accuracy.toDouble()
                        } else {
                            distanceAllow = 20+sqrt(location.speed*5)*6.5
                        }
                    } else {
                        // speed 为0
                        if (location.accuracy < 100) {
                            // 精度较高时
                            distanceAllow = location.accuracy.toDouble() * 1.5
                        } else {
                            // 精度较低时
                            distanceAllow = 200.0
                        }
                    }

                    val distance = location.distanceTo(previousLocation)
                    locationDistance = distance
                    if (distance > distanceAllow) {
                        theLocationAllowed = true
                    } else {
                        LocalLogManager.getInstance(this.context).logSync("GPS偏移 distance(${distance}) distanceAllow(${distanceAllow}) subTime(${subTime}) speed(${location.speed}) lc: ${location}")
                    }
                }
            }
        } else {
            theLocationAllowed = true
        }

        // 应用GPS偏移过滤器
        if (theLocationAllowed) {
            theLocationAllowed = gpsOffsetFilter.shouldAcceptLocation(location)
            if (!theLocationAllowed) {
                LocalLogManager.getInstance(this.context).logSync("GPS偏移过滤器拒绝: lat=${location.latitude}, lng=${location.longitude}, accuracy=${location.accuracy}")
            }
        }

        this.lastLocation = location

        // 更新定位精度状态
        _locationAccuracy.value = location.accuracy

        if (theLocationAllowed) {
            CoroutineScope(Dispatchers.IO).launch {
                _locationFlow.emit(location)
                locationDataRecorder.recordLocation(location)
            }
        }

        val sdf = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
        val title = "位置更新:${sdf.format(location.time)}"
        val text = String.format(
            "%s %.1f 当前位置: %.3f, %.3f",
            theLocationAllowed,
            locationDistance,
            location.latitude,
            location.longitude
        )

        AppNotificationManager.showLocationUpdateNotification(context, title, text)

        LocalLogManager.getInstance(this.context).logSync("****收到定位更新来自：${sourceName} *****")

        // 判断是否是GPS定位（包括系统GPS和FusedLocationProvider）
        val isGpsSource = sourceName.contains("GPS") || sourceName.contains("FusedLocationProvider")

        if (isGpsSource) {
            lastGPSUpdatetime = location.time.toLong()/1000

            if (isGpsRetrying) {
                cancelGpsRetry()
            }
        } else {
            chechTimeToRetryGPS("网络定位触发")

            lastNetUpdatetime = location.time.toLong()/1000
        }
    }

    @SuppressLint("MissingPermission")
    fun startLocationUpdates() {
        if (!hasRequiredPermissions()) return

        LocalLogManager.getInstance(context).logSync(
            "LocationManager: 开始位置更新 - 使用 ${getProviderTypeName()}"
        )

        // 更新定位状态
        _isLocationUpdating.value = true

        // 重置GPS重试状态
        resetGpsRetryState()

        // 重置地理围栏状态
        resetGeofenceState()

        // 根据当前定位提供者类型启动定位
        when (currentProviderType) {
            LocationProviderType.FUSED -> startFusedLocationUpdates()
            LocationProviderType.SYSTEM -> startSystemLocationUpdates()
        }
    }

    /**
     * 启动 FusedLocationProvider 定位更新
     */
    @SuppressLint("MissingPermission")
    private fun startFusedLocationUpdates() {
        LocalLogManager.getInstance(context).logSync("LocationManager: 启动 FusedLocationProvider 定位更新")

        try {
            fusedLocationManager.startLocationUpdates { location ->
                // 处理定位更新
                handleLocationUpdate(location, "FusedLocationProvider")
            }

            // 注册GPS状态监听器（用于获取卫星信息）
            registerGnssStatusCallback()

            // 更新定位服务状态
            updateLocationServiceStatus()

        } catch (e: Exception) {
            LocalLogManager.getInstance(context).logSync(
                "LocationManager: 启动 FusedLocationProvider 失败 - ${e.message}，降级到系统 LocationManager"
            )

            // 降级到系统 LocationManager
            currentProviderType = LocationProviderType.SYSTEM
            startSystemLocationUpdates()
        }
    }

    /**
     * 启动系统 LocationManager 定位更新
     */
    @SuppressLint("MissingPermission")
    private fun startSystemLocationUpdates() {
        LocalLogManager.getInstance(context).logSync("LocationManager: 启动系统 LocationManager 定位更新")

        // 获取当前的定位参数
        var minTime = 1200L
        var minDistance = 100f

        var minTime_net = 120L
        var minDistance_net = 20f

        when (AppPrefs.sharedInstance.runningModel) {
            1 -> {
                // 耗电模式
                minTime = 1000L
                minDistance = 20f

                minTime_net = 1000L
                minDistance_net = 20f

            }
            2 -> {
                // 省电模式
                minTime = 2000L
                minDistance = 200f

                minTime_net = 2000L
                minDistance_net = 20f
            }
        }

        if (systemLocationManager.isProviderEnabled(android.location.LocationManager.GPS_PROVIDER)) {
            systemLocationManager.requestLocationUpdates(
                android.location.LocationManager.GPS_PROVIDER,
                minTime,
                minDistance,
                locationListener,
                Looper.getMainLooper()
            )
        }

        if (systemLocationManager.isProviderEnabled(android.location.LocationManager.NETWORK_PROVIDER)) {
            systemLocationManager.requestLocationUpdates(
                android.location.LocationManager.NETWORK_PROVIDER,
                minTime_net,
                minDistance_net,
                locationListener,
                Looper.getMainLooper()
            )
        }

        // 注册GPS状态监听器
        registerGnssStatusCallback()

        // 更新定位服务状态
        updateLocationServiceStatus()
    }

    private fun hasRequiredPermissions(): Boolean {
        return context.checkSelfPermission(Manifest.permission.ACCESS_FINE_LOCATION) == PackageManager.PERMISSION_GRANTED &&
               context.checkSelfPermission(Manifest.permission.ACCESS_COARSE_LOCATION) == PackageManager.PERMISSION_GRANTED &&
               (Build.VERSION.SDK_INT < Build.VERSION_CODES.Q ||
                context.checkSelfPermission(Manifest.permission.ACCESS_BACKGROUND_LOCATION) == PackageManager.PERMISSION_GRANTED)
    }

    fun stopLocationUpdates() {
        LocalLogManager.getInstance(context).logSync(
            "LocationManager: 停止位置更新 - 当前使用 ${getProviderTypeName()}"
        )

        // 更新定位状态
        _isLocationUpdating.value = false

        // 取消GPS重试
        cancelGpsRetry()

        // 根据当前定位提供者类型停止定位
        when (currentProviderType) {
            LocationProviderType.FUSED -> stopFusedLocationUpdates()
            LocationProviderType.SYSTEM -> stopSystemLocationUpdates()
        }

        // 注销GPS状态监听器
        unregisterGnssStatusCallback()

        // 重置GPS相关状态
        _satelliteCount.value = 0
        _gpsSignalFlow.value = 0
        _locationAccuracy.value = 0f

        // 更新定位服务状态
        updateLocationServiceStatus()

        // 注意：不重置地理围栏状态，让地理围栏继续工作以便后台唤醒
    }

    /**
     * 停止 FusedLocationProvider 定位更新
     */
    private fun stopFusedLocationUpdates() {
        LocalLogManager.getInstance(context).logSync("LocationManager: 停止 FusedLocationProvider 定位更新")
        fusedLocationManager.stopLocationUpdates()
    }

    /**
     * 停止系统 LocationManager 定位更新
     */
    private fun stopSystemLocationUpdates() {
        LocalLogManager.getInstance(context).logSync("LocationManager: 停止系统 LocationManager 定位更新")
        systemLocationManager.removeUpdates(locationListener)
    }

    /**
     * 处理地理围栏管理
     * 基于原始GPS数据，不依赖位置过滤结果
     */
    private fun handleGeofenceManagement(location: Location) {
        try {
            // 首先检查是否支持地理围栏功能
            if (!GooglePlayServicesChecker.isGeofenceSupported(context)) {
                // 只在第一次检查时记录日志，避免重复日志
                if (!hasCreatedInitialGeofence) {
                    LocalLogManager.getInstance(context).logSync(
                        "LocationManager: 设备不支持地理围栏功能，跳过地理围栏管理"
                    )
                    hasCreatedInitialGeofence = true // 设置为true避免重复检查
                }
                return
            }

            // 检查GPS精度，只有精度足够好的位置才用于地理围栏
            if (location.accuracy > 100) {
                LocalLogManager.getInstance(context).logSync(
                    "LocationManager: GPS精度不足(${location.accuracy}m)，跳过地理围栏更新"
                )
                return
            }

            LocalLogManager.getInstance(context).logSync(
                "LocationManager: 处理地理围栏管理 - " +
                "位置: (${location.latitude}, ${location.longitude}), " +
                "精度: ${location.accuracy}m"
            )

            // 如果还没有创建初始地理围栏，立即创建
            if (!hasCreatedInitialGeofence) {
                createInitialGeofence(location)
                hasCreatedInitialGeofence = true
                lastGeofenceUpdateLocation = location
            } else {
                // 检查是否需要更新地理围栏
                checkAndUpdateGeofence(location)
            }

        } catch (e: Exception) {
            LocalLogManager.getInstance(context).logSync(
                "LocationManager: 地理围栏管理异常 - ${e.message}"
            )
        }
    }

    /**
     * 创建初始地理围栏
     */
    private fun createInitialGeofence(location: Location) {
        try {
            LocalLogManager.getInstance(context).logSync(
                "LocationManager: 创建初始地理围栏 - " +
                "位置: (${location.latitude}, ${location.longitude}), " +
                "精度: ${location.accuracy}m"
            )

            geofenceManager.createGeofence(location)

        } catch (e: Exception) {
            LocalLogManager.getInstance(context).logSync(
                "LocationManager: 创建初始地理围栏失败 - ${e.message}"
            )
        }
    }

    /**
     * 检查并更新地理围栏
     */
    private fun checkAndUpdateGeofence(location: Location) {
        try {
            lastGeofenceUpdateLocation?.let { lastLocation ->
                val distance = lastLocation.distanceTo(location)
                val threshold = 50f // 50米阈值

                if (distance > threshold) {
                    LocalLogManager.getInstance(context).logSync(
                        "LocationManager: 位置变化超过阈值(${threshold}m)，当前距离: ${distance}m，更新地理围栏"
                    )
                    geofenceManager.createGeofence(location)
                    lastGeofenceUpdateLocation = location
                } else {
                    LocalLogManager.getInstance(context).logSync(
                        "LocationManager: 位置变化未超过阈值，距离: ${distance}m"
                    )
                }
            } ?: run {
                // 如果没有上次更新位置记录，直接更新
                LocalLogManager.getInstance(context).logSync(
                    "LocationManager: 没有上次地理围栏位置记录，创建新围栏"
                )
                geofenceManager.createGeofence(location)
                lastGeofenceUpdateLocation = location
            }

        } catch (e: Exception) {
            LocalLogManager.getInstance(context).logSync(
                "LocationManager: 检查地理围栏更新失败 - ${e.message}"
            )
        }
    }

    /**
     * 重置地理围栏状态
     * 在停止定位或重新开始时调用
     */
    fun resetGeofenceState() {
        try {
            LocalLogManager.getInstance(context).logSync("LocationManager: 重置地理围栏状态")
            hasCreatedInitialGeofence = false
            lastGeofenceUpdateLocation = null
        } catch (e: Exception) {
            LocalLogManager.getInstance(context).logSync(
                "LocationManager: 重置地理围栏状态失败 - ${e.message}"
            )
        }
    }

    /**
     * 获取地理围栏状态信息
     */
    fun getGeofenceStatus(): String {
        return try {
            val isSupported = GooglePlayServicesChecker.isGeofenceSupported(context)
            val (geofenceId, geofenceLocation) = geofenceManager.getCurrentGeofenceInfo()

            buildString {
                append("地理围栏支持: ${if (isSupported) "是" else "否"}\n")
                if (!isSupported) {
                    append("不支持原因: ${GooglePlayServicesChecker.getGooglePlayServicesStatusDescription(context)}\n")
                }
                append("初始围栏已创建: $hasCreatedInitialGeofence\n")
                append("当前围栏ID: ${geofenceId ?: "无"}\n")
                append("围栏位置: ${geofenceLocation?.let { "(${it.latitude}, ${it.longitude})" } ?: "无"}\n")
                append("上次更新位置: ${lastGeofenceUpdateLocation?.let { "(${it.latitude}, ${it.longitude})" } ?: "无"}")
            }
        } catch (e: Exception) {
            "获取地理围栏状态失败: ${e.message}"
        }
    }

    // ==================== 定位服务状态监听器相关方法 ====================
    
    /**
     * 初始化定位服务状态
     */
    private fun initializeLocationServiceStatus() {
        updateProviderStatus(android.location.LocationManager.GPS_PROVIDER, 
            systemLocationManager.isProviderEnabled(android.location.LocationManager.GPS_PROVIDER))
        updateProviderStatus(android.location.LocationManager.NETWORK_PROVIDER, 
            systemLocationManager.isProviderEnabled(android.location.LocationManager.NETWORK_PROVIDER))
        updateLocationServiceStatus()
    }

    /**
     * 注册定位提供者状态监听器
     */
    @SuppressLint("MissingPermission")
    private fun registerProviderStatusListeners() {
        if (!hasRequiredPermissions()) return
        
        try {
            // 注册GPS提供者状态监听
            systemLocationManager.requestLocationUpdates(
                android.location.LocationManager.GPS_PROVIDER,
                Long.MAX_VALUE,
                Float.MAX_VALUE,
                providerListener,
                Looper.getMainLooper()
            )
            
            // 注册网络提供者状态监听
            systemLocationManager.requestLocationUpdates(
                android.location.LocationManager.NETWORK_PROVIDER,
                Long.MAX_VALUE,
                Float.MAX_VALUE,
                providerListener,
                Looper.getMainLooper()
            )
            
            LocalLogManager.getInstance(context).logSync("LocationManager: 定位提供者状态监听器已注册")
        } catch (e: Exception) {
            LocalLogManager.getInstance(context).logSync("LocationManager: 注册定位提供者状态监听器失败 - ${e.message}")
        }
    }

    /**
     * 更新提供者状态
     */
    private fun updateProviderStatus(provider: String, isEnabled: Boolean) {
        when (provider) {
            android.location.LocationManager.GPS_PROVIDER -> {
                _isGpsEnabled.value = isEnabled
            }
            android.location.LocationManager.NETWORK_PROVIDER -> {
                _isNetworkEnabled.value = isEnabled
            }
        }
    }

    /**
     * 更新定位服务状态
     */
    private fun updateLocationServiceStatus() {
        val providerPrefix = when (currentProviderType) {
            LocationProviderType.FUSED -> "[Fused] "
            LocationProviderType.SYSTEM -> "[系统] "
        }

        val status = when {
            _isLocationUpdating.value -> {
                when {
                    _isGpsEnabled.value && _isNetworkEnabled.value -> "${providerPrefix}GPS + 网络定位"
                    _isGpsEnabled.value -> "${providerPrefix}GPS定位"
                    _isNetworkEnabled.value -> "${providerPrefix}网络定位"
                    else -> "${providerPrefix}无定位服务"
                }
            }
            _isGpsEnabled.value && _isNetworkEnabled.value -> "GPS + 网络可用"
            _isGpsEnabled.value -> "GPS可用"
            _isNetworkEnabled.value -> "网络可用"
            else -> "定位服务不可用"
        }

        _locationServiceStatus.value = status
    }

    /**
     * 注册GPS状态监听器
     */
    @SuppressLint("MissingPermission")
    private fun registerGnssStatusCallback() {
        if (!hasRequiredPermissions()) return
        
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N && gnssStatusCallback != null) {
                systemLocationManager.registerGnssStatusCallback(gnssStatusCallback!!, null)
                LocalLogManager.getInstance(context).logSync("LocationManager: GNSS状态监听器已注册")
            }
        } catch (e: Exception) {
            LocalLogManager.getInstance(context).logSync("LocationManager: 注册GNSS状态监听器失败 - ${e.message}")
        }
    }

    /**
     * 注销GPS状态监听器
     */
    private fun unregisterGnssStatusCallback() {
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N && gnssStatusCallback != null) {
                systemLocationManager.unregisterGnssStatusCallback(gnssStatusCallback!!)
                LocalLogManager.getInstance(context).logSync("LocationManager: GNSS状态监听器已注销")
            }
        } catch (e: Exception) {
            LocalLogManager.getInstance(context).logSync("LocationManager: 注销GNSS状态监听器失败 - ${e.message}")
        }
    }

    /**
     * 计算GPS信号强度
     */
    private fun calculateGpsSignalStrength(status: GnssStatus): Int {
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.N) return 0
        
        val satelliteCount = status.satelliteCount
        var totalCn0 = 0.0
        var usedCount = 0
        
        for (i in 0 until satelliteCount) {
            if (status.usedInFix(i)) {
                totalCn0 += status.getCn0DbHz(i)
                usedCount++
            }
        }
        
        if (usedCount == 0) return 0
        
        val averageCn0 = totalCn0 / usedCount
        
        // 将信号强度转换为0-100的范围
        // 一般来说，Cn0的范围是10-50 dBHz
        return when {
            averageCn0 >= 40 -> 100 // 优秀
            averageCn0 >= 35 -> 80  // 良好
            averageCn0 >= 30 -> 60  // 一般
            averageCn0 >= 25 -> 40  // 较差
            averageCn0 >= 20 -> 20  // 很差
            else -> 0               // 无信号
        }
    }

    // ==================== GPS重试相关方法 ====================
    
    /**
     * 触发GPS重试
     */
    private fun triggerGpsRetry() {
        if (gpsRetryCount >= maxGpsRetryCount) {
            LocalLogManager.getInstance(context).logSync(
                "LocationManager: GPS重试次数已达上限(${maxGpsRetryCount})，停止重试"
            )
            return
        }
        
        if (isGpsRetrying) {
            LocalLogManager.getInstance(context).logSync("LocationManager: GPS重试已在进行中，跳过")
            return
        }
        
        gpsRetryCount++
        isGpsRetrying = true
        
        LocalLogManager.getInstance(context).logSync(
            "LocationManager: GPS重试 ${gpsRetryCount}/${maxGpsRetryCount}，${gpsRetryDelayMs}ms后执行"
        )
        
        gpsRetryHandler?.postDelayed({
            executeGpsRetry()
        }, gpsRetryDelayMs)
    }
    
    /**
     * 执行GPS重试
     */
    @SuppressLint("MissingPermission")
    private fun executeGpsRetry() {
        if (!_isLocationUpdating.value) {
            LocalLogManager.getInstance(context).logSync("LocationManager: 定位已停止，取消GPS重试")
            isGpsRetrying = false
            return
        }
        
        if (!hasRequiredPermissions()) {
            LocalLogManager.getInstance(context).logSync("LocationManager: 权限不足，取消GPS重试")
            isGpsRetrying = false
            return
        }
        
        if (!systemLocationManager.isProviderEnabled(android.location.LocationManager.GPS_PROVIDER)) {
            LocalLogManager.getInstance(context).logSync("LocationManager: GPS提供者已禁用，取消GPS重试")
            isGpsRetrying = false
            return
        }

        if (currentProviderType == LocationProviderType.FUSED) {
            LocalLogManager.getInstance(context).logSync("LocationManager: 当前定位为FusedLocationProvider，取消GPS重试")
            isGpsRetrying = false
            return
        }
        
        try {
            LocalLogManager.getInstance(context).logSync("LocationManager: 执行GPS重试 - 重新注册GNSS状态监听器")
            
            // 先注销现有的监听器
            unregisterGnssStatusCallback()
            
            // 短暂延迟后重新注册
            gpsRetryHandler?.postDelayed({
                registerGnssStatusCallback()
                
                // 重新请求GPS位置更新
                restartGpsLocationUpdates()
                
                isGpsRetrying = false
                LocalLogManager.getInstance(context).logSync("LocationManager: GPS重试执行完成")
            }, 1000) // 1秒延迟
            
        } catch (e: Exception) {
            LocalLogManager.getInstance(context).logSync("LocationManager: GPS重试执行失败 - ${e.message}")
            isGpsRetrying = false
        }
    }
    
    /**
     * 重新启动GPS位置更新
     */
    @SuppressLint("MissingPermission")
    private fun restartGpsLocationUpdates() {
        if (!hasRequiredPermissions()) return
        
        try {
            // 获取当前的定位参数
            var minTime = 1200L
            var minDistance = 100f

            var minTime_net = 120L
            var minDistance_net = 20f

            when (AppPrefs.sharedInstance.runningModel) {
                1 -> {
                    // 耗电模式
                    minTime = 1000L
                    minDistance = 20f

                    minTime_net = 1000L
                    minDistance_net = 20f

                }
                2 -> {
                    // 省电模式
                    minTime = 2000L
                    minDistance = 200f

                    minTime_net = 2000L
                    minDistance_net = 20f
                }
            }
            
            // 直接重新请求GPS位置更新，系统会自动替换现有的相同provider请求
            // 不移除现有的位置更新，避免影响网络定位
            if (systemLocationManager.isProviderEnabled(android.location.LocationManager.GPS_PROVIDER)) {
                systemLocationManager.requestLocationUpdates(
                    android.location.LocationManager.GPS_PROVIDER,
                    minTime,
                    minDistance,
                    locationListener,
                    Looper.getMainLooper()
                )
                LocalLogManager.getInstance(context).logSync("LocationManager: GPS已重新启动（保留网络定位）")
            } else {
                LocalLogManager.getInstance(context).logSync("LocationManager: GPS提供者已禁用，无法重新启动GPS位置更新")
            }

            if (systemLocationManager.isProviderEnabled(android.location.LocationManager.NETWORK_PROVIDER)) {
                systemLocationManager.requestLocationUpdates(
                    android.location.LocationManager.NETWORK_PROVIDER,
                    minTime_net,
                    minDistance_net,
                    locationListener,
                    Looper.getMainLooper()
                )

                LocalLogManager.getInstance(context).logSync("LocationManager: GPS位置更新已重新启动（启动网络定位）")
            }
            
        } catch (e: Exception) {
            LocalLogManager.getInstance(context).logSync("LocationManager: 重新启动GPS位置更新失败 - ${e.message}")
        }
    }

    @SuppressLint("MissingPermission")
    private fun restartGps(from:String) {
        if (!hasRequiredPermissions()) return

        try {
            // 检查设备是否处于 Doze 模式
            val isInDozeMode = isDeviceInDozeMode()
            LocalLogManager.getInstance(context).logSync(
                "LocationManager: 设备Doze状态: $isInDozeMode (${from}) - 当前使用 ${getProviderTypeName()}"
            )

            // 根据当前定位提供者类型重启定位
            when (currentProviderType) {
                LocationProviderType.FUSED -> restartFusedLocationProvider(from, isInDozeMode)
                LocationProviderType.SYSTEM -> restartSystemLocationProvider(from, isInDozeMode)
            }

        } catch (e: Exception) {
            LocalLogManager.getInstance(context).logSync("LocationManager: 重启定位失败(${from}) - ${e.message}")
        }
    }

    /**
     * 重启 FusedLocationProvider
     */
    @SuppressLint("MissingPermission")
    private fun restartFusedLocationProvider(from: String, isInDozeMode: Boolean) {
        LocalLogManager.getInstance(context).logSync("LocationManager: 重启 FusedLocationProvider (${from})")

        try {
            // 停止当前的定位更新
            fusedLocationManager.stopLocationUpdates()

            // 短暂延迟后重新启动
            android.os.Handler(Looper.getMainLooper()).postDelayed({
                fusedLocationManager.startLocationUpdates { location ->
                    handleLocationUpdate(location, "FusedLocationProvider")
                }
                LocalLogManager.getInstance(context).logSync("LocationManager: FusedLocationProvider 已重新启动(${from})")

                if (isInDozeMode) {
                    LocalLogManager.getInstance(context).logSync("LocationManager: Doze模式下 FusedLocationProvider 重启完成")
                }
            }, 500)

        } catch (e: Exception) {
            LocalLogManager.getInstance(context).logSync("LocationManager: 重启 FusedLocationProvider 失败 - ${e.message}")

            // 如果 FusedLocationProvider 重启失败，尝试切换到系统定位
            LocalLogManager.getInstance(context).logSync("LocationManager: 尝试切换到系统定位")
            currentProviderType = LocationProviderType.SYSTEM
            restartSystemLocationProvider(from, isInDozeMode)
        }
    }

    /**
     * 重启系统 LocationManager
     */
    @SuppressLint("MissingPermission")
    private fun restartSystemLocationProvider(from: String, isInDozeMode: Boolean) {
        LocalLogManager.getInstance(context).logSync("LocationManager: 重启系统 LocationManager (${from})")

        // 获取当前的定位参数
        var minTime = 1200L
        var minDistance = 100f

        when (AppPrefs.sharedInstance.runningModel) {
            1 -> {
                // 耗电模式
                minTime = 1000L
                minDistance = 20f
            }
            2 -> {
                // 省电模式
                minTime = 2000L
                minDistance = 200f
            }
        }

        // 先尝试移除现有的位置更新，然后重新请求
        try {
            systemLocationManager.removeUpdates(locationListener)
            LocalLogManager.getInstance(context).logSync("LocationManager: 已移除现有位置更新")
        } catch (e: Exception) {
            LocalLogManager.getInstance(context).logSync("LocationManager: 移除现有位置更新失败: ${e.message}")
        }

        // 检查GPS提供者状态
        if (systemLocationManager.isProviderEnabled(android.location.LocationManager.GPS_PROVIDER)) {
            // 重新请求GPS位置更新
            systemLocationManager.requestLocationUpdates(
                android.location.LocationManager.GPS_PROVIDER,
                minTime,
                minDistance,
                locationListener,
                Looper.getMainLooper()
            )
            LocalLogManager.getInstance(context).logSync("LocationManager: GPS已重新启动(${from}) - minTime: ${minTime}ms, minDistance: ${minDistance}m")

            // 在 Doze 模式下，同时请求网络定位作为备用
            if (isInDozeMode && systemLocationManager.isProviderEnabled(android.location.LocationManager.NETWORK_PROVIDER)) {
                systemLocationManager.requestLocationUpdates(
                    android.location.LocationManager.NETWORK_PROVIDER,
                    minTime,
                    minDistance,
                    locationListener,
                    Looper.getMainLooper()
                )
                LocalLogManager.getInstance(context).logSync("LocationManager: Doze模式下同时启用网络定位作为备用")
            }

        } else {
            LocalLogManager.getInstance(context).logSync("LocationManager: GPS提供者已禁用(${from})")

            // GPS不可用时，尝试使用网络定位
            if (systemLocationManager.isProviderEnabled(android.location.LocationManager.NETWORK_PROVIDER)) {
                systemLocationManager.requestLocationUpdates(
                    android.location.LocationManager.NETWORK_PROVIDER,
                    minTime,
                    minDistance,
                    locationListener,
                    Looper.getMainLooper()
                )
                LocalLogManager.getInstance(context).logSync("LocationManager: GPS不可用，已启用网络定位(${from})")
            }
        }

        // 如果是从 Doze 模式恢复，记录特殊日志
        if (isInDozeMode) {
            LocalLogManager.getInstance(context).logSync("LocationManager: Doze模式下系统定位重启完成，建议检查电池优化设置")
        }
    }

    /**
     * 重置GPS重试状态
     */
    private fun resetGpsRetryState() {
        if (gpsRetryCount > 0 || isGpsRetrying) {
            LocalLogManager.getInstance(context).logSync("LocationManager: 重置GPS重试状态")
        }
        gpsRetryCount = 0
        isGpsRetrying = false
        gpsRetryHandler?.removeCallbacksAndMessages(null)
    }
    
    /**
     * 取消GPS重试
     */
    private fun cancelGpsRetry() {
        if (isGpsRetrying) {
            LocalLogManager.getInstance(context).logSync("LocationManager: 取消GPS重试")
            isGpsRetrying = false
            gpsRetryHandler?.removeCallbacksAndMessages(null)
        }
    }

    fun chechTimeToRetryGPS(from:String) {

        val currentTime = currentTime().epochSeconds
        val subGPSTimestamp = currentTime - lastGPSUpdatetime
        val subNetTimestamp = currentTime - lastNetUpdatetime


        LocalLogManager.getInstance(context).logSync(
            "checkTimeToRetryGPS: GPS重试轮询 GPS定位: ${subGPSTimestamp}s Net定位: ${subNetTimestamp}s 未更新了"
        )

        val canRestartGpsTime = 60 * 5
        if (subGPSTimestamp > canRestartGpsTime && subNetTimestamp > canRestartGpsTime) {
            LocalLogManager.getInstance(context).logSync(
                "restartGps 执行GPS重新刷新 - GPS定位更新时间、Net定位时间均超过${canRestartGpsTime}s"
            )
            restartGps(from)
        }
    }

    /**
     * 检查设备是否处于 Doze 模式
     */
    private fun isDeviceInDozeMode(): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            try {
                val powerManager = context.getSystemService(Context.POWER_SERVICE) as android.os.PowerManager
                val isInDozeMode = powerManager.isDeviceIdleMode
                val isIgnoringBatteryOptimizations = powerManager.isIgnoringBatteryOptimizations(context.packageName)
                
                LocalLogManager.getInstance(context).logSync(
                    "LocationManager: Doze状态检查 - isDeviceIdleMode: $isInDozeMode, " +
                    "isIgnoringBatteryOptimizations: $isIgnoringBatteryOptimizations"
                )
                
                // 如果设备处于 Doze 模式且应用未在电池优化白名单中，返回 true
//                isInDozeMode && !isIgnoringBatteryOptimizations
                isInDozeMode
            } catch (e: Exception) {
                LocalLogManager.getInstance(context).logSync("LocationManager: 检查Doze模式失败: ${e.message}")
                false
            }
        } else {
            false
        }
    }


    /**
     * 获取当前定位提供者类型
     */
    fun getCurrentProviderType(): LocationProviderType {
        return currentProviderType
    }

    /**
     * 获取定位提供者状态信息
     */
    fun getLocationProviderStatus(): String {
        return buildString {
            append("当前定位提供者: ${getProviderTypeName()}\n")
            append("Google Play Services: ")
            append(GooglePlayServicesChecker.getGooglePlayServicesStatusDescription(context))
            append("\n")
            if (currentProviderType == LocationProviderType.FUSED) {
                append("FusedLocationProvider 状态: ")
                append(if (fusedLocationManager.isUpdating()) "运行中" else "已停止")
            } else {
                append("系统 LocationManager 状态: ")
                append(if (_isLocationUpdating.value) "运行中" else "已停止")
            }
        }
    }

    companion object {
        @Volatile
        private var instance: LocationManager? = null

        fun getInstance(context: Context): LocationManager {
            return instance ?: synchronized(this) {
                instance ?: LocationManager(context.applicationContext).also { instance = it }
            }
        }
    }
}