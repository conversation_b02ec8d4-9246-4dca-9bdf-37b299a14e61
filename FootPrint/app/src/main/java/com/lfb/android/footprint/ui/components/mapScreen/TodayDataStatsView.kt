package com.lfb.android.footprint.ui.components.mapScreen

import androidx.compose.animation.core.*
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.lfb.android.footprint.ui.theme.rememberMapThemeConfig
import kotlin.math.roundToInt

/**
 * 今日数据统计视图 - 参考iOS TodayFunctionView
 * 显示距离、点数等统计信息（深色主题）
 */
@Composable
fun TodayDataStatsView(
    distance: Double,
    points: Int,
    modifier: Modifier = Modifier
) {
    val themeConfig = rememberMapThemeConfig()
    var animatedDistance by remember { mutableStateOf(0.0) }
    var animatedPoints by remember { mutableStateOf(0) }
    
    // 数字动画效果
    LaunchedEffect(distance) {
        animate(
            initialValue = 0f,
            targetValue = distance.toFloat(),
            animationSpec = tween(durationMillis = 1500, easing = FastOutSlowInEasing)
        ) { value, _ ->
            animatedDistance = value.toDouble()
        }
    }
    
    LaunchedEffect(points) {
        animate(
            initialValue = 0f,
            targetValue = points.toFloat(),
            animationSpec = tween(durationMillis = 1500, easing = FastOutSlowInEasing)
        ) { value, _ ->
            animatedPoints = value.roundToInt()
        }
    }

    Column(
        modifier = modifier
            .background(
                color = themeConfig.backgroundColor,
                shape = RoundedCornerShape(12.dp)
            )
            .padding(horizontal = 12.dp, vertical = 8.dp)
    ) {
        // 标题
        Text(
            text = "今日数据",
            style = TextStyle(
                color = themeConfig.normalTextColor,
                fontSize = 13.sp,
                fontWeight = FontWeight.Light
            ),
            modifier = Modifier.padding(start = 12.dp, top = 8.dp, bottom = 4.dp)
        )
        
        // 统计数据行
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .height(54.dp),
            horizontalArrangement = Arrangement.SpaceEvenly,
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 距离统计
            StatItem(
                value = formatDistanceValue(animatedDistance),
                unit = getDistanceUnit(animatedDistance),
                label = "轨迹距离",
                modifier = Modifier.weight(1f)
            )

//            // 分隔线
//            Box(
//                modifier = Modifier
//                    .width(1.dp)
//                    .height(32.dp)
//                    .background(Color.White.copy(alpha = 0.1f))
//            )

            // 点数统计
            StatItem(
                value = animatedPoints.toString(),
                unit = "个",
                label = "轨迹点数",
                modifier = Modifier.weight(1f)
            )

            // 分隔线
//            Box(
//                modifier = Modifier
//                    .width(1.dp)
//                    .height(32.dp)
//                    .background(Color.White.copy(alpha = 0.1f))
//            )

            // 步数统计（占位）
            StatItem(
                value = "0",
                unit = "步",
                label = "总步数",
                modifier = Modifier.weight(1f)
            )
        }
    }
}

@Composable
private fun StatItem(
    value: String,
    unit: String,
    label: String,
    modifier: Modifier = Modifier
) {
    val themeConfig = rememberMapThemeConfig()

    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Row(
            verticalAlignment = Alignment.Bottom,
            horizontalArrangement = Arrangement.Center
        ) {
            Text(
                text = value,
                style = TextStyle(
                    color = themeConfig.normalTextColor,
                    fontSize = 15.sp,
                    fontWeight = FontWeight.Normal
                )
            )
            if (unit.isNotEmpty()) {
                Spacer(modifier = Modifier.width(2.dp))
                Text(
                    text = unit,
                    style = TextStyle(
                        color = themeConfig.normalTextColor.copy(alpha = 0.8f),
                        fontSize = 9.sp,
                        fontWeight = FontWeight.Normal
                    ),
                    modifier = Modifier.padding(start = 2.dp, bottom = 2.dp)
                )
            }
        }
        Spacer(modifier = Modifier.height(8.dp))
        Text(
            text = label,
            style = TextStyle(
                color = themeConfig.normalTextColor.copy(alpha = 0.55f),
                fontSize = 10.sp
            )
        )
    }
}

// 格式化距离值
private fun formatDistanceValue(distance: Double): String {
    return when {
        distance >= 1000 -> String.format("%.2f", distance / 1000)
        else -> String.format("%.0f", distance)
    }
}

// 获取距离单位
private fun getDistanceUnit(distance: Double): String {
    return if (distance >= 1000) "千米" else "米"
}
