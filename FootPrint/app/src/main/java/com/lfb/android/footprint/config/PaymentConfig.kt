package com.lfb.android.footprint.config

/**
 * 支付配置
 * 
 * 使用说明：
 * 1. 在微信商户平台(https://pay.weixin.qq.com/)注册商户
 * 2. 获取商户号(mch_id)和API密钥
 * 3. 配置支付回调URL
 * 4. 将下面的占位符替换为实际的值
 */
object PaymentConfig {
    
    /**
     * 微信商户号
     * 需要在微信商户平台注册后获取
     */
    const val MCH_ID = "1727996379"
    
    /**
     * 微信支付API密钥 (V2版本)
     * 注意：API密钥应该保存在服务端，这里仅用于演示
     * 生产环境中应该通过服务端API处理支付
     */
    const val API_KEY = "8wMuoF1k0F8hSEtM86WDg1jf5P2OrrNz"
    
    /**
     * 微信支付API V3密钥
     * V3版本使用的API密钥，用于请求签名
     * 注意：这个密钥需要在微信商户平台设置，长度必须为32位
     */
    const val API_V3_KEY = "Hn0igP0c9zwpr9yJhsCWcTC1dwDLmfr2"
    
    /**
     * 商户证书序列号
     * 用于V3版本API的证书验证
     * 需要从微信商户平台下载的证书中获取
     */
    const val CERT_SERIAL_NO = "1DDE55AD98ED71D6EDD4A4A16996DE7B47773A8C" // 示例序列号，需要替换为真实值
    
    /**
     * 商户私钥 (用于V3 API调起支付签名)
     * 从微信商户平台下载的apiclient_key.pem文件内容
     */
    val MERCHANT_PRIVATE_KEY = """
************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************    """.trimIndent()
    
    /**
     * 是否有有效的商户私钥
     */
    fun hasMerchantPrivateKey(): Boolean {
        return MERCHANT_PRIVATE_KEY.contains("BEGIN PRIVATE KEY") && 
               !MERCHANT_PRIVATE_KEY.contains("YOUR_MERCHANT_PRIVATE_KEY_HERE")
    }
    
    /**
     * 支付回调通知URL
     * 微信支付成功后会向这个URL发送通知
     */
    const val NOTIFY_URL = "https://your-server.com/api/payment/notify"
    
    /**
     * 微信支付统一下单接口 (V2版本)
     */
    const val UNIFIED_ORDER_URL = "https://api.mch.weixin.qq.com/pay/unifiedorder"
    
    /**
     * 微信支付统一下单接口 (V3版本)
     */
    const val UNIFIED_ORDER_V3_URL = "https://api.mch.weixin.qq.com/v3/pay/transactions/app"
    
    /**
     * 使用V3版本API开关
     * 注意：调起支付的签名算法取决于是否有商户私钥
     * - 有私钥：使用RSA签名
     * - 无私钥：使用MD5签名（兼容模式）
     */
    const val USE_V3_API = true // 使用V3版本API
    
    /**
     * 订阅计划配置
     */
    object SubscriptionPlans {
        const val MONTHLY_PRICE = 12 // 元
        const val QUARTERLY_PRICE = 30 // 元
        const val YEARLY_PRICE = 98 // 元
        
        const val MONTHLY_DURATION = 30L * 24 * 60 * 60 * 1000 // 30天
        const val QUARTERLY_DURATION = 90L * 24 * 60 * 60 * 1000 // 90天
        const val YEARLY_DURATION = 365L * 24 * 60 * 60 * 1000 // 365天
    }
    
    /**
     * 开发模式开关
     * 开发模式下使用模拟支付参数，生产环境下使用真实的统一下单接口
     */
    const val DEVELOPMENT_MODE = true

    /**
     * 调试模式开关
     */
    const val IS_DEBUG = true
    
    /**
     * 检查支付配置是否完整
     */
    fun isConfigured(): Boolean {
        return MCH_ID != "1234567890" && API_KEY != "YOUR_API_KEY_HERE"
    }
    
    /**
     * 检查V3 API配置是否完整
     */
    fun isV3Configured(): Boolean {
        return API_V3_KEY.length == 32 && 
               CERT_SERIAL_NO != "your_cert_serial_number_here" &&
               CERT_SERIAL_NO.length > 10
    }
    
    /**
     * 获取当前使用的API版本信息
     */
    fun getApiVersionInfo(): String {
        return if (USE_V3_API) {
            if (DEVELOPMENT_MODE) {
                "V3 API (开发模式)"
            } else {
                "V3 API (生产模式) - ${if (isV3Configured()) "已配置" else "需要配置"}"
            }
        } else {
            if (DEVELOPMENT_MODE) {
                "V2 API (开发模式)"
            } else {
                "V2 API (生产模式) - ${if (isConfigured()) "已配置" else "需要配置"}"
            }
        }
    }
}