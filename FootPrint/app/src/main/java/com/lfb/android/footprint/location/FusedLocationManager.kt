package com.lfb.android.footprint.location

import android.Manifest
import android.annotation.SuppressLint
import android.content.Context
import android.content.pm.PackageManager
import android.location.Location
import android.os.Build
import android.os.Looper
import androidx.core.content.ContextCompat
import com.google.android.gms.common.api.ApiException
import com.google.android.gms.common.api.ResolvableApiException
import com.google.android.gms.location.*
import com.lfb.android.footprint.Manager.LocalLogManager
import com.lfb.android.footprint.geofence.GooglePlayServicesChecker
import com.lfb.android.footprint.prefs.AppPrefs
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow

/**
 * FusedLocationProvider 管理器
 * 封装 Google Play Services 的 FusedLocationProviderClient
 * 提供三种定位模式：普通、省电、耗电
 */
class FusedLocationManager private constructor(private val context: Context) {

    companion object {
        @Volatile
        private var INSTANCE: FusedLocationManager? = null

        fun getInstance(context: Context): FusedLocationManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: FusedLocationManager(context.applicationContext).also { INSTANCE = it }
            }
        }
    }

    private val fusedLocationClient: FusedLocationProviderClient by lazy {
        LocationServices.getFusedLocationProviderClient(context)
    }

    private val localLogManager by lazy {
        LocalLogManager.getInstance(context)
    }

    // 定位更新状态
    private val _isLocationUpdating = MutableStateFlow(false)
    val isLocationUpdating = _isLocationUpdating.asStateFlow()

    // 定位精度状态
    private val _locationAccuracy = MutableStateFlow(0f)
    val locationAccuracy = _locationAccuracy.asStateFlow()

    // 最后一次定位
    private var lastLocation: Location? = null

    // 定位回调
    private var locationCallback: LocationCallback? = null

    // 错误回调 - 用于通知 LocationManager 切换到系统定位
    private var onProviderFailedCallback: (() -> Unit)? = null

    /**
     * 设置提供者失败回调
     */
    fun setOnProviderFailedCallback(callback: () -> Unit) {
        onProviderFailedCallback = callback
    }

    /**
     * 检查是否有必需的权限
     */
    private fun hasRequiredPermissions(): Boolean {
        return ContextCompat.checkSelfPermission(
            context,
            Manifest.permission.ACCESS_FINE_LOCATION
        ) == PackageManager.PERMISSION_GRANTED &&
        ContextCompat.checkSelfPermission(
            context,
            Manifest.permission.ACCESS_COARSE_LOCATION
        ) == PackageManager.PERMISSION_GRANTED &&
        (Build.VERSION.SDK_INT < Build.VERSION_CODES.Q ||
         ContextCompat.checkSelfPermission(
             context,
             Manifest.permission.ACCESS_BACKGROUND_LOCATION
         ) == PackageManager.PERMISSION_GRANTED)
    }

    /**
     * 创建定位请求配置
     * 根据运行模式返回不同的配置
     */
    private fun createLocationRequest(runningModel: Int): LocationRequest {
        // 根据运行模式设置参数
        val (priority, intervalMillis, minUpdateDistanceMeters) = when (runningModel) {
            1 -> {
                // 耗电模式：高精度
                Triple(
                    Priority.PRIORITY_HIGH_ACCURACY,
                    1000L,  // 1秒
                    20f     // 20米
                )
            }
            2 -> {
                // 省电模式：低功耗
                Triple(
                    Priority.PRIORITY_LOW_POWER,
                    2000L,  // 2秒
                    200f    // 200米
                )
            }
            else -> {
                // 普通模式（默认）：平衡精度和功耗
                Triple(
                    Priority.PRIORITY_BALANCED_POWER_ACCURACY,
                    1200L,  // 1.2秒
                    100f    // 100米
                )
            }
        }

        localLogManager.logSync(
            "FusedLocationManager: 创建定位请求 - " +
            "模式: ${getModeName(runningModel)}, " +
            "优先级: ${getPriorityName(priority)}, " +
            "间隔: ${intervalMillis}ms, " +
            "最小距离: ${minUpdateDistanceMeters}m"
        )

        return LocationRequest.Builder(priority, intervalMillis)
            .setMinUpdateDistanceMeters(minUpdateDistanceMeters)
            .setWaitForAccurateLocation(false)
            .setMaxUpdateDelayMillis(intervalMillis)
            .build()
    }

    /**
     * 获取模式名称
     */
    private fun getModeName(runningModel: Int): String {
        return when (runningModel) {
            1 -> "耗电模式"
            2 -> "省电模式"
            else -> "普通模式"
        }
    }

    /**
     * 获取优先级名称
     */
    private fun getPriorityName(priority: Int): String {
        return when (priority) {
            Priority.PRIORITY_HIGH_ACCURACY -> "高精度"
            Priority.PRIORITY_BALANCED_POWER_ACCURACY -> "平衡精度"
            Priority.PRIORITY_LOW_POWER -> "低功耗"
            Priority.PRIORITY_PASSIVE -> "被动"
            else -> "未知"
        }
    }

    /**
     * 开始定位更新
     * @param onLocationUpdate 定位更新回调
     */
    @SuppressLint("MissingPermission")
    fun startLocationUpdates(onLocationUpdate: (Location) -> Unit) {
        if (!hasRequiredPermissions()) {
            localLogManager.logSync("FusedLocationManager: 权限不足，无法开始定位更新")
            return
        }

        try {
            // 停止现有的定位更新
            stopLocationUpdates()

            // 获取当前运行模式
            val runningModel = AppPrefs.sharedInstance.runningModel
            
            // 创建定位请求
            val locationRequest = createLocationRequest(runningModel)

            // 创建定位回调
            locationCallback = object : LocationCallback() {
                override fun onLocationResult(locationResult: LocationResult) {
                    locationResult.lastLocation?.let { location ->
                        lastLocation = location

                        // 更新定位精度状态
                        _locationAccuracy.value = location.accuracy

                        localLogManager.logSync(
                            "FusedLocationManager: 收到定位更新 - " +
                            "位置: (${location.latitude}, ${location.longitude}), " +
                            "精度: ${location.accuracy}m, " +
                            "速度: ${location.speed}m/s, " +
                            "时间: ${location.time}"
                        )

                        // 回调定位更新
                        onLocationUpdate(location)
                    }
                }

                override fun onLocationAvailability(availability: LocationAvailability) {
                    localLogManager.logSync(
                        "FusedLocationManager: 定位可用性变化 - ${availability.isLocationAvailable}"
                    )
                }
            }

            // 请求定位更新
            fusedLocationClient.requestLocationUpdates(
                locationRequest,
                locationCallback!!,
                Looper.getMainLooper()
            ).addOnSuccessListener {
                _isLocationUpdating.value = true
                localLogManager.logSync("FusedLocationManager: 定位更新已开始")
            }.addOnFailureListener { exception ->
                localLogManager.logSync("FusedLocationManager: 开始定位更新失败 - ${exception.message}")
                _isLocationUpdating.value = false

                // 检查是否是 Google Play Services 不可用导致的失败
                if (shouldSwitchToSystemProvider(exception)) {
                    localLogManager.logSync("FusedLocationManager: Google Play Services 不可用，需要切换到系统定位")
                    onProviderFailedCallback?.invoke()
                }
            }

        } catch (e: Exception) {
            localLogManager.logSync("FusedLocationManager: 开始定位更新异常 - ${e.message}")
            _isLocationUpdating.value = false

            // 检查是否是 Google Play Services 不可用导致的异常
            if (shouldSwitchToSystemProvider(e)) {
                localLogManager.logSync("FusedLocationManager: Google Play Services 不可用，需要切换到系统定位")
                onProviderFailedCallback?.invoke()
            }
        }
    }

    /**
     * 停止定位更新
     */
    fun stopLocationUpdates() {
        try {
            locationCallback?.let { callback ->
                fusedLocationClient.removeLocationUpdates(callback)
                    .addOnSuccessListener {
                        localLogManager.logSync("FusedLocationManager: 定位更新已停止")
                    }
                    .addOnFailureListener { exception ->
                        localLogManager.logSync("FusedLocationManager: 停止定位更新失败 - ${exception.message}")
                    }
            }

            locationCallback = null
            _isLocationUpdating.value = false
            _locationAccuracy.value = 0f

        } catch (e: Exception) {
            localLogManager.logSync("FusedLocationManager: 停止定位更新异常 - ${e.message}")
        }
    }

    /**
     * 获取最后一次定位
     */
    @SuppressLint("MissingPermission")
    fun getLastLocation(onSuccess: (Location?) -> Unit) {
        if (!hasRequiredPermissions()) {
            localLogManager.logSync("FusedLocationManager: 权限不足，无法获取最后定位")
            onSuccess(null)
            return
        }

        try {
            fusedLocationClient.lastLocation
                .addOnSuccessListener { location ->
                    lastLocation = location
                    localLogManager.logSync(
                        "FusedLocationManager: 获取最后定位成功 - " +
                        if (location != null) {
                            "(${location.latitude}, ${location.longitude})"
                        } else {
                            "无定位数据"
                        }
                    )
                    onSuccess(location)
                }
                .addOnFailureListener { exception ->
                    localLogManager.logSync("FusedLocationManager: 获取最后定位失败 - ${exception.message}")
                    onSuccess(null)
                }
        } catch (e: Exception) {
            localLogManager.logSync("FusedLocationManager: 获取最后定位异常 - ${e.message}")
            onSuccess(null)
        }
    }

    /**
     * 判断是否应该切换到系统定位提供者
     * 只有当确认是 Google Play Services 不可用时才返回 true
     */
    private fun shouldSwitchToSystemProvider(exception: Exception): Boolean {
        // 首先检查 Google Play Services 是否真的不可用
        if (!GooglePlayServicesChecker.isGooglePlayServicesAvailable(context)) {
            localLogManager.logSync("FusedLocationManager: 确认 Google Play Services 不可用")
            return true
        }

        // 检查异常类型
        when (exception) {
            is ApiException -> {
                // Google Play Services API 异常
                localLogManager.logSync("FusedLocationManager: ApiException - statusCode: ${exception.statusCode}")

                // 常见的 Google Play Services 不可用错误码
                // 参考: https://developers.google.com/android/reference/com/google/android/gms/common/api/CommonStatusCodes
                return when (exception.statusCode) {
                    2 -> true  // SERVICE_VERSION_UPDATE_REQUIRED
                    9 -> true  // SERVICE_INVALID
                    18 -> true // SERVICE_UPDATING
                    else -> {
                        // 其他错误不切换，可能是临时性问题
                        localLogManager.logSync("FusedLocationManager: 其他 API 错误，不切换定位提供者")
                        false
                    }
                }
            }
            is SecurityException -> {
                // 权限问题，不是 Google Play Services 的问题
                localLogManager.logSync("FusedLocationManager: 权限异常，不切换定位提供者")
                return false
            }
            is IllegalStateException -> {
                // 可能是 Google Play Services 不可用
                localLogManager.logSync("FusedLocationManager: IllegalStateException，检查是否需要切换")
                return !GooglePlayServicesChecker.isGooglePlayServicesAvailable(context)
            }
            else -> {
                // 其他异常，再次确认 Google Play Services 状态
                localLogManager.logSync("FusedLocationManager: 未知异常类型: ${exception.javaClass.simpleName}")
                return !GooglePlayServicesChecker.isGooglePlayServicesAvailable(context)
            }
        }
    }

    /**
     * 获取当前定位更新状态
     */
    fun isUpdating(): Boolean {
        return _isLocationUpdating.value
    }

    /**
     * 获取最后一次定位（同步）
     */
    fun getLastLocationSync(): Location? {
        return lastLocation
    }
}

