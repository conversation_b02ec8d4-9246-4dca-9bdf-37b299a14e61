package com.lfb.android.footprint.worker

import android.content.Context
import androidx.work.CoroutineWorker
import androidx.work.WorkerParameters
import androidx.work.PeriodicWorkRequestBuilder
import androidx.work.WorkManager
import androidx.work.ExistingPeriodicWorkPolicy
import com.lfb.android.footprint.Manager.MembershipCheckManager
import com.lfb.android.footprint.Manager.LocalLogManager
import java.util.concurrent.TimeUnit
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlin.coroutines.resume

/**
 * 会员状态定期检查Worker
 * 使用WorkManager实现后台定期检查
 */
class MembershipCheckWorker(
    context: Context,
    params: WorkerParameters
) : CoroutineWorker(context, params) {

    companion object {
        private const val TAG = "MembershipCheckWorker"
        private const val WORK_NAME = "membership_check_work"
        
        // 检查间隔：3天
        private const val CHECK_INTERVAL_DAYS = 3L
        
        /**
         * 调度定期会员检查任务
         */
        fun schedule(context: Context) {
            val workRequest = PeriodicWorkRequestBuilder<MembershipCheckWorker>(
                CHECK_INTERVAL_DAYS, TimeUnit.DAYS
            ).build()
            
            WorkManager.getInstance(context).enqueueUniquePeriodicWork(
                WORK_NAME,
                ExistingPeriodicWorkPolicy.KEEP, // 保持现有任务
                workRequest
            )
            
            LocalLogManager.getInstance(context).logSync("MembershipCheckWorker: 定期会员检查任务已调度")
        }
        
        /**
         * 取消定期会员检查任务
         */
        fun cancel(context: Context) {
            WorkManager.getInstance(context).cancelUniqueWork(WORK_NAME)
            LocalLogManager.getInstance(context).logSync("MembershipCheckWorker: 定期会员检查任务已取消")
        }
    }

    override suspend fun doWork(): Result {
        return try {
            LocalLogManager.getInstance(applicationContext).logSync("MembershipCheckWorker: 开始执行会员状态检查")
            
            val membershipCheckManager = MembershipCheckManager.getInstance(applicationContext)
            
            // 使用suspendCancellableCoroutine将回调转换为挂起函数
            val checkResult = suspendCancellableCoroutine<CheckResult> { continuation ->
                membershipCheckManager.checkMembershipStatus(
                    onResult = { success, needRelogin, message ->
                        continuation.resume(CheckResult(success, needRelogin, message))
                    },
                    onVersionCheck = null  // Worker 中不处理版本检查，由 Activity 处理
                )
            }
            
            LocalLogManager.getInstance(applicationContext).logSync(
                "MembershipCheckWorker: 检查完成 - success=${checkResult.success}, needRelogin=${checkResult.needRelogin}, message=${checkResult.message}"
            )
            
            if (checkResult.needRelogin) {
                // 需要重新登录，可以发送通知
                LocalLogManager.getInstance(applicationContext).logSync("MembershipCheckWorker: 检测到需要重新登录")
            }
            
            Result.success()
            
        } catch (e: Exception) {
            LocalLogManager.getInstance(applicationContext).logSync("MembershipCheckWorker: 执行失败 - ${e.message}")
            Result.retry()
        }
    }
    
    private data class CheckResult(
        val success: Boolean,
        val needRelogin: Boolean,
        val message: String
    )
}
