package com.lfb.android.footprint.ui.components.mapScreen

import androidx.compose.animation.core.*
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.*
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.lfb.android.footprint.ui.theme.rememberMapThemeConfig
import java.util.Calendar
import kotlin.math.abs

/**
 * 24小时折线图 - 一比一迁移iOS LHYChartView
 * 参考: stepofmyworld/StepOfMyWorld/thirdpart/charView/LHYChartView.m
 */
@Composable
fun HourlyChartView(
    distanceData: FloatArray,
    altitudeData: FloatArray,
    modifier: Modifier = Modifier
) {
    val themeConfig = rememberMapThemeConfig()
    var selectedHour by remember { mutableStateOf<Int?>(null) }
    var animationProgress by remember { mutableStateOf(0f) }
    var popupOffsetX by remember { mutableStateOf(0f) }
    var chartWidth by remember { mutableStateOf(0f) }
    
    val density = LocalDensity.current
    
    // 获取当前小时，默认选中
    val currentHour = remember {
        Calendar.getInstance().get(Calendar.HOUR_OF_DAY)
    }
    
    // 初始化时选中当前小时
    LaunchedEffect(Unit) {
        selectedHour = currentHour
    }
    
    // 动画效果 (iOS: duration 1.5s, easeInEaseOut)
    LaunchedEffect(distanceData, altitudeData) {
        animate(
            initialValue = 0f,
            targetValue = 1f,
            animationSpec = tween(durationMillis = 1500, easing = FastOutSlowInEasing)
        ) { value, _ ->
            animationProgress = value
        }
    }
    
    Box(modifier = modifier) {
        Canvas(
            modifier = Modifier
                .fillMaxSize()
                .pointerInput(Unit) {
                    detectTapGestures { offset ->
                        // 计算点击的小时
                        val hourWidth = size.width / 24f
                        val hour = (offset.x / hourWidth).toInt().coerceIn(0, 23)
                        selectedHour = hour
                        // 计算气泡位置（iOS: paopao_x = index * xMargin + xMargin - size.width * 0.5）
                        popupOffsetX = offset.x
                        chartWidth = size.width * 1f
                    }
                }
        ) {
            val width = size.width
            val height = size.height
            val paddingBottom = 30f
            val paddingTop = 50f  // 为数据显示留出空间
            val paddingLineTop = 0f

            val paddingLeft = 40f  // iOS: titleWOfY 左侧Y轴标签宽度
            val paddingRight = 40f  // iOS: titleWOfY 右侧Y轴标签宽度
            val chartWidthValue = width - paddingLeft - paddingRight
            val chartHeight = height - paddingTop - paddingBottom
            
            // 更新chartWidth供气泡使用
            chartWidth = width
            
            // 计算数据范围（使用海拔数据，只考虑非0值）
            val nonZeroAltitudes = altitudeData.filter { it > 0f }
            val maxAltitude = nonZeroAltitudes.maxOrNull() ?: 1f
            val minAltitude = nonZeroAltitudes.minOrNull() ?: 0f
            
            // 判断所有值是否相同
            val allAltitudesSame = maxAltitude == minAltitude && maxAltitude > 0
            
            // 如果所有值都相同，创建一个以该值为中心的范围
            val (altitudeMin, altitudeMax) = if (allAltitudesSame) {
                val center = maxAltitude
                val range = center * 0.2f  // 上下各10%的范围
                Pair(center - range, center + range)
            } else if (maxAltitude > minAltitude) {
                Pair(minAltitude, maxAltitude)
            } else {
                Pair(0f, 1f)
            }
            val altitudeRange = altitudeMax - altitudeMin
            
            // 计算距离数据范围（iOS: leftDataArr）
            val maxDistance = distanceData.maxOrNull()?.takeIf { it > 0f } ?: 1f
            val minDistance = 0f
            val distanceRange = if (maxDistance > 0f) maxDistance else 1f
            
            // iOS: addLines1With - 绘制坐标轴和箭头
            val yAxisSteps = 4  // iOS: _row
//            val axisColor = Color(0xFFE0E0E0)  // iOS: borderLineColor
            val axisColor = Color(0xFFA1A1A1)  // iOS: borderLineColor
            val axisBottomY = paddingTop + chartHeight
            
            // 绘制X轴时间标签（只显示奇数小时，iOS: i % 2 == 1）
            for (i in listOf(1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23)) {
                val x = paddingLeft + (chartWidthValue / 24f) * i
                drawContext.canvas.nativeCanvas.apply {
                    drawText(
                        "$i",
                        x,
                        axisBottomY + 50f,  // X轴下方25像素处显示标签
                        android.graphics.Paint().apply {
                            color = android.graphics.Color.parseColor("#888888")
                            textSize = 24f
                            textAlign = android.graphics.Paint.Align.CENTER
                            isAntiAlias = true
                        }
                    )
                }
            }
            
            // iOS: isShowYLine - 绘制Y轴（左侧竖线 + 顶部箭头）
            // 竖线：从底部到顶部
            drawLine(
                color = axisColor,
                start = Offset(paddingLeft, axisBottomY),
                end = Offset(paddingLeft, paddingLineTop),
                strokeWidth = 1f
            )
            // 顶部箭头（三角形）
            val arrowPath = Path().apply {
                moveTo(paddingLeft, paddingLineTop)  // 箭头顶点
                lineTo(paddingLeft - 10f, paddingLineTop + 10f)  // 左边
                lineTo(paddingLeft + 10f, paddingLineTop + 10f)  // 右边
                close()
            }
            drawPath(
                path = arrowPath,
                color = axisColor
            )
            
            // iOS: isShowXLine - 绘制X轴（底部横线 + 右侧箭头）
            val xAxisEndX = width - paddingRight
            // 横线：从左到右
            drawLine(
                color = axisColor,
                start = Offset(paddingLeft, axisBottomY),
                end = Offset(xAxisEndX, axisBottomY),
                strokeWidth = 1f
            )
            // 右侧箭头（三角形）
            val xArrowPath = Path().apply {
                moveTo(xAxisEndX, axisBottomY)  // 箭头顶点
                lineTo(xAxisEndX - 10f, axisBottomY - 10f)  // 上边
                lineTo(xAxisEndX - 10f, axisBottomY + 10f)  // 下边
                close()
            }
            drawPath(
                path = xArrowPath,
                color = axisColor
            )
            
            // iOS: addLeftViews - 绘制左侧Y轴标签（距离数据）
            for (i in 0 until yAxisSteps) {
                if (i == 0) {
                    continue
                }
                val value = minDistance + (distanceRange / (yAxisSteps - 1)) * i
                val y = axisBottomY - (chartHeight / (yAxisSteps - 1)) * i
                drawContext.canvas.nativeCanvas.apply {
                    drawText(
                        String.format("%.0f", value),
                        paddingLeft - 8f,
                        y + 5f,  // 调整文字垂直居中对齐
                        android.graphics.Paint().apply {
                            color = android.graphics.Color.parseColor("#888888")
                            textSize = 16f
                            textAlign = android.graphics.Paint.Align.RIGHT
                            isAntiAlias = true
                        }
                    )
                }
            }
            
            // iOS: addRightViews - 绘制右侧Y轴标签（海拔数据）
            for (i in 0 until yAxisSteps) {
                val value = altitudeMin + (altitudeRange / (yAxisSteps - 1)) * i
                val y = axisBottomY - (chartHeight / (yAxisSteps - 1)) * i
                drawContext.canvas.nativeCanvas.apply {
                    drawText(
                        String.format("%.0f", value),
                        width - paddingRight + 8f,
                        y + 5f,  // 调整文字垂直居中对齐
                        android.graphics.Paint().apply {
                            color = android.graphics.Color.parseColor("#888888")
                            textSize = 16f
                            textAlign = android.graphics.Paint.Align.LEFT
                            isAntiAlias = true
                        }
                    )
                }
            }
            
            // 绘制海拔曲线（iOS: rightDataArr，使用贝塞尔曲线，渐变填充）
            val hasAltitudeData = altitudeData.any { it > 0f }
            if (hasAltitudeData && altitudeRange > 0) {
                val altitudePath = Path()
                val altitudeGradientPath = Path()
                
                // 计算所有点的坐标
                val altitudePoints = mutableListOf<Offset>()
                for (i in 0..23) {
                    val x = paddingLeft + (chartWidthValue / 24f) * i
                    val normalizedValue = if (altitudeData[i] > 0) {
                        ((altitudeData[i] - altitudeMin) / altitudeRange).coerceIn(0f, 1f)
                    } else {
                        0f
                    }
                    val y = paddingTop + chartHeight - (normalizedValue * chartHeight * animationProgress)
                    altitudePoints.add(Offset(x, y))
                }
                
                // 绘制贝塞尔曲线（iOS: addBezierPoint）
                if (altitudePoints.isNotEmpty()) {
                    altitudePath.moveTo(altitudePoints[0].x, altitudePoints[0].y)
                    altitudeGradientPath.moveTo(altitudePoints[0].x, height - paddingBottom)
                    altitudeGradientPath.lineTo(altitudePoints[0].x, altitudePoints[0].y)
                    
                    for (i in 1 until altitudePoints.size) {
                        val prePoint = altitudePoints[i - 1]
                        val nowPoint = altitudePoints[i]
                        
                        // iOS贝塞尔曲线控制点
                        val controlX = (nowPoint.x + prePoint.x) / 2
                        
                        altitudePath.cubicTo(
                            controlX, prePoint.y,
                            controlX, nowPoint.y,
                            nowPoint.x, nowPoint.y
                        )
                        
                        altitudeGradientPath.cubicTo(
                            controlX, prePoint.y,
                            controlX, nowPoint.y,
                            nowPoint.x, nowPoint.y
                        )
                    }
                    
                    // 完成渐变路径
                    altitudeGradientPath.lineTo(altitudePoints.last().x, height - paddingBottom)
                    altitudeGradientPath.lineTo(altitudePoints.first().x, height - paddingBottom)
                    altitudeGradientPath.close()
                    
                    // 绘制渐变填充（海拔 - 青绿色 #6dd89c）
                    drawPath(
                        path = altitudeGradientPath,
                        brush = Brush.verticalGradient(
                            colors = listOf(
                                Color(0xFF6DD89C).copy(alpha = 0.6f),
                                Color(0xFF6DD89C).copy(alpha = 0.4f),
                                Color(0xFF6DD89C).copy(alpha = 0.2f),
                                Color(0xFF6DD89C).copy(alpha = 0.05f)
                            ),
                            startY = paddingTop,
                            endY = height - paddingBottom
                        )
                    )
                    
                    // 绘制曲线边框
                    drawPath(
                        path = altitudePath,
                        color = Color(0xFF6DD89C),
                        style = Stroke(width = 4f, cap = StrokeCap.Round)
                    )
                }
            }
            
            // 绘制距离曲线（iOS: leftDataArr，使用贝塞尔曲线，渐变填充）
            val hasDistanceData = distanceData.any { it > 0f }
            if (hasDistanceData && distanceRange > 0) {
                val distancePath = Path()
                val distanceGradientPath = Path()
                
                // 计算所有点的坐标
                val distancePoints = mutableListOf<Offset>()
                for (i in 0..23) {
                    val x = paddingLeft + (chartWidthValue / 24f) * i
                    val normalizedValue = if (distanceData[i] > 0) {
                        ((distanceData[i] - minDistance) / distanceRange).coerceIn(0f, 1f)
                    } else {
                        0f
                    }
                    val y = paddingTop + chartHeight - (normalizedValue * chartHeight * animationProgress)
                    distancePoints.add(Offset(x, y))
                }
                
                // 绘制贝塞尔曲线
                if (distancePoints.isNotEmpty()) {
                    distancePath.moveTo(distancePoints[0].x, distancePoints[0].y)
                    distanceGradientPath.moveTo(distancePoints[0].x, height - paddingBottom)
                    distanceGradientPath.lineTo(distancePoints[0].x, distancePoints[0].y)
                    
                    for (i in 1 until distancePoints.size) {
                        val prePoint = distancePoints[i - 1]
                        val nowPoint = distancePoints[i]
                        
                        val controlX = (nowPoint.x + prePoint.x) / 2
                        
                        distancePath.cubicTo(
                            controlX, prePoint.y,
                            controlX, nowPoint.y,
                            nowPoint.x, nowPoint.y
                        )
                        
                        distanceGradientPath.cubicTo(
                            controlX, prePoint.y,
                            controlX, nowPoint.y,
                            nowPoint.x, nowPoint.y
                        )
                    }
                    
                    // 完成渐变路径
                    distanceGradientPath.lineTo(distancePoints.last().x, height - paddingBottom)
                    distanceGradientPath.lineTo(distancePoints.first().x, height - paddingBottom)
                    distanceGradientPath.close()
                    
                    // 绘制渐变填充（距离 - 蓝色 #00a1eb）
                    drawPath(
                        path = distanceGradientPath,
                        brush = Brush.verticalGradient(
                            colors = listOf(
                                Color(0xFF00A1EB).copy(alpha = 0.6f),
                                Color(0xFF00A1EB).copy(alpha = 0.4f),
                                Color(0xFF00A1EB).copy(alpha = 0.2f),
                                Color(0xFF00A1EB).copy(alpha = 0.05f)
                            ),
                            startY = paddingTop,
                            endY = height - paddingBottom
                        )
                    )
                    
                    // 绘制曲线边框
                    drawPath(
                        path = distancePath,
                        color = Color(0xFF00A1EB),
                        style = Stroke(width = 4f, cap = StrokeCap.Round)
                    )
                }
            }
            
            // 绘制选中小时的指示器（iOS: drawCircle方法，在所有折线上绘制圆圈）
            selectedHour?.let { hour ->
                val x = paddingLeft + (chartWidthValue / 24f) * hour
                // 更新气泡位置
                popupOffsetX = x
                
                // iOS: 在距离折线上绘制圆圈（leftPointArr）
                // 即使数据为0也显示圆圈（在底部）
                if (distanceRange > 0) {
                    val normalizedValue = if (distanceData[hour] > 0) {
                        ((distanceData[hour] - minDistance) / distanceRange).coerceIn(0f, 1f)
                    } else {
                        0f  // 数据为0时，圆圈显示在底部
                    }
                    val y = paddingTop + chartHeight - (normalizedValue * chartHeight)
                    
                    // iOS: circleView.backgroundColor = [UIColor whiteColor]
                    // iOS: circleView.layer.borderColor = colors[i].CGColor
                    // iOS: circleView.layer.borderWidth = 2
                    drawCircle(
                        color = Color.White,
                        radius = 10f,
                        center = Offset(x, y)
                    )
                    drawCircle(
                        color = Color(0xFF00A1EB),
                        radius = 7f,
                        center = Offset(x, y)
                    )
                }
                
                // iOS: 在海拔折线上绘制圆圈（rightPointArr）
                // 即使数据为0也显示圆圈（在底部）
                if (altitudeRange > 0) {
                    val normalizedValue = if (altitudeData[hour] > 0) {
                        ((altitudeData[hour] - altitudeMin) / altitudeRange).coerceIn(0f, 1f)
                    } else {
                        0f  // 数据为0时，圆圈显示在底部
                    }
                    val y = paddingTop + chartHeight - (normalizedValue * chartHeight)
                    
                    // iOS: KCircleRadius1 = 5, 外圈白色，内圈颜色
                    drawCircle(
                        color = Color.White,
                        radius = 10f,
                        center = Offset(x, y)
                    )
                    drawCircle(
                        color = Color(0xFF6DD89C),
                        radius = 7f,
                        center = Offset(x, y)
                    )
                }
            }
        }
        
        // 显示选中小时的数据（iOS: LHYLinesPaoPaoView气泡）
        selectedHour?.let { hour ->
            // 计算气泡宽度和位置（iOS: paopao_x = index * xMargin + xMargin - size.width * 0.5）
            val popupWidth = 140.dp
            val popupWidthPx = with(density) { popupWidth.toPx() }
            
            // 计算气泡X位置，确保不超出边界
            var offsetX = popupOffsetX - popupWidthPx / 2f
            if (offsetX < 0f) {
                offsetX = 8f
            } else if (offsetX + popupWidthPx > chartWidth) {
                offsetX = chartWidth - popupWidthPx - 8f
            }
            
            // iOS: paopaoView with rounded corners, shadow, and title
            Column(
                modifier = Modifier
                    .offset(x = with(density) { offsetX.toDp() }, y = 8.dp)
                    .width(popupWidth)
                    .graphicsLayer {
                        shadowElevation = 8f
                        shape = RoundedCornerShape(8.dp)
                        clip = true
                    }
                    .background(
                        // iOS: [UIColor colorWithHexString:@"#000000" andAlpha:0.85]
                        color = themeConfig.normalBackgroundColor,
                        shape = RoundedCornerShape(8.dp)
                    )
                    .padding(horizontal = 12.dp, vertical = 6.dp)
            ) {
                // iOS: title in paopaoView header (paopaoTitleArray format: "%d时-%d时")
                Text(
                    text = "${hour}时-${(hour + 1) % 24}时",
                    style = TextStyle(
                        color = Color(0xFFD3D3D3),  // iOS: #d3d3d3
                        fontSize = 11.sp,
                        fontWeight = FontWeight.Bold  // iOS: Helvetica-Bold
                    )
                )
                Spacer(modifier = Modifier.height(6.dp))
                // iOS: disTextArr format: "轨迹距离%.1f米"
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "轨迹距离",
                        style = TextStyle(
                            color = Color(0xFF00A1EB),  // iOS: 颜色1 #00a1eb
                            fontSize = 10.sp
                        )
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        text = String.format("%.1f米", distanceData[hour]),
                        style = TextStyle(
                            color = Color(0xFF00A1EB),
                            fontSize = 10.sp,
                            fontWeight = FontWeight.Medium
                        )
                    )
                }
                Spacer(modifier = Modifier.height(4.dp))
                // iOS: altitudeTextArr format: "海拔高度%.1f米"
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "海拔高度",
                        style = TextStyle(
                            color = Color(0xFF6DD89C),  // iOS: 颜色2 #6dd89c
                            fontSize = 10.sp
                        )
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        text = String.format("%.1f米", altitudeData[hour]),
                        style = TextStyle(
                            color = Color(0xFF6DD89C),
                            fontSize = 10.sp,
                            fontWeight = FontWeight.Medium
                        )
                    )
                }
            }
        }
    }
}
