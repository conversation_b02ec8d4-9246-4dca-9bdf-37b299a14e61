package com.lfb.android.footprint.Manager

import android.content.Context
import android.content.Intent
import android.net.Uri
import android.util.Log

/**
 * 版本管理器
 * 负责版本检查和更新提示
 */
object VersionManager {
    
    private const val TAG = "VersionManager"
    
    /**
     * 获取当前应用版本号
     */
    fun getCurrentVersionCode(context: Context): Int {
        return try {
            val packageInfo = context.packageManager.getPackageInfo(context.packageName, 0)
            @Suppress("DEPRECATION")
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.P) {
                packageInfo.longVersionCode.toInt()
            } else {
                packageInfo.versionCode
            }
        } catch (e: Exception) {
            Log.e(TAG, "获取版本号失败", e)
            1
        }
    }
    
    /**
     * 获取当前应用版本名称
     */
    fun getCurrentVersionName(context: Context): String {
        return try {
            val packageInfo = context.packageManager.getPackageInfo(context.packageName, 0)
            packageInfo.versionName ?: "1.0"
        } catch (e: Exception) {
            Log.e(TAG, "获取版本名称失败", e)
            "1.0"
        }
    }
    
    /**
     * 检查版本更新
     * @param context Context
     * @param latestVersionCode 最新版本号
     * @param minVersionCode 最低可用版本号
     * @param ignoredVersionCode 被忽略的版本号
     * @return UpdateCheckResult 更新检查结果
     */
    fun checkUpdate(
        context: Context, 
        latestVersionCode: Int, 
        minVersionCode: Int,
        ignoredVersionCode: Int = 0
    ): UpdateCheckResult {
        val currentVersion = getCurrentVersionCode(context)
        
        Log.d(TAG, "版本检查: 当前版本=$currentVersion, 最新版本=$latestVersionCode, 最低版本=$minVersionCode, 忽略版本=$ignoredVersionCode")
        
        return when {
            currentVersion < minVersionCode -> {
                // 强制更新
                UpdateCheckResult.ForceUpdate(
                    currentVersion = currentVersion,
                    latestVersion = latestVersionCode,
                    minVersion = minVersionCode
                )
            }
            currentVersion < latestVersionCode -> {
                // 检查是否被忽略
                if (latestVersionCode == ignoredVersionCode) {
                    Log.d(TAG, "版本 $latestVersionCode 已被用户忽略")
                    UpdateCheckResult.NoUpdate(currentVersion = currentVersion)
                } else {
                    // 可选更新
                    UpdateCheckResult.OptionalUpdate(
                        currentVersion = currentVersion,
                        latestVersion = latestVersionCode
                    )
                }
            }
            else -> {
                // 无需更新
                UpdateCheckResult.NoUpdate(currentVersion = currentVersion)
            }
        }
    }
    
    /**
     * 打开下载链接
     */
    fun openDownloadUrl(context: Context, downloadUrl: String) {
        try {
            val intent = Intent(Intent.ACTION_VIEW, Uri.parse(downloadUrl))
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            context.startActivity(intent)
            Log.d(TAG, "打开下载链接: $downloadUrl")
        } catch (e: Exception) {
            Log.e(TAG, "打开下载链接失败", e)
        }
    }
    
    /**
     * 更新检查结果
     */
    sealed class UpdateCheckResult {
        /**
         * 无需更新
         */
        data class NoUpdate(val currentVersion: Int) : UpdateCheckResult()
        
        /**
         * 可选更新（可以取消）
         */
        data class OptionalUpdate(
            val currentVersion: Int,
            val latestVersion: Int
        ) : UpdateCheckResult()
        
        /**
         * 强制更新（必须更新）
         */
        data class ForceUpdate(
            val currentVersion: Int,
            val latestVersion: Int,
            val minVersion: Int
        ) : UpdateCheckResult()
    }
}
