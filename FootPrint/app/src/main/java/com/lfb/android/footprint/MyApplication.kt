package com.lfb.android.footprint

import android.app.Application
import android.content.Intent
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.ProcessLifecycleOwner
import com.lfb.android.footprint.location.LocationDataRecorder
import com.lfb.android.footprint.prefs.AppPrefs
import com.lfb.android.footprint.ui.theme.MapThemeManager
import io.realm.kotlin.Realm
import io.realm.kotlin.RealmConfiguration
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import android.app.ActivityManager
import android.content.Context
import androidx.core.view.WindowCompat
import com.lfb.android.footprint.Manager.RealmModelManager
import com.lfb.android.footprint.Manager.CityDataManager
import com.lfb.android.footprint.Manager.GlobalVersionUpdateDialog
import com.lfb.android.footprint.Manager.LocalLogManager
import com.lfb.android.footprint.Manager.UserManager
import com.lfb.android.footprint.Manager.VersionManager
import com.lfb.android.footprint.Manager.WechatLoginManager
import com.lfb.android.footprint.utils.ScreenManager
import com.lfb.android.footprint.api.ApiService

class MyApplication : Application() {

    override fun onCreate() {
        super.onCreate()
        AppPrefs.init(this)

        // 初始化主题管理器，确保主题状态与用户配置同步
        MapThemeManager.updateMapDisplayType(AppPrefs.sharedInstance.mapDisplayType)

        // Realm 初始化（传递 Context 以支持磁盘缓存）
        RealmModelManager.getInstance(this)
        
        // 初始化用户管理器
        UserManager.getInstance(this)

        // 初始化API服务
        ApiService.init(this)

        // 初始化全局下拉提示
        com.lfb.android.footprint.Manager.GlobalDropdownAlert.init(this)
        
        // 初始化全局版本更新对话框
        com.lfb.android.footprint.Manager.GlobalVersionUpdateDialog.init(this)

        // 初始化微信SDK
        initializeWechatSDK()
        
        // 启动会员状态定期检查
        initializeMembershipCheck()

        // 应用启动时执行数据迁移
//        migrateLocationData()

        // 初始化保活机制
        if (AppPrefs.sharedInstance.hasShowGuidPage) {
            initializeKeepAlive()

            ProcessLifecycleOwner.get().lifecycle.addObserver(AppLifecycleObserver(this))
        }
    }
    
    /**
     * 初始化微信SDK
     */
    private fun initializeWechatSDK() {
        try {
            // 初始化微信登录管理器
            WechatLoginManager.getInstance(this)
            LocalLogManager.getInstance(this).logSync("MyApplication: 微信SDK初始化成功")
        } catch (e: Exception) {
            LocalLogManager.getInstance(this).logSync("MyApplication: 微信SDK初始化失败 - ${e.message}")
        }
    }
    
    /**
     * 初始化会员状态定期检查
     */
    private fun initializeMembershipCheck() {
        try {
            com.lfb.android.footprint.worker.MembershipCheckWorker.schedule(this)
            LocalLogManager.getInstance(this).logSync("MyApplication: 会员状态定期检查已启动")
        } catch (e: Exception) {
            LocalLogManager.getInstance(this).logSync("MyApplication: 会员状态定期检查启动失败 - ${e.message}")
        }
    }
    
    /**
     * 初始化保活机制
     */
    private fun initializeKeepAlive() {
        try {

            val keepAliveManager = com.lfb.android.footprint.keepalive.KeepAliveManager.getInstance(this)
            val success = keepAliveManager.initializeKeepAlive()
            
            if (success) {
                LocalLogManager.getInstance(this).logSync("MyApplication: 保活机制初始化成功")
            } else {
                LocalLogManager.getInstance(this).logSync("MyApplication: 保活机制初始化失败")
            }
            
            // 初始化所有广播监听器（包括动态注册和网络监听）
            com.lfb.android.footprint.keepalive.SystemBroadcastReceiver.initializeBroadcastListeners(this)
            LocalLogManager.getInstance(this).logSync("MyApplication: 系统广播监听器初始化完成")
            
        } catch (e: Exception) {
            LocalLogManager.getInstance(this).logSync("MyApplication: 保活机制初始化异常 - ${e.message}")
        }
    }

    override fun onTerminate() {
        super.onTerminate()
        LocalLogManager.getInstance(this).logSync("MyApplication: 应用终止")
        // 注意：onTerminate()在真实设备上几乎从不被调用，主要用于调试
        // 对于保活应用，我们不应该在这里清理广播监听器，因为：
        // 1. 静态注册的广播是保活的关键，需要在应用被杀死后继续工作
        // 2. 动态注册的广播监听器在进程被杀死时会自动清理
        // 3. 手动清理可能会干扰保活机制
    }
    
    companion object {
        @Volatile
        private var instance: MyApplication? = null

        fun getInstance(): MyApplication {
            return instance ?: synchronized(this) {
                instance ?: throw IllegalStateException("MyApplication not initialized")
            }
        }
    }
}

/**
 * 应用状态管理器
 * 用于跟踪应用的前台后台状态
 */
object AppStateManager {
    @Volatile
    private var isAppInForeground = false

    fun setAppInForeground(inForeground: Boolean) {
        isAppInForeground = inForeground
    }

    fun isAppInForeground(): Boolean {
        return isAppInForeground
    }
}

class AppLifecycleObserver(private val app: Application) : LifecycleEventObserver {
    override fun onStateChanged(source: LifecycleOwner, event: Lifecycle.Event) {
        when (event) {
            Lifecycle.Event.ON_START -> {
                // 更新应用前台状态
                AppStateManager.setAppInForeground(true)

                // 检查当前是否有Activity在前台，避免干扰其他Activity
                if (!isAnyActivityInForeground()) {
                    // App进入前台，跳转MainActivity（可根据条件修改）
                    val intent = Intent(app, MainMapActivity::class.java).apply {
                        addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                    }
                    app.startActivity(intent)
                }

                LocalLogManager.getInstance(app).logSync("***************************************")
                LocalLogManager.getInstance(app).logSync("***************************************")
                LocalLogManager.getInstance(app).logSync("***************************************")

                LocalLogManager.getInstance(app).logSync("app 进入前台")

                // 检查并刷新用户令牌
                checkAndRefreshUserToken()

                // 检查会员状态
                checkMembershipStatus()

                // 执行数据迁移
                migrateLocationData()
                
                // 执行城市数据解析（增量）
                initializeCityData()
                
                // 检查保活机制状态
                checkKeepAliveStatus()
            }
            Lifecycle.Event.ON_STOP -> {
                // 更新应用后台状态
                AppStateManager.setAppInForeground(false)

                // App进入后台，释放屏幕常亮资源以节省电量
                ScreenManager.release()
                LocalLogManager.getInstance(app).logSync("app 进入后台，已释放屏幕常亮资源")
            }
            else -> {}
        }
    }

    private fun isAnyActivityInForeground(): Boolean {
        val activityManager = app.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
        val runningTasks = try {
            activityManager.getRunningTasks(1)
        } catch (e: Exception) {
            return false
        }

        return runningTasks.isNotEmpty() &&
               runningTasks[0].topActivity?.packageName == app.packageName
    }

    fun migrateLocationData() {
        CoroutineScope(Dispatchers.IO).launch {
            try {
                LocationDataRecorder.getInstance(app).saveAllLocation()
            } catch (e: Exception) {
                android.util.Log.e("MyApplication", "Error saving location data: ${e.message}")
            }
        }
    }

    /**
     * 检查并刷新用户令牌
     */
    private fun checkAndRefreshUserToken() {
        CoroutineScope(Dispatchers.IO).launch {
            try {
                LocalLogManager.getInstance(app).logSync("开始检查用户令牌状态")
                
                val userManager = UserManager.getInstance(app)
                val refreshResult = userManager.checkAndRefreshTokenIfNeeded()
                
                if (refreshResult) {
                    LocalLogManager.getInstance(app).logSync("用户令牌检查完成，状态正常")
                } else {
                    LocalLogManager.getInstance(app).logSync("用户令牌已过期或刷新失败，需要重新登录")
                }
                
            } catch (e: Exception) {
                LocalLogManager.getInstance(app).logSync("检查用户令牌失败: ${e.message}")
            }
        }
    }

    /**
     * 检查会员状态
     */
    private fun checkMembershipStatus() {
        CoroutineScope(Dispatchers.IO).launch {
            try {
                val membershipCheckManager = com.lfb.android.footprint.Manager.MembershipCheckManager.getInstance(app)

                // 检查是否需要进行会员状态检查
                if (membershipCheckManager.shouldCheckMembership()) {
                    LocalLogManager.getInstance(app).logSync("开始执行会员状态检查")

                    membershipCheckManager.checkMembershipStatus(
                        onResult = { success, needRelogin, message ->
                            LocalLogManager.getInstance(app).logSync("会员状态检查完成: success=$success, needRelogin=$needRelogin, message=$message")

                            if (needRelogin) {
                                // 需要重新登录，可以在这里发送通知或跳转到登录页面
                                LocalLogManager.getInstance(app).logSync("会员状态检查失败，需要重新登录")
                            }
                        },
                        onVersionCheck = { config ->
                            // 检查版本更新
                            val ignoredVersion = AppPrefs.sharedInstance.ignoredVersionCode
                            val updateResult = VersionManager.checkUpdate(
                                context = app,
                                latestVersionCode = config.latestVersionCode,
                                minVersionCode = config.minVersionCode,
                                ignoredVersionCode = ignoredVersion
                            )

                            val currentVersionCode = VersionManager.getCurrentVersionCode(app)

                            when (updateResult) {
                                is VersionManager.UpdateCheckResult.ForceUpdate -> {
                                    // 强制更新 - 使用全局管理器显示对话框
                                    GlobalVersionUpdateDialog.show(
                                        currentVersion = currentVersionCode,
                                        latestVersion = config.latestVersionCode,
                                        isForceUpdate = true,
                                        downloadUrl = config.downloadUrl,
                                        onUpdate = {
                                            // 打开下载链接，但不关闭对话框
                                            VersionManager.openDownloadUrl(app, config.downloadUrl)
                                        }
                                    )
                                }
                                is VersionManager.UpdateCheckResult.OptionalUpdate -> {
                                    // 可选更新 - 使用全局管理器显示对话框
                                    GlobalVersionUpdateDialog.show(
                                        currentVersion = currentVersionCode,
                                        latestVersion = config.latestVersionCode,
                                        isForceUpdate = false,
                                        downloadUrl = config.downloadUrl,
                                        onUpdate = {
                                            // 打开下载链接
                                            VersionManager.openDownloadUrl(app, config.downloadUrl)
                                        },
                                        onCancel = {
                                            // 取消时关闭对话框
                                            GlobalVersionUpdateDialog.dismiss()
                                        },
                                        onIgnore = {
                                            // 记录忽略的版本号
                                            AppPrefs.sharedInstance.ignoredVersionCode = config.latestVersionCode
                                            GlobalVersionUpdateDialog.dismiss()
                                        }
                                    )
                                }
                                is VersionManager.UpdateCheckResult.NoUpdate -> {
                                    // 无需更新
                                }
                            }
                        }
                    )
                } else {
                    LocalLogManager.getInstance(app).logSync("会员状态检查未到时间，跳过")
                }

            } catch (e: Exception) {
                LocalLogManager.getInstance(app).logSync("检查会员状态失败: ${e.message}")
            }
        }
    }

    /**
     * 初始化城市数据解析
     * 在应用启动时检查并执行增量城市数据解析
     */
    fun initializeCityData() {
        CoroutineScope(Dispatchers.IO).launch {
            try {
                LocalLogManager.getInstance(app).logSync("开始检查城市数据解析需求")
                val cityDataManager = CityDataManager.getInstance(app)
                // 检查是否需要解析城市数据
                if (cityDataManager.needsParsing()) {

                    LocalLogManager.getInstance(app).logSync("检测到新轨迹数据，开始城市数据解析")
                    val startTime = System.currentTimeMillis()

                    cityDataManager.performIncrementalParsing()

                    val endTime = System.currentTimeMillis()

                    LocalLogManager.getInstance(app).logSync("城市数据解析完成，耗时: ${endTime - startTime}ms")
                    
                    // 显示解析统计信息
                    val stats = cityDataManager.getCityDataStats()
                    LocalLogManager.getInstance(app).logSync("城市数据统计: 总记录${stats.totalRecords}条，${stats.uniqueCities}个不同城市")

                } else {
                    LocalLogManager.getInstance(app).logSync("城市数据已是最新，无需解析")
                }
            } catch (e: Exception) {
                LocalLogManager.getInstance(app).logSync("城市数据解析失败: ${e.message}")
                android.util.Log.e("MyApplication", "Error initializing city data: ${e.message}", e)
            }
        }
    }
    
    /**
     * 检查保活机制状态
     */
    private fun checkKeepAliveStatus() {
        CoroutineScope(Dispatchers.IO).launch {
            try {
                LocalLogManager.getInstance(app).logSync("开始检查保活机制状态")
                
                val keepAliveManager = com.lfb.android.footprint.keepalive.KeepAliveManager.getInstance(app)
                val status = keepAliveManager.getKeepAliveStatus()

                LocalLogManager.getInstance(app).logSync("保活状态检查结果:")
                LocalLogManager.getInstance(app).logSync("- LocationService: ${if (status.isLocationServiceRunning) "运行中" else "未运行"}")
                LocalLogManager.getInstance(app).logSync("- 地理围栏: ${if (status.isGeofenceActive) "已激活" else "未激活"}")
                LocalLogManager.getInstance(app).logSync("- 电池优化: ${if (status.isBatteryOptimizationIgnored) "已忽略" else "未设置"}")
                
                // 如果发现关键服务未运行，尝试重新初始化
                if (!status.isLocationServiceRunning) {
                    LocalLogManager.getInstance(app).logSync("检测到关键服务未运行，重新初始化保活机制")
                    keepAliveManager.reinitializeKeepAlive()
                }
                
            } catch (e: Exception) {
                LocalLogManager.getInstance(app).logSync("检查保活机制状态失败: ${e.message}")
            }
        }
    }
}