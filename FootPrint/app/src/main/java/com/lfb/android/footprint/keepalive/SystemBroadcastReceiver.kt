package com.lfb.android.footprint.keepalive

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.net.ConnectivityManager
import android.net.Network
import android.net.NetworkCapabilities
import android.net.NetworkRequest
import android.os.Build
import android.os.BatteryManager
import android.app.ActivityManager
import android.app.Application
import android.content.ComponentName
import android.content.pm.PackageManager
import com.lfb.android.footprint.Manager.LocalLogManager
import com.lfb.android.footprint.MyApplication
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.delay
import kotlin.math.abs

/**
 * 系统广播接收器
 * 监听系统广播事件，在关键时刻检查应用进程是否存活，并重启保活机制
 */
class SystemBroadcastReceiver : BroadcastReceiver() {
    
    companion object {
        private var networkCallback: ConnectivityManager.NetworkCallback? = null
        private var isNetworkCallbackRegistered = false
        private var dynamicReceiver: SystemBroadcastReceiver? = null
        private var isDynamicReceiverRegistered = false

        private var lastBatteryLevel = -1
        /**
         * 动态注册广播接收器 (用于Android 8.0+受限的广播)
         */
        fun registerDynamicReceiver(context: Context) {
            if (isDynamicReceiverRegistered) {
                LocalLogManager.getInstance(context).logSync("SystemBroadcastReceiver: 动态接收器已注册，跳过")
                return
            }
            
            try {
                dynamicReceiver = SystemBroadcastReceiver()
                val intentFilter = IntentFilter().apply {
                    // 这些广播在Android 8.0+必须动态注册
                    addAction(Intent.ACTION_SCREEN_ON)
                    addAction(Intent.ACTION_SCREEN_OFF)
                    addAction(Intent.ACTION_USER_PRESENT)
                    if (Build.VERSION.SDK_INT < Build.VERSION_CODES.N) {
                        addAction(ConnectivityManager.CONNECTIVITY_ACTION)
                    }
                    // 电池相关广播也建议动态注册
                    addAction(Intent.ACTION_BATTERY_CHANGED)
                    addAction(Intent.ACTION_BATTERY_LOW)
                    addAction(Intent.ACTION_BATTERY_OKAY)
                    priority = 1000
                }
                
                context.registerReceiver(dynamicReceiver, intentFilter)
                isDynamicReceiverRegistered = true
                LocalLogManager.getInstance(context).logSync("SystemBroadcastReceiver: 动态广播接收器注册成功")
            } catch (e: Exception) {
                LocalLogManager.getInstance(context).logSync("SystemBroadcastReceiver: 动态广播接收器注册失败 - ${e.message}")
            }
        }
        
        /**
         * 取消动态注册的广播接收器
         */
        fun unregisterDynamicReceiver(context: Context) {
            if (isDynamicReceiverRegistered && dynamicReceiver != null) {
                try {
                    context.unregisterReceiver(dynamicReceiver)
                    isDynamicReceiverRegistered = false
                    dynamicReceiver = null
                    LocalLogManager.getInstance(context).logSync("SystemBroadcastReceiver: 动态广播接收器取消注册成功")
                } catch (e: Exception) {
                    LocalLogManager.getInstance(context).logSync("SystemBroadcastReceiver: 动态广播接收器取消注册失败 - ${e.message}")
                }
            }
        }
        
        /**
         * 注册网络状态监听 (Android 7.0+)
         */
        fun registerNetworkCallback(context: Context) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N && !isNetworkCallbackRegistered) {
                try {
                    val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
                    val localLogManager = LocalLogManager.getInstance(context)
                    
                    networkCallback = object : ConnectivityManager.NetworkCallback() {
                        override fun onAvailable(network: Network) {
                            super.onAvailable(network)
//                            localLogManager.logSync("NetworkCallback: 网络连接可用")
                            
                            CoroutineScope(Dispatchers.IO).launch {
                                try {
                                    // 延迟一点时间确保网络真的可用
                                    delay(2000)
                                    checkApplicationProcessAndRestart(context)
                                } catch (e: Exception) {
                                    localLogManager.logSync("NetworkCallback: 处理网络可用事件异常 - ${e.message}")
                                }
                            }
                        }
                        
                        override fun onLost(network: Network) {
                            super.onLost(network)
                            localLogManager.logSync("NetworkCallback: 网络连接丢失")
                        }
                        
                        override fun onCapabilitiesChanged(network: Network, networkCapabilities: NetworkCapabilities) {
                            super.onCapabilitiesChanged(network, networkCapabilities)
                            localLogManager.logSync("NetworkCallback: 网络能力变化")
                            
                            CoroutineScope(Dispatchers.IO).launch {
                                try {
                                    checkApplicationProcessAndRestart(context)
                                } catch (e: Exception) {
                                    localLogManager.logSync("NetworkCallback: 处理网络能力变化事件异常 - ${e.message}")
                                }
                            }
                        }
                    }
                    
                    val networkRequest = NetworkRequest.Builder()
                        .addCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET)
                        .addTransportType(NetworkCapabilities.TRANSPORT_WIFI)
                        .addTransportType(NetworkCapabilities.TRANSPORT_CELLULAR)
                        .build()
                    
                    connectivityManager.registerNetworkCallback(networkRequest, networkCallback!!)
                    isNetworkCallbackRegistered = true
                    localLogManager.logSync("NetworkCallback: 网络状态监听注册成功")
                } catch (e: Exception) {
                    LocalLogManager.getInstance(context).logSync("NetworkCallback: 注册网络状态监听失败 - ${e.message}")
                }
            }
        }
        
        /**
         * 取消网络状态监听
         */
        fun unregisterNetworkCallback(context: Context) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N && isNetworkCallbackRegistered && networkCallback != null) {
                try {
                    val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
                    connectivityManager.unregisterNetworkCallback(networkCallback!!)
                    isNetworkCallbackRegistered = false
                    LocalLogManager.getInstance(context).logSync("NetworkCallback: 网络状态监听取消成功")
                } catch (e: Exception) {
                    LocalLogManager.getInstance(context).logSync("NetworkCallback: 取消网络状态监听失败 - ${e.message}")
                }
            }
        }
        
        /**
         * 初始化所有广播监听
         */
        fun initializeBroadcastListeners(context: Context) {
            val localLogManager = LocalLogManager.getInstance(context)
            localLogManager.logSync("SystemBroadcastReceiver: 开始初始化广播监听器")
            
            // 1. 注册动态广播接收器
            registerDynamicReceiver(context)
            
            // 2. 注册网络状态监听 (Android 7.0+)
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                registerNetworkCallback(context)
            }
            
            localLogManager.logSync("SystemBroadcastReceiver: 广播监听器初始化完成")
        }
        
        /**
         * 清理所有广播监听
         */
        fun cleanupBroadcastListeners(context: Context) {
            unregisterDynamicReceiver(context)
            unregisterNetworkCallback(context)
        }
        
        /**
         * 检查应用进程是否存活，如果被杀死则重启应用
         */
        private fun checkApplicationProcessAndRestart(context: Context) {
            val localLogManager = LocalLogManager.getInstance(context)
            
            try {
                if (!isApplicationProcessAlive(context)) {
                    localLogManager.logSync("NetworkCallback: 应用进程被杀死，正在重启应用")
                    restartApplication(context)
                } else {
                    localLogManager.logSync("NetworkCallback: 应用进程正在运行，检查保活机制")
                    checkAndRestartKeepAlive(context)
                }
            } catch (e: Exception) {
                localLogManager.logSync("NetworkCallback: 检查应用进程异常 - ${e.message}")
            }
        }
        
        /**
         * 检查应用进程是否存活
         */
        private fun isApplicationProcessAlive(context: Context): Boolean {
            return try {
                val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
                val processName = context.packageName
                
                // 检查运行中的进程
                val runningProcesses = activityManager.runningAppProcesses
                val isProcessRunning = runningProcesses?.any { processInfo ->
                    processInfo.processName == processName
                } ?: false
                
                if (isProcessRunning) {
                    // 进程在运行，进一步检查Application是否正常
                    val application = context.applicationContext
                    val isApplicationAlive = application is MyApplication
                    
                    LocalLogManager.getInstance(context).logSync(
                        "NetworkCallback: 应用进程检查 - 进程运行: $isProcessRunning, Application正常: $isApplicationAlive"
                    )
                    
                    isApplicationAlive
                } else {
                    LocalLogManager.getInstance(context).logSync(
                        "NetworkCallback: 应用进程检查 - 进程未运行"
                    )
                    false
                }
            } catch (e: Exception) {
                LocalLogManager.getInstance(context).logSync(
                    "NetworkCallback: 检查应用进程异常 - ${e.message}"
                )
                false
            }
        }
        
        /**
         * 重启应用
         */
        private fun restartApplication(context: Context) {
            val localLogManager = LocalLogManager.getInstance(context)
            
            try {
                localLogManager.logSync("NetworkCallback: 开始重启应用")
                
                // 方式1：通过启动主Activity重启应用
                val packageManager = context.packageManager
                val launchIntent = packageManager.getLaunchIntentForPackage(context.packageName)
                
                if (launchIntent != null) {
                    launchIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                    launchIntent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
                    launchIntent.addFlags(Intent.FLAG_ACTIVITY_SINGLE_TOP)
                    
                    context.startActivity(launchIntent)
                    localLogManager.logSync("NetworkCallback: 通过启动主Activity重启应用成功")
                } else {
                    localLogManager.logSync("NetworkCallback: 无法获取启动Intent")
                }
                
                // 方式2：延迟启动保活机制（作为备用方案）
                CoroutineScope(Dispatchers.IO).launch {
                    delay(5000) // 延迟5秒等待应用启动
                    try {
                        val keepAliveManager = KeepAliveManager.getInstance(context)
                        keepAliveManager.initializeKeepAlive()
                        localLogManager.logSync("NetworkCallback: 延迟启动保活机制完成")
                    } catch (e: Exception) {
                        localLogManager.logSync("NetworkCallback: 延迟启动保活机制异常 - ${e.message}")
                    }
                }
                
            } catch (e: Exception) {
                localLogManager.logSync("NetworkCallback: 重启应用异常 - ${e.message}")
            }
        }
        
        /**
         * 检查并重启保活机制
         */
        private fun checkAndRestartKeepAlive(context: Context) {
            val localLogManager = LocalLogManager.getInstance(context)

            try {
                val keepAliveManager = KeepAliveManager.getInstance(context)
                val status = keepAliveManager.getKeepAliveStatus()

                localLogManager.logSync("NetworkCallback: 当前保活状态检查 - LocationService: ${status.isLocationServiceRunning}, Geofence: ${status.isGeofenceActive}")

                // 如果LocationService未运行，尝试重启
                if (!status.isLocationServiceRunning) {
                    localLogManager.logSync("NetworkCallback: LocationService未运行，尝试重启")
                    keepAliveManager.initializeKeepAlive()
                }

                // 如果地理围栏未激活，尝试重新初始化
                if (!status.isGeofenceActive) {
//                    localLogManager.logSync("NetworkCallback: 地理围栏未激活，尝试重新初始化")
                    // 延迟一段时间后重新初始化地理围栏，因为可能需要等待LocationService启动
                    Thread.sleep(5000) // 5秒延迟
                    com.lfb.android.footprint.geofence.GeofenceInitializer.initialize(context)
                }
                
            } catch (e: Exception) {
                localLogManager.logSync("NetworkCallback: 检查并重启保活机制异常 - ${e.message}")
            }
        }
    }
    
    override fun onReceive(context: Context, intent: Intent) {
        val localLogManager = LocalLogManager.getInstance(context)
        val action = intent.action
        
//        localLogManager.logSync("SystemBroadcastReceiver: 收到系统广播 - $action")
        
        // 在后台线程中处理广播事件
        CoroutineScope(Dispatchers.IO).launch {
            try {
                when (action) {
                    Intent.ACTION_BOOT_COMPLETED,
                    Intent.ACTION_REBOOT,
                    "android.intent.action.QUICKBOOT_POWERON",
                    "com.htc.intent.action.QUICKBOOT_POWERON" -> {
                        handleBootCompleted(context)
                    }
                    Intent.ACTION_USER_PRESENT -> {
                        handleUserPresent(context)
                    }
                    Intent.ACTION_SCREEN_ON -> {
                        handleScreenOn(context)
                    }
                    Intent.ACTION_SCREEN_OFF -> {
                        handleScreenOff(context)
                    }
                    ConnectivityManager.CONNECTIVITY_ACTION -> {
                        handleConnectivityChange(context)
                    }
                    Intent.ACTION_BATTERY_CHANGED -> {
                        handleBatteryChanged(context, intent)
                    }
                    Intent.ACTION_BATTERY_LOW -> {
                        handleBatteryLow(context)
                    }
                    Intent.ACTION_BATTERY_OKAY -> {
                        handleBatteryOkay(context)
                    }
                }
            } catch (e: Exception) {
                localLogManager.logSync("SystemBroadcastReceiver: 处理广播事件时发生异常 - ${e.message}")
            }
        }
    }
    
    /**
     * 处理开机完成事件
     */
    private fun handleBootCompleted(context: Context) {
        val localLogManager = LocalLogManager.getInstance(context)
        localLogManager.logSync("SystemBroadcastReceiver: 处理开机完成事件")
        
        try {
            // 延迟一段时间再启动保活机制，确保系统完全启动
            Thread.sleep(15000) // 15秒延迟，确保系统完全启动
            
            localLogManager.logSync("SystemBroadcastReceiver: 系统启动延迟完成，开始检查应用状态")
            
            // 初始化所有广播监听器
            initializeBroadcastListeners(context)
            
            // 检查应用进程是否存活，如果不存在则重启应用
            if (!isApplicationProcessAlive(context)) {
                localLogManager.logSync("SystemBroadcastReceiver: 开机时应用进程未运行，启动应用")
                restartApplication(context)
            } else {
                localLogManager.logSync("SystemBroadcastReceiver: 开机时应用进程正在运行，初始化保活机制")
                val keepAliveManager = KeepAliveManager.getInstance(context)
                val success = keepAliveManager.initializeKeepAlive()
                
                if (success) {
                    localLogManager.logSync("SystemBroadcastReceiver: 开机启动保活机制成功")
                } else {
                    localLogManager.logSync("SystemBroadcastReceiver: 开机启动保活机制失败")
                }
            }
            
            // 额外延迟后再次检查（双重保险）
            Thread.sleep(10000) // 再延迟10秒
            if (!isApplicationProcessAlive(context)) {
                localLogManager.logSync("SystemBroadcastReceiver: 开机后二次检查发现应用进程未运行，重启应用")
                restartApplication(context)
            }
            
        } catch (e: Exception) {
            localLogManager.logSync("SystemBroadcastReceiver: 开机启动保活机制异常 - ${e.message}")
        }
    }
    
    /**
     * 处理用户解锁事件
     */
    private fun handleUserPresent(context: Context) {
        val localLogManager = LocalLogManager.getInstance(context)
        localLogManager.logSync("SystemBroadcastReceiver: 处理用户解锁事件")
        
        try {
            // 用户解锁时检查应用进程并重启保活机制
            checkApplicationProcessAndRestart(context)
        } catch (e: Exception) {
            localLogManager.logSync("SystemBroadcastReceiver: 处理用户解锁事件异常 - ${e.message}")
        }
    }
    
    /**
     * 处理屏幕亮起事件
     */
    private fun handleScreenOn(context: Context) {
        val localLogManager = LocalLogManager.getInstance(context)

        localLogManager.logSync("***************************************")
        localLogManager.logSync("***************************************")
        localLogManager.logSync("SystemBroadcastReceiver: 屏幕亮起事件")
        
        try {
            // 屏幕亮起时检查应用进程状态
            checkApplicationProcessAndRestart(context)
        } catch (e: Exception) {
            localLogManager.logSync("SystemBroadcastReceiver: 处理屏幕亮起事件异常 - ${e.message}")
        }
    }
    
    /**
     * 处理屏幕熄灭事件
     */
    private fun handleScreenOff(context: Context) {
        val localLogManager = LocalLogManager.getInstance(context)
        localLogManager.logSync("SystemBroadcastReceiver: 处理屏幕熄灭事件")
        
        try {
            // 屏幕熄灭时确保应用进程和保活机制运行
            checkApplicationProcessAndRestart(context)
        } catch (e: Exception) {
            localLogManager.logSync("SystemBroadcastReceiver: 处理屏幕熄灭事件异常 - ${e.message}")
        }
    }
    
    /**
     * 处理网络连接变化事件 (兼容Android 7.0以下)
     */
    private fun handleConnectivityChange(context: Context) {
        val localLogManager = LocalLogManager.getInstance(context)
        localLogManager.logSync("SystemBroadcastReceiver: 处理网络连接变化事件")
        
        try {
            // 对于Android 7.0以下的系统，仍然使用传统的广播方式
            if (Build.VERSION.SDK_INT < Build.VERSION_CODES.N) {
                val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
                val networkInfo = connectivityManager.activeNetworkInfo
                
                if (networkInfo != null && networkInfo.isConnected) {
                    localLogManager.logSync("SystemBroadcastReceiver: 网络已连接（传统方式），检查应用进程状态")
                    checkApplicationProcessAndRestart(context)
                } else {
                    localLogManager.logSync("SystemBroadcastReceiver: 网络已断开（传统方式）")
                }
            } else {
                localLogManager.logSync("SystemBroadcastReceiver: Android 7.0+系统，使用NetworkCallback监听网络变化")
                // 确保NetworkCallback已注册
                if (!isNetworkCallbackRegistered) {
                    registerNetworkCallback(context)
                }
            }
        } catch (e: Exception) {
            localLogManager.logSync("SystemBroadcastReceiver: 处理网络连接变化事件异常 - ${e.message}")
        }
    }
    
    /**
     * 处理电池状态变化事件
     */
    private fun handleBatteryChanged(context: Context, intent: Intent) {
        val localLogManager = LocalLogManager.getInstance(context)
        
        try {
            val level = intent.getIntExtra(BatteryManager.EXTRA_LEVEL, -1)
            val scale = intent.getIntExtra(BatteryManager.EXTRA_SCALE, -1)
            val batteryPct = level * 100 / scale.toFloat()
            val status = intent.getIntExtra(BatteryManager.EXTRA_STATUS, -1)
            val isCharging = status == BatteryManager.BATTERY_STATUS_CHARGING || 
                           status == BatteryManager.BATTERY_STATUS_FULL

            val batteryLevel = batteryPct.toInt()
            if (abs(lastBatteryLevel - batteryLevel) > 5) {
                lastBatteryLevel = batteryLevel
                localLogManager.logSync("SystemBroadcastReceiver: 电池状态变化 - 电量: ${batteryPct.toInt()}%, 充电状态: $isCharging")
                // 在电池状态变化时检查应用进程
                checkApplicationProcessAndRestart(context)
            }
            
        } catch (e: Exception) {
            localLogManager.logSync("SystemBroadcastReceiver: 处理电池状态变化事件异常 - ${e.message}")
        }
    }
    
    /**
     * 处理电池电量低事件
     */
    private fun handleBatteryLow(context: Context) {
        val localLogManager = LocalLogManager.getInstance(context)
        localLogManager.logSync("SystemBroadcastReceiver: 处理电池电量低事件")
        
        try {
            // 电池电量低时检查应用进程状态
            checkApplicationProcessAndRestart(context)
        } catch (e: Exception) {
            localLogManager.logSync("SystemBroadcastReceiver: 处理电池电量低事件异常 - ${e.message}")
        }
    }
    
    /**
     * 处理电池电量恢复正常事件
     */
    private fun handleBatteryOkay(context: Context) {
        val localLogManager = LocalLogManager.getInstance(context)
        localLogManager.logSync("SystemBroadcastReceiver: 处理电池电量恢复正常事件")
        
        try {
            // 电池电量恢复时检查应用进程状态
            checkApplicationProcessAndRestart(context)
        } catch (e: Exception) {
            localLogManager.logSync("SystemBroadcastReceiver: 处理电池电量恢复正常事件异常 - ${e.message}")
        }
    }
    
    /**
     * 确保保活机制运行
     */
    private fun ensureKeepAliveRunning(context: Context) {
        val localLogManager = LocalLogManager.getInstance(context)
        
        try {
            // 首先检查应用进程是否存活
            if (!isApplicationProcessAlive(context)) {
                localLogManager.logSync("SystemBroadcastReceiver: 关键时刻应用进程未运行，重启应用")
                restartApplication(context)
                return
            }
            
            val keepAliveManager = KeepAliveManager.getInstance(context)
            val status = keepAliveManager.getKeepAliveStatus()
            
            // 检查关键服务状态
            if (!status.isLocationServiceRunning) {
                localLogManager.logSync("SystemBroadcastReceiver: 关键时刻LocationService未运行，立即重启")
                keepAliveManager.initializeKeepAlive()
            } else {
                localLogManager.logSync("SystemBroadcastReceiver: 保活机制运行正常")
            }
            
        } catch (e: Exception) {
            localLogManager.logSync("SystemBroadcastReceiver: 确保保活机制运行异常 - ${e.message}")
        }
    }
    
    /**
     * 检查应用进程是否存活，如果被杀死则重启应用
     */
    private fun checkApplicationProcessAndRestart(context: Context) {
        val localLogManager = LocalLogManager.getInstance(context)
        
        try {
            if (!isApplicationProcessAlive(context)) {
                localLogManager.logSync("SystemBroadcastReceiver: 应用进程被杀死，正在重启应用")
                restartApplication(context)
            } else {
//                localLogManager.logSync("SystemBroadcastReceiver: 应用进程正在运行，检查保活机制")
                checkAndRestartKeepAlive(context)
            }
        } catch (e: Exception) {
            localLogManager.logSync("SystemBroadcastReceiver: 检查应用进程异常 - ${e.message}")
        }
    }


    /**
     * 检查应用进程是否存活
     */
    private fun isApplicationProcessAlive(context: Context): Boolean {
        return try {
            val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
            val processName = context.packageName
            
            // 检查运行中的进程
            val runningProcesses = activityManager.runningAppProcesses
            val isProcessRunning = runningProcesses?.any { processInfo ->
                processInfo.processName == processName
            } ?: false
            
            if (isProcessRunning) {
                // 进程在运行，进一步检查Application是否正常
                val application = context.applicationContext
                val isApplicationAlive = application is MyApplication

//                LocalLogManager.getInstance(context).logSync(
//                    "SystemBroadcastReceiver: 应用进程检查 - 进程运行: $isProcessRunning, Application正常: $isApplicationAlive"
//                )

                return isApplicationAlive
            } else {
                LocalLogManager.getInstance(context).logSync(
                    "SystemBroadcastReceiver: 应用进程检查 - 进程未运行"
                )
                false
            }
        } catch (e: Exception) {
            LocalLogManager.getInstance(context).logSync(
                "SystemBroadcastReceiver: 检查应用进程异常 - ${e.message}"
            )
            false
        }
    }
    
    /**
     * 重启应用
     */
    private fun restartApplication(context: Context) {
        val localLogManager = LocalLogManager.getInstance(context)
        
        try {
            localLogManager.logSync("SystemBroadcastReceiver: 开始重启应用")
            
            // 方式1：通过启动主Activity重启应用
            val packageManager = context.packageManager
            val launchIntent = packageManager.getLaunchIntentForPackage(context.packageName)
            
            if (launchIntent != null) {
                launchIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                launchIntent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
                launchIntent.addFlags(Intent.FLAG_ACTIVITY_SINGLE_TOP)
                
                context.startActivity(launchIntent)
                localLogManager.logSync("SystemBroadcastReceiver: 通过启动主Activity重启应用成功")
            } else {
                localLogManager.logSync("SystemBroadcastReceiver: 无法获取启动Intent")
            }
            
            // 方式2：延迟启动保活机制（作为备用方案）
            CoroutineScope(Dispatchers.IO).launch {
                delay(5000) // 延迟5秒等待应用启动
                try {
                    val keepAliveManager = KeepAliveManager.getInstance(context)
                    keepAliveManager.initializeKeepAlive()
                    localLogManager.logSync("SystemBroadcastReceiver: 延迟启动保活机制完成")
                } catch (e: Exception) {
                    localLogManager.logSync("SystemBroadcastReceiver: 延迟启动保活机制异常 - ${e.message}")
                }
            }
            
        } catch (e: Exception) {
            localLogManager.logSync("SystemBroadcastReceiver: 重启应用异常 - ${e.message}")
        }
    }
    
    /**
     * 检查并重启保活机制
     */
    private fun checkAndRestartKeepAlive(context: Context) {
        val localLogManager = LocalLogManager.getInstance(context)

        try {
            val keepAliveManager = KeepAliveManager.getInstance(context)
            val status = keepAliveManager.getKeepAliveStatus()

            localLogManager.logSync("SystemBroadcastReceiver: 当前保活状态检查 - LocationService: ${status.isLocationServiceRunning}, Geofence: ${status.isGeofenceActive}")

            // 如果LocationService未运行，尝试重启
            if (!status.isLocationServiceRunning) {
                localLogManager.logSync("SystemBroadcastReceiver: LocationService未运行，尝试重启")
                keepAliveManager.initializeKeepAlive()
            }

            // 如果地理围栏未激活，尝试重新初始化
            if (!status.isGeofenceActive) {
//                localLogManager.logSync("SystemBroadcastReceiver: 地理围栏未激活，尝试重新初始化")
                // 延迟一段时间后重新初始化地理围栏，因为可能需要等待LocationService启动
                Thread.sleep(5000) // 5秒延迟
                com.lfb.android.footprint.geofence.GeofenceInitializer.initialize(context)
            }

        } catch (e: Exception) {
            localLogManager.logSync("SystemBroadcastReceiver: 检查并重启保活机制异常 - ${e.message}")
        }
    }
} 