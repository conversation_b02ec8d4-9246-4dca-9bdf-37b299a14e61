package com.lfb.android.footprint.ui.components.mapScreen

import androidx.compose.foundation.clickable
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.background
//import androidx.compose.foundation.layout.matchParentSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.sizeIn
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.runtime.rememberUpdatedState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
//import androidx.xr.compose.testing.toDp
import kotlin.math.abs
import kotlin.math.max
import kotlin.math.min
import kotlin.math.roundToInt
import androidx.xr.compose.testing.toDp
import com.lfb.android.footprint.ui.theme.mainRedColor
import com.lfb.android.footprint.ui.theme.rememberMapThemeConfig

@Composable
fun TimePickerView(
    modifier: Modifier = Modifier,
    onItemSelected: (Int) -> Unit
) {
    var selected by remember { mutableStateOf(2) }
    val items = listOf("一生", "自定义", "今日", "昨日", "七日")

    Column(
        modifier = modifier,
        verticalArrangement = Arrangement.Center,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        CarouselPicker(
            items = items,
            selectedIndex = selected,
            onItemSelected = {
                selected = it
                onItemSelected(it)
            }
        )
    }
}

@Composable
fun CarouselPicker(
    items: List<String>,
    selectedIndex: Int,
    onItemSelected: (Int) -> Unit,
    modifier: Modifier = Modifier
) {
    val themeConfig = rememberMapThemeConfig()

    val itemWidth = 46.dp
    // 用于获取滑动后最新的 selectedIndex
    val currentIndex by rememberUpdatedState(selectedIndex)
    val spacing = 0.dp
    val maxRotation = 50f // 进一步增大旋转角度，让两侧文字透视更明显
    val maxScale = 1.3f // 增大中间项的缩放
    val minScale = 0.5f // 减小外侧项的缩放，增强大小对比
    val radiusPx = with(LocalDensity.current) { 60.dp.toPx() } // 增大圆盘半径，增强弧度效果
    val itemWidthPx = with(LocalDensity.current) { itemWidth.toPx() }
    val spacingPx = with(LocalDensity.current) { spacing.toPx() }
    val totalItemSpace = itemWidthPx + spacingPx
    var listWidth by remember { mutableStateOf(0) }
    val horizontalPadding = if (listWidth > 0) (listWidth / 2f - itemWidthPx / 2f).toDp() else 0.dp

    val listState = rememberLazyListState(initialFirstVisibleItemIndex = selectedIndex)

    // 自动滚动到选中项
    LaunchedEffect(selectedIndex) {
        listState.animateScrollToItem(selectedIndex)
    }

    // 监听滚动停止，自动选中最中间的item
    LaunchedEffect(listState.isScrollInProgress) {
        if (!listState.isScrollInProgress) {
            val center = listState.firstVisibleItemIndex + (listState.firstVisibleItemScrollOffset / totalItemSpace)
            val nearestIndex = center.roundToInt().coerceIn(0, items.lastIndex)
            if (nearestIndex != selectedIndex) {
                onItemSelected(nearestIndex)
            } else {
                listState.animateScrollToItem(nearestIndex)
            }
        }
    }

    Box(
        modifier = modifier
            .fillMaxWidth()
            .onGloballyPositioned { coordinates ->
                listWidth = coordinates.size.width
            },
        contentAlignment = Alignment.Center
    ) {
        LazyRow(
            state = listState,
            contentPadding = PaddingValues(horizontal = horizontalPadding),
            horizontalArrangement = Arrangement.spacedBy(spacing),
            modifier = Modifier.fillMaxWidth()
//                .padding(start = 8.dp) // 向右偏移12dp

        ) {
            itemsIndexed(items) { index, item ->
                // 计算每个item到中心的距离（单位：item）
                val center = listState.firstVisibleItemIndex + (listState.firstVisibleItemScrollOffset / totalItemSpace)
                val distanceFromCenter = index - center
                val angle = distanceFromCenter * 90f // 增大角度间隔，让两侧旋转更明显
                // 修复透视方向：左侧应该是正值，右侧应该是负值，让所有项目都朝向中心
                var rotationY = max(-maxRotation, min(maxRotation, -angle))
                val scale = max(minScale, maxScale - abs(distanceFromCenter) * 0.4f)
                val rad = Math.toRadians(angle.toDouble())
                val translationX = (Math.sin(rad) * radiusPx * 0.3f).toFloat() // 增大水平位移

                Box(
                    modifier = Modifier
                        .sizeIn(minWidth = 40.dp)
//                        .padding(horizontal = spacing / 2)
                        .padding(vertical = 12.dp)
//                        .background(Color.Gray)
                        .clickable(
                            interactionSource = remember { MutableInteractionSource() },
                            indication = null
                        ) { onItemSelected(index) },
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = item,
                        fontSize = 16.sp,
                        color = if (index == selectedIndex) mainRedColor else Color.Gray,
                        modifier = Modifier
                            .wrapContentWidth()
                            .graphicsLayer {
                                this.rotationY = rotationY
                                this.cameraDistance = 50 * density // 进一步减小相机距离，让Y轴旋转的透视效果更强
                                this.scaleX = scale
                                this.scaleY = scale
                                this.translationX = translationX
                            }
                    )
                }
            }
        }

        // 渐变遮罩层：中间透明，两侧有阴影
        Box(
            modifier = Modifier
                .matchParentSize()
                .background(
                    brush = Brush.horizontalGradient(
                        colors = listOf(
                            themeConfig.backgroundColor,  // 左侧阴影
                            Color.Transparent,                // 中间透明
                            Color.Transparent,                // 中间透明
                            themeConfig.backgroundColor   // 右侧阴影
                        ),
                        startX = 0f,
                        endX = Float.POSITIVE_INFINITY
                    )
                )
        )

        Box(
            modifier = Modifier
                .matchParentSize()
        ) {
            // 左侧
            Box(
                modifier = Modifier
                    .align(Alignment.CenterStart)
                    .fillMaxWidth(0.3f)
                    .fillMaxHeight()
                    .pointerInput(currentIndex) {
                        detectTapGestures(
                            onTap = {
                                if (currentIndex > 0) onItemSelected(currentIndex - 1)
                            }
                        )
                    }
            )
            // 右侧
            Box(
                modifier = Modifier
                    .align(Alignment.CenterEnd)
                    .fillMaxWidth(0.3f)
                    .fillMaxHeight()
                    .pointerInput(currentIndex) {
                        detectTapGestures(
                            onTap = {
                                if (currentIndex < items.lastIndex) onItemSelected(currentIndex + 1)
                            }
                        )
                    }
            )
        }
    }
}