package com.lfb.android.footprint.ui.components

import android.app.Activity
import android.view.ViewGroup
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import androidx.lifecycle.setViewTreeLifecycleOwner
import androidx.lifecycle.setViewTreeViewModelStoreOwner
import androidx.savedstate.setViewTreeSavedStateRegistryOwner
import com.lfb.android.footprint.ui.theme.rememberMapThemeConfig

/**
 * 版本更新对话框
 * 支持两种使用方式：
 * 1. 在Compose中直接调用VersionUpdateDialog组件
 * 2. 通过VersionUpdateDialogManager全局显示（推荐）
 */
object VersionUpdateDialogManager {
    private var currentDialog: VersionUpdateDialogState? by mutableStateOf(null)
    private var globalComposeView: ComposeView? = null
    
    /**
     * 显示版本更新对话框
     */
    fun show(
        currentVersion: Int,
        latestVersion: Int,
        isForceUpdate: Boolean,
        downloadUrl: String,
        onUpdate: () -> Unit,
        onCancel: (() -> Unit)? = null,
        onIgnore: (() -> Unit)? = null
    ) {
        currentDialog = VersionUpdateDialogState(
            currentVersion = currentVersion,
            latestVersion = latestVersion,
            isForceUpdate = isForceUpdate,
            downloadUrl = downloadUrl,
            onUpdate = onUpdate,
            onCancel = onCancel,
            onIgnore = onIgnore
        )
    }
    
    /**
     * 关闭对话框
     */
    fun dismiss() {
        currentDialog = null
    }
    
    /**
     * 在Compose中使用的Container
     */
    @Composable
    fun Container() {
        currentDialog?.let { state ->
            VersionUpdateDialog(
                currentVersion = state.currentVersion,
                latestVersion = state.latestVersion,
                isForceUpdate = state.isForceUpdate,
                downloadUrl = state.downloadUrl,
                onUpdate = {
                    state.onUpdate()
                    dismiss()
                },
                onCancel = if (state.onCancel != null) {
                    {
                        state.onCancel?.invoke()
                        dismiss()
                    }
                } else null,
                onIgnore = if (state.onIgnore != null) {
                    {
                        state.onIgnore?.invoke()
                        dismiss()
                    }
                } else null
            )
        }
    }
    
    /**
     * 附加到Activity的Window（内部使用）
     */
    internal fun attachToWindow(activity: Activity) {
        if (globalComposeView != null) {
            detachFromWindow()
        }
        
        if (activity !is androidx.lifecycle.LifecycleOwner) {
            return
        }
        
        val decorView = activity.window.decorView as ViewGroup
        
        globalComposeView = ComposeView(activity).apply {
            setViewTreeLifecycleOwner(activity)
            if (activity is androidx.lifecycle.ViewModelStoreOwner) {
                setViewTreeViewModelStoreOwner(activity)
            }
            if (activity is androidx.savedstate.SavedStateRegistryOwner) {
                setViewTreeSavedStateRegistryOwner(activity)
            }
            
            setContent {
                Container()
            }
        }
        
        decorView.addView(
            globalComposeView,
            ViewGroup.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.MATCH_PARENT
            )
        )
    }
    
    /**
     * 从Window分离（内部使用）
     */
    internal fun detachFromWindow() {
        globalComposeView?.let { view ->
            (view.parent as? ViewGroup)?.removeView(view)
        }
        globalComposeView = null
    }
}

private class VersionUpdateDialogState(
    val currentVersion: Int,
    val latestVersion: Int,
    val isForceUpdate: Boolean,
    val downloadUrl: String,
    val onUpdate: () -> Unit,
    val onCancel: (() -> Unit)?,
    val onIgnore: (() -> Unit)?
)

/**
 * 版本更新对话框组件
 */
@Composable
fun VersionUpdateDialog(
    currentVersion: Int,
    latestVersion: Int,
    isForceUpdate: Boolean,
    downloadUrl: String,
    onUpdate: () -> Unit,
    onCancel: (() -> Unit)? = null,
    onIgnore: (() -> Unit)? = null
) {

    val themeConfig = rememberMapThemeConfig()

    Dialog(
        onDismissRequest = {
            // 强制更新时不允许通过点击外部关闭
            if (!isForceUpdate) {
                onCancel?.invoke()
            }
        },
        properties = DialogProperties(
            dismissOnBackPress = !isForceUpdate,
            dismissOnClickOutside = !isForceUpdate
        )
    ) {
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            shape = RoundedCornerShape(16.dp),
            colors = CardDefaults.cardColors(
                containerColor = themeConfig.backgroundColor
            ),
            elevation = CardDefaults.cardElevation(
                defaultElevation = 8.dp
            )
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(24.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                // 标题
                Text(
                    text = if (isForceUpdate) "发现新版本（强制更新）" else "发现新版本",
                    fontSize = 20.sp,
                    fontWeight = FontWeight.Bold,
                    color = themeConfig.textColor
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 版本信息
                Text(
                    text = "当前版本：$currentVersion\n最新版本：$latestVersion",
                    fontSize = 14.sp,
                    color = themeConfig.normalTextColor,
                    textAlign = TextAlign.Center,
                    lineHeight = 20.sp
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                // 提示信息
                if (isForceUpdate) {
                    Text(
                        text = "您的版本过低，需要更新后才能继续使用",
                        fontSize = 13.sp,
                        color = themeConfig.textColor,
                        textAlign = TextAlign.Center,
                        lineHeight = 18.sp
                    )
                } else {
                    Text(
                        text = "建议您更新到最新版本以获得更好的体验",
                        fontSize = 13.sp,
                        color = themeConfig.textColor,
                        textAlign = TextAlign.Center,
                        lineHeight = 18.sp
                    )
                }
                
                Spacer(modifier = Modifier.height(24.dp))
                
                // 按钮区域
                if (isForceUpdate) {
                    // 强制更新：只显示"前去更新"按钮
                    Button(
                        onClick = onUpdate,
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(48.dp),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = themeConfig.backgroundColor
                        ),
                        shape = RoundedCornerShape(8.dp)
                    ) {
                        Text(
                            text = "前去更新",
                            fontSize = 16.sp,
                            fontWeight = FontWeight.Medium,
                            color = themeConfig.textColor
                        )
                    }
                } else {
                    // 可选更新：显示三个按钮
                    Column(
                        modifier = Modifier.fillMaxWidth(),
                        verticalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        // 更新按钮
                        Button(
                            onClick = onUpdate,
                            modifier = Modifier
                                .fillMaxWidth()
                                .height(44.dp),
                            colors = ButtonDefaults.buttonColors(
                                containerColor = themeConfig.normalBackgroundColor
                            ),
                            shape = RoundedCornerShape(8.dp)
                        ) {
                            Text(
                                text = "前去更新",
                                fontSize = 16.sp,
                                fontWeight = FontWeight.Medium,
                                color = themeConfig.normalTextColor
                            )
                        }
                        
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.spacedBy(12.dp)
                        ) {
                            // 取消按钮
                            OutlinedButton(
                                onClick = { onCancel?.invoke() },
                                modifier = Modifier
                                    .weight(1f)
                                    .height(40.dp),
                                colors = ButtonDefaults.outlinedButtonColors(
                                    contentColor = themeConfig.normalBackgroundColor
                                ),
                                shape = RoundedCornerShape(8.dp)
                            ) {
                                Text(
                                    text = "取消",
                                    fontSize = 14.sp,
                                    fontWeight = FontWeight.Medium,
                                    color = themeConfig.normalTextColor
                                )
                            }
                            
                            // 不再提示按钮
                            OutlinedButton(
                                onClick = { onIgnore?.invoke() },
                                modifier = Modifier
                                    .weight(1f)
                                    .height(40.dp),
                                colors = ButtonDefaults.outlinedButtonColors(
                                    contentColor = themeConfig.normalBackgroundColor
                                ),
                                shape = RoundedCornerShape(8.dp)
                            ) {
                                Text(
                                    text = "不再提示",
                                    fontSize = 14.sp,
                                    fontWeight = FontWeight.Medium,
                                    color = themeConfig.normalTextColor
                                )
                            }
                        }
                    }
                }
            }
        }
    }
}
