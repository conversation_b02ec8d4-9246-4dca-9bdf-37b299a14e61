package com.lfb.android.footprint.ui.components.mapScreen

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.lfb.android.footprint.R
import androidx.compose.foundation.gestures.detectTapGestures
import com.lfb.android.footprint.ui.theme.*
import com.lfb.android.footprint.ui.theme.rememberMapThemeConfig
import com.lfb.android.footprint.prefs.AppPrefs

@Composable
fun MapControlButtons(
    onLocationClick: () -> Unit,
    onConfigClick: () -> Unit,
    onDetailClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    val themeConfig = rememberMapThemeConfig()
    Column(
        modifier = modifier
            .shadow(
                elevation = 1.dp,
                shape = RoundedCornerShape(12.dp),
                spotColor = themeConfig.shadowColor
            )
            .clip(RoundedCornerShape(12.dp))
            .background(themeConfig.backgroundColor)
            .padding(2.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.spacedBy(-8.dp)
    ) {
        // Location Button
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(0.dp),
            modifier = Modifier.padding(vertical = 1.dp)
        ) {
            MapControlButton(
                onClick = onLocationClick,
                iconResId = R.drawable.loc,
                contentDescription = "定位按钮",
                tintColor = mainRedColor
            )
            Text(
                text = "定位",
                fontSize = 11.sp,
                color = mainRedColor,
                textAlign = TextAlign.Center,
                modifier = Modifier.offset(y = (-9).dp)
            )
        }

        // Config Button
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(0.dp),
            modifier = Modifier.padding(vertical = 1.dp)
        ) {
            MapControlButton(
                onClick = onConfigClick,
                iconResId = R.drawable.platte,
                contentDescription = "配置按钮",
                tintColor = mainRedColor
            )
            Text(
                text = "配置",
                fontSize = 11.sp,
                color = mainRedColor,
                textAlign = TextAlign.Center,
                modifier = Modifier.offset(y = (-9).dp)
            )
        }

        // Detail Button
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(0.dp),
            modifier = Modifier.padding(vertical = 1.dp)
        ) {
            MapControlButton(
                onClick = onDetailClick,
                iconResId = R.drawable.details,
                contentDescription = "详细按钮",
                tintColor = mainRedColor
            )
            Text(
                text = "详细",
                fontSize = 11.sp,
                color = mainRedColor,
                textAlign = TextAlign.Center,
                modifier = Modifier.offset(y = (-9).dp)
            )
        }
    }
}

@Composable
private fun MapControlButton(
    onClick: () -> Unit,
    iconResId: Int,
    contentDescription: String,
    tintColor: Color
) {
    var isPressed by remember { mutableStateOf(false) }

    Box(
        modifier = Modifier
            .size(44.dp)
            .alpha(if (isPressed) 0.5f else 1f) // 点击时透明度变化
            .pointerInput(Unit) {
                detectTapGestures(
                    onPress = {
                        isPressed = true
                        tryAwaitRelease()
                        isPressed = false
                    },
                    onTap = { onClick() }
                )
            }
    ) {
        Image(
            painter = painterResource(id = iconResId),
            contentDescription = contentDescription,
            modifier = Modifier.fillMaxSize(),
            colorFilter = ColorFilter.tint(tintColor)
        )
    }
}