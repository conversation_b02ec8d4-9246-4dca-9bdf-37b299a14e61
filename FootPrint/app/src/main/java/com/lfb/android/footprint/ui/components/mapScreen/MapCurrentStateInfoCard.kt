package com.lfb.android.footprint.ui.components.mapScreen

import android.annotation.SuppressLint
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.foundation.gestures.detectHorizontalDragGestures
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.foundation.clickable
import androidx.compose.ui.text.style.TextOverflow
import com.lfb.android.footprint.ui.theme.mainRedColor
import com.lfb.android.footprint.ui.theme.rememberMapThemeConfig
import com.lfb.android.footprint.prefs.AppPrefs

@SuppressLint("DefaultLocale")
@Composable
fun MapCurrentStateInfoCard(
    altitude: Float,
    speed: Float,
    modifier: Modifier = Modifier
) {
    val themeConfig = rememberMapThemeConfig()
    var showSpeed by remember { mutableStateOf(false) }
    
    Box(
        modifier = modifier
            .size(width = 68.dp, height = 78.dp)  // 恢复原来的固定大小
            .shadow(
                elevation = 2.dp,
                shape = RoundedCornerShape(12.dp),
                spotColor = themeConfig.shadowColor
            )
            .background(
                color = themeConfig.backgroundColor,
                shape = RoundedCornerShape(12.dp)
            )
            .pointerInput(Unit) {
                detectHorizontalDragGestures { _, dragAmount ->
                    when {
                        dragAmount > 0 -> showSpeed = false
                        dragAmount < 0 -> showSpeed = true
                    }
                }
            }
            .padding(horizontal = 12.dp, vertical = 12.dp)
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier = Modifier.fillMaxSize()
        ) {
            Text(
                text = if (!showSpeed) "海拔 (米)" else "速度 (米/秒)",
                style = TextStyle(
                    fontSize = 8.sp,  // 恢复原来的字体大小
                    color = themeConfig.textColorSecondary
                ),
                maxLines = 1
            )
            
            Box(
                modifier = Modifier
                    .weight(1f)
                    .fillMaxWidth(),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = if (!showSpeed) 
                        String.format("%.2f", altitude) 
                    else 
                        String.format("%.2f", speed),
                    style = TextStyle(
                        color = mainRedColor
                    ),
                    fontSize = 18.sp,  // 保持原来的字体大小
                    maxLines = 1,
                    overflow = TextOverflow.Visible,
                    textAlign = TextAlign.Center
                )
            }
            
            Row(
                horizontalArrangement = Arrangement.Center,
                modifier = Modifier.fillMaxWidth()
            ) {
                Box(
                    Modifier
                        .size(6.dp)
                        .background(
                            color = if (!showSpeed) mainRedColor else themeConfig.textColorSecondary,
                            shape = RoundedCornerShape(3.dp)
                        )
                        .clickable { showSpeed = false }
                )
                Spacer(modifier = Modifier.width(4.dp))
                Box(
                    Modifier
                        .size(6.dp)
                        .background(
                            color = if (showSpeed) mainRedColor else themeConfig.textColorSecondary,
                            shape = RoundedCornerShape(3.dp)
                        )
                        .clickable { showSpeed = true }
                )
            }
        }
    }
} 