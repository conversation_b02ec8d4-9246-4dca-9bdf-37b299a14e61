package com.lfb.android.footprint.Manager

import android.annotation.SuppressLint
import android.app.Activity
import android.app.Application
import android.os.Bundle
import com.lfb.android.footprint.ui.components.VersionUpdateDialogManager

/**
 * 全局版本更新对话框管理器
 * 自动在所有Activity中附加VersionUpdateDialog，无需手动添加Container
 * 
 * 使用方式：
 * 在Application的onCreate中调用：
 * GlobalVersionUpdateDialog.init(this)
 * 
 * 显示对话框：
 * GlobalVersionUpdateDialog.show(
 *     currentVersion = 1,
 *     latestVersion = 2,
 *     isForceUpdate = false,
 *     downloadUrl = "https://...",
 *     onUpdate = { /* 打开下载链接 */ },
 *     onCancel = { /* 取消操作 */ }
 * )
 */
@SuppressLint("StaticFieldLeak")
object GlobalVersionUpdateDialog {
    
    private var isInitialized = false
    private var currentActivity: Activity? = null
    
    /**
     * 初始化全局版本更新对话框
     * 在Application的onCreate中调用
     */
    fun init(application: Application) {
        if (isInitialized) {
            return
        }
        
        application.registerActivityLifecycleCallbacks(object : Application.ActivityLifecycleCallbacks {
            override fun onActivityCreated(activity: Activity, savedInstanceState: Bundle?) {
                // Activity创建时不立即附加
            }
            
            override fun onActivityStarted(activity: Activity) {
                // Activity启动时不立即附加
            }

            override fun onActivityResumed(activity: Activity) {
                // Activity恢复时附加VersionUpdateDialog
                currentActivity = activity
                VersionUpdateDialogManager.attachToWindow(activity)
            }
            
            override fun onActivityPaused(activity: Activity) {
                // Activity暂停时不分离，保持显示
            }
            
            override fun onActivityStopped(activity: Activity) {
                // Activity停止时分离VersionUpdateDialog
                if (currentActivity == activity) {
                    VersionUpdateDialogManager.detachFromWindow()
                    currentActivity = null
                }
            }
            
            override fun onActivitySaveInstanceState(activity: Activity, outState: Bundle) {
                // 不需要处理
            }
            
            override fun onActivityDestroyed(activity: Activity) {
                // Activity销毁时确保分离
                if (currentActivity == activity) {
                    VersionUpdateDialogManager.detachFromWindow()
                    currentActivity = null
                }
            }
        })
        
        isInitialized = true
    }
    
    /**
     * 显示版本更新对话框
     */
    fun show(
        currentVersion: Int,
        latestVersion: Int,
        isForceUpdate: Boolean,
        downloadUrl: String,
        onUpdate: () -> Unit,
        onCancel: (() -> Unit)? = null,
        onIgnore: (() -> Unit)? = null
    ) {
        VersionUpdateDialogManager.show(
            currentVersion = currentVersion,
            latestVersion = latestVersion,
            isForceUpdate = isForceUpdate,
            downloadUrl = downloadUrl,
            onUpdate = onUpdate,
            onCancel = onCancel,
            onIgnore = onIgnore
        )
    }
    
    /**
     * 关闭对话框
     */
    fun dismiss() {
        VersionUpdateDialogManager.dismiss()
    }
    
    /**
     * 获取当前Activity
     */
    fun getCurrentActivity(): Activity? {
        return currentActivity
    }
    
    /**
     * 检查是否已初始化
     */
    fun isInitialized(): Boolean {
        return isInitialized
    }
}
