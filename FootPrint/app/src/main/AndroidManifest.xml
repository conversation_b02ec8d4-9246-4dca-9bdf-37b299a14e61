<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <application
        android:name=".MyApplication"
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher"
        android:supportsRtl="true"
        android:theme="@style/Theme.FootPrint"
        android:usesCleartextTraffic="true"
        android:networkSecurityConfig="@xml/network_security_config"
        tools:targetApi="31">
        <activity
            android:name=".GuideActivity"
            android:exported="true"
            android:label="@string/app_name"
            android:theme="@style/Theme.FootPrint">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity
            android:name=".MainMapActivity"
            android:exported="false"
            android:label="@string/app_name"
            android:theme="@style/Theme.FootPrint">
        </activity>
        <activity
            android:name=".SettingsActivity"
            android:exported="false"
            android:label="设置"
            android:theme="@style/Theme.FootPrint" />
        <activity
            android:name=".ImportExportActivity"
            android:exported="false"
            android:label="导入导出"
            android:theme="@style/Theme.FootPrint" />
        <activity
            android:name=".LocationModeActivity"
            android:exported="false"
            android:label="定位模式"
            android:theme="@style/Theme.FootPrint" />
        <activity
            android:name=".AboutActivity"
            android:exported="false"
            android:label="关于足迹"
            android:theme="@style/Theme.FootPrint" />
        <activity
            android:name=".LabActivity"
            android:exported="false"
            android:label="足迹实验室"
            android:theme="@style/Theme.FootPrint" />
        <activity
            android:name=".TrackOverviewActivity"
            android:exported="false"
            android:label="轨迹总览"
            android:theme="@style/Theme.FootPrint" />
        <activity
            android:name=".ui.components.LoginActivity"
            android:exported="false"
            android:label="登录"
            android:theme="@style/Theme.FootPrint" />
        <activity
            android:name=".ui.components.VipSubscriptionActivity"
            android:exported="false"
            android:label="VIP订阅"
            android:theme="@style/Theme.FootPrint" />
        
        <!-- 微信登录回调Activity，必须放在包名.wxapi包下 -->
        <activity
            android:name=".wxapi.WXEntryActivity"
            android:exported="true"
            android:launchMode="singleTop"
            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
        
        <!-- 微信支付回调Activity，必须放在包名.wxapi包下 -->
        <!-- Android 13开始去除intent-filter，避免Intent过滤器屏蔽问题 -->
        <activity
            android:name=".wxapi.WXPayEntryActivity"
            android:label="@string/app_name"
            android:theme="@android:style/Theme.Translucent.NoTitleBar"
            android:exported="true"
            android:taskAffinity="com.lfb.android.footprint"
            android:launchMode="singleTask" />
        
        <service
            android:name=".service.LocationService"
            android:foregroundServiceType="location" />

        <!-- 地理围栏广播接收器 -->
        <receiver
            android:name=".geofence.GeofenceBroadcastReceiver"
            android:exported="false" />

        <!-- 系统广播接收器，用于监听开机启动等系统事件 -->
        <receiver
            android:name=".keepalive.SystemBroadcastReceiver"
            android:exported="false">
            <!-- 开机启动相关广播（可以静态注册）-->
            <intent-filter android:priority="1000">
                <action android:name="android.intent.action.BOOT_COMPLETED" />
                <action android:name="android.intent.action.QUICKBOOT_POWERON" />
                <action android:name="com.htc.intent.action.QUICKBOOT_POWERON" />
                <action android:name="android.intent.action.REBOOT" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <!-- 网络连接变化广播（仅适用于Android 7.0以下）-->
            <intent-filter>
                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
            </intent-filter>
        </receiver>

        <!-- WakeLock 定时唤醒广播接收器，用于 Doze 模式下的定期唤醒 -->
        <receiver
            android:name=".service.LocationService$WakeLockAlarmReceiver"
            android:exported="false">
            <intent-filter>
                <action android:name="com.lfb.android.footprint.WAKELOCK_ALARM" />
            </intent-filter>
        </receiver>
    </application>

    <!-- Include this permission to grab user's general location -->
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <!-- Include only if your app benefits from precise location access. -->
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <!-- Add background location permission -->
    <uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_LOCATION" />
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
    <!-- 屏幕唤醒权限，用于保持屏幕常亮 -->
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    
    <!-- 保活相关权限 -->
    <!-- 电池优化相关权限 -->
    <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />
    <!-- 开机启动权限 -->
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
    <!-- 查看正在运行的应用权限 -->
    <uses-permission android:name="android.permission.GET_TASKS" />
    <!-- 网络状态变化监听权限 -->
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <!-- 网络访问权限，用于微信登录API调用 -->
    <uses-permission android:name="android.permission.INTERNET" />
    <!-- 设备管理权限 -->
    <uses-permission android:name="android.permission.DEVICE_POWER" 
        tools:ignore="ProtectedPermissions" />
    <!-- 系统设置权限（用于跳转到系统设置页面） -->
    <uses-permission android:name="android.permission.WRITE_SETTINGS" 
        tools:ignore="ProtectedPermissions" />
    <!-- 精确闹钟权限（Android 12+） -->
    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" />
    <!-- 使用精确闹钟权限（Android 14+） -->
    <uses-permission android:name="android.permission.USE_EXACT_ALARM" />

    <!-- 指定微信包名 -->
    <queries>
        <package android:name="com.tencent.mm" />
    </queries>
</manifest>