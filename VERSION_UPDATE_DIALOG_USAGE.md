# 版本更新对话框使用说明

## 概述

`VersionUpdateDialog` 已改造为全局可弹出的提示框，使用方式类似于 `DropdownAlert`。

## 架构

### 核心组件

1. **VersionUpdateDialogManager** - 对话框管理器（在 `VersionUpdateDialog.kt` 中）
   - 管理对话框状态
   - 提供 `show()` 和 `dismiss()` 方法
   - 提供 `Container()` 组件用于渲染
   - 提供 `attachToWindow()` 和 `detachFromWindow()` 方法

2. **GlobalVersionUpdateDialog** - 全局管理器（独立文件）
   - 监听 Activity 生命周期
   - 自动附加/分离对话框到 Window
   - 提供全局调用接口

3. **VersionUpdateDialog** - UI 组件
   - 实际的对话框 UI 实现
   - 支持强制更新和可选更新两种模式

## 初始化

在 `MyApplication.onCreate()` 中已自动初始化：

```kotlin
// 初始化全局版本更新对话框
com.lfb.android.footprint.Manager.GlobalVersionUpdateDialog.init(this)
```

## 使用方式

### 1. 显示对话框

```kotlin
// 强制更新
GlobalVersionUpdateDialog.show(
    currentVersion = 1,
    latestVersion = 2,
    isForceUpdate = true,
    downloadUrl = "https://example.com/app.apk",
    onUpdate = {
        // 打开下载链接
        VersionManager.openDownloadUrl(context, downloadUrl)
    }
)

// 可选更新
GlobalVersionUpdateDialog.show(
    currentVersion = 1,
    latestVersion = 2,
    isForceUpdate = false,
    downloadUrl = "https://example.com/app.apk",
    onUpdate = {
        // 打开下载链接
        VersionManager.openDownloadUrl(context, downloadUrl)
    },
    onCancel = {
        // 用户取消更新
        GlobalVersionUpdateDialog.dismiss()
    }
)
```

### 2. 关闭对话框

```kotlin
GlobalVersionUpdateDialog.dismiss()
```

### 3. 在 MainMapActivity 中的实际使用

```kotlin
MapScreen(
    onVersionCheck = { config ->
        val updateResult = VersionManager.checkUpdate(
            context = this,
            latestVersionCode = config.latestVersionCode,
            minVersionCode = config.minVersionCode
        )
        
        val currentVersionCode = VersionManager.getCurrentVersionCode(this)
        
        when (updateResult) {
            is VersionManager.UpdateCheckResult.ForceUpdate -> {
                GlobalVersionUpdateDialog.show(
                    currentVersion = currentVersionCode,
                    latestVersion = config.latestVersionCode,
                    isForceUpdate = true,
                    downloadUrl = config.downloadUrl,
                    onUpdate = {
                        VersionManager.openDownloadUrl(this, config.downloadUrl)
                    }
                )
            }
            is VersionManager.UpdateCheckResult.OptionalUpdate -> {
                GlobalVersionUpdateDialog.show(
                    currentVersion = currentVersionCode,
                    latestVersion = config.latestVersionCode,
                    isForceUpdate = false,
                    downloadUrl = config.downloadUrl,
                    onUpdate = {
                        VersionManager.openDownloadUrl(this, config.downloadUrl)
                    },
                    onCancel = {
                        GlobalVersionUpdateDialog.dismiss()
                    }
                )
            }
            is VersionManager.UpdateCheckResult.NoUpdate -> {
                // 无需更新
            }
        }
    }
)
```

## 特性

### 1. 全局可用
- 在任何地方都可以通过 `GlobalVersionUpdateDialog.show()` 显示对话框
- 无需在 Compose 中手动添加组件

### 2. 自动生命周期管理
- 自动跟随 Activity 生命周期
- Activity 切换时自动处理

### 3. 两种更新模式

#### 强制更新
- 不显示"取消"按钮
- 不能通过点击外部或返回键关闭
- 用户必须点击"前去更新"

#### 可选更新
- 显示"取消"和"前去更新"两个按钮
- 可以通过点击外部或返回键关闭
- 用户可以选择稍后更新

### 4. 对话框持久性
- 点击"前去更新"后对话框不会自动关闭
- 需要手动调用 `dismiss()` 或用户取消（可选更新模式）

## 与 DropdownAlert 的对比

| 特性 | DropdownAlert | VersionUpdateDialog |
|------|--------------|---------------------|
| 显示位置 | 屏幕顶部下拉 | 屏幕中央对话框 |
| 自动关闭 | 是（3秒后） | 否（需手动关闭） |
| 用户交互 | 点击关闭 | 按钮操作 |
| 使用场景 | 临时提示消息 | 重要操作确认 |
| 管理器 | GlobalDropdownAlert | GlobalVersionUpdateDialog |

## 文件清单

### 修改的文件
- `FootPrint/app/src/main/java/com/lfb/android/footprint/ui/components/VersionUpdateDialog.kt`
  - 添加 `VersionUpdateDialogManager` 对象
  - 添加 `VersionUpdateDialogState` 类
  - 保留原有的 `VersionUpdateDialog` 组件

### 新增的文件
- `FootPrint/app/src/main/java/com/lfb/android/footprint/Manager/GlobalVersionUpdateDialog.kt`
  - 全局管理器实现
  - Activity 生命周期监听

### 更新的文件
- `FootPrint/app/src/main/java/com/lfb/android/footprint/MyApplication.kt`
  - 添加 `GlobalVersionUpdateDialog.init(this)` 初始化

- `FootPrint/app/src/main/java/com/lfb/android/footprint/MainMapActivity.kt`
  - 移除本地状态管理
  - 改用 `GlobalVersionUpdateDialog.show()` 显示对话框

## 优势

1. **代码简化**: 不需要在每个 Activity 中管理对话框状态
2. **全局可用**: 可以在任何地方显示对话框
3. **统一管理**: 所有对话框通过统一的管理器控制
4. **易于维护**: 对话框逻辑集中在一个地方
5. **生命周期安全**: 自动处理 Activity 生命周期

## 注意事项

1. 必须在 `Application.onCreate()` 中初始化
2. 强制更新模式下对话框无法关闭，确保下载链接有效
3. 点击"前去更新"后对话框不会自动关闭，需要根据业务逻辑决定是否关闭
4. 对话框会覆盖在所有内容之上，包括其他对话框
