# APP版本号上报功能实现

## 概述
在用户登录时上报APP版本号，并在Web管理后台的用户详情页面显示。

## 实现内容

### 1. Android端修改

#### WechatLoginManager.kt
在`handleAuthSuccess`方法中获取并上报APP版本号：
```kotlin
// 获取APP版本号
val appVersion = VersionManager.getCurrentVersionName(context)

// 调用登录接口时传递版本号
val loginResponse = ApiService.wechatLogin(code, deviceId, appVersion)
```

#### ApiService.kt
修改`wechatLogin`方法签名，添加`appVersion`参数：
```kotlin
fun wechatLogin(code: String, deviceId: String, appVersion: String): LoginResponse {
    val requestBody = JSONObject().apply {
        put("code", code)
        put("device_id", deviceId)
        put("app_version", appVersion)
    }
    // ...
}
```

### 2. 后端服务

#### 认证接口（auth.py）
`wechat_login`接口接收和存储`app_version`：
- 接收请求参数中的`app_version`
- 调用`UserDevice.record_login`存储版本信息

#### 管理后台API（admin.py）
`api_users`接口返回用户的APP版本：
- 直接从`user.app_version`字段读取（无需JOIN查询）
- 在用户列表数据中添加`app_version`字段
- 性能优化：避免了对user_devices表的额外查询

#### 数据模型（user_device.py）
`UserDevice`模型包含`app_version`字段：
- 类型：`String(32)`
- 可选字段
- 在每次登录时更新

### 3. Web管理页面

#### 用户列表页面（users.html）
在用户列表表格中添加"APP版本"列：
- 位置：VIP状态列之后
- 显示：蓝色徽章 `v{版本号}`（如 v1.0.0）
- 无版本时显示：`-`
- 显示的是用户最后一次使用的APP版本（从活跃设备获取）

#### 用户详情页面
在用户详情模态框的"设备信息"标签页中：
- 独立的"APP版本"列
- 每个设备显示其对应的版本号
- 显示格式：蓝色徽章 `v{版本号}`

## 数据流程

1. 用户在Android端发起微信登录
2. `WechatLoginManager`获取设备ID和APP版本号
3. 调用`ApiService.wechatLogin`上报到服务端
4. 服务端`/auth/wechat/login`接口接收数据
5. 更新`user.app_version`字段（存储在users表，快速查询）
6. 同时调用`UserDevice.record_login`记录设备历史（存储在user_devices表）
7. 管理后台用户列表直接从`user.app_version`读取显示（无需JOIN查询）
8. 用户详情页面从`user_devices`表读取每个设备的完整历史

## 数据存储策略

### users表的app_version字段
- **用途**：存储用户当前使用的APP版本
- **更新时机**：每次登录时更新
- **优点**：查询快速，无需JOIN，适合列表页面展示
- **数据库迁移**：`add_app_version_to_users.py`

### user_devices表的app_version字段
- **用途**：存储每个设备的版本号历史
- **更新时机**：每次登录时更新对应设备记录
- **优点**：保留完整历史，可追溯每个设备的版本变化
- **用途场景**：用户详情页面的设备列表

## 版本号获取

使用`VersionManager.getCurrentVersionName(context)`获取版本名称（如"1.0.0"），而非版本号（如1）。
这样在管理后台显示更友好。

## 注意事项

- 版本号在每次登录时更新
- 如果用户升级APP后重新登录，版本号会自动更新
- 历史设备的版本号保留最后一次登录时的版本

## 部署步骤

### 数据库迁移
```bash
cd FootPrintSever
flask db upgrade
```

迁移文件：`migrations/versions/add_app_version_to_users.py`
- 添加`app_version`字段到`users`表
- 类型：`String(32)`，可为空

### 验证
```bash
# 检查当前数据库版本
flask db current
# 应该显示: d4e6f7a8b9c0 (head)

# 验证字段已添加
python -c "from app import create_app; from app.models.user import User; app = create_app(); app.app_context().push(); print([c.name for c in User.__table__.columns])"
# 应该包含: app_version
```

## 性能优化说明

### 优化前
- 用户列表查询需要JOIN `user_devices`表
- 每个用户需要额外查询活跃设备
- 大量用户时性能较差

### 优化后
- 直接从`users`表读取`app_version`
- 无需JOIN查询
- 查询性能显著提升
- 适合大规模用户列表展示
