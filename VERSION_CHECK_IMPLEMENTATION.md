# 版本检查功能实现文档

## 功能概述

实现了 Android 应用的版本检查功能，包括：
- 后台管理页面配置版本信息
- API 接口返回版本配置
- Android 端检查版本并显示更新提示
- 支持可选更新和强制更新两种模式

## 实现内容

### 1. 后端实现

#### 1.1 数据库模型
**文件**: `FootPrintSever/app/models/app_settings.py`
- 创建 `AppSettings` 模型存储应用配置
- 字段：
  - `latest_version_code`: 最新版本号（整数）
  - `min_version_code`: 最低可用版本号（整数）
  - `download_url`: 新版本下载地址（字符串）
- 提供静态方法 `get_settings()` 和 `update_settings()` 用于单例模式访问

#### 1.2 数据库迁移
**文件**: `FootPrintSever/migrations/versions/20251102200032_create_app_settings_table.py`
- 创建 `app_settings` 表
- 插入默认配置（版本号均为 1）

#### 1.3 管理后台页面
**文件**: `FootPrintSever/app/templates/admin/settings.html`
- 提供表单编辑三个配置项
- 包含输入验证和帮助文本
- 显示最后更新时间

#### 1.4 管理后台路由
**文件**: `FootPrintSever/app/routes/admin.py`
- 修改 `/admin/settings` 路由支持 GET 和 POST
- GET: 显示当前配置
- POST: 保存配置并验证（最低版本不能大于最新版本）

#### 1.5 API 接口修改
**文件**: `FootPrintSever/app/routes/billing.py`
- 修改 `/billing/member/status` 接口
- 在响应中添加 `version_config` 字段：
  ```json
  {
    "version_config": {
      "latest_version_code": 1,
      "min_version_code": 1,
      "download_url": "https://example.com/app.apk"
    }
  }
  ```

### 2. Android 端实现

#### 2.1 版本管理器
**文件**: `FootPrint/app/src/main/java/com/lfb/android/footprint/Manager/VersionManager.kt`
- 获取当前应用版本号（`getCurrentVersionCode()`）
- 检查版本更新（`checkUpdate()`）
- 返回更新检查结果：
  - `NoUpdate`: 无需更新
  - `OptionalUpdate`: 可选更新
  - `ForceUpdate`: 强制更新
- 打开下载链接（`openDownloadUrl()`）

#### 2.2 版本更新对话框
**文件**: `FootPrint/app/src/main/java/com/lfb/android/footprint/ui/components/VersionUpdateDialog.kt`
- Compose 实现的对话框组件
- 支持两种模式：
  - **可选更新**: 显示"取消"和"前去更新"两个按钮
  - **强制更新**: 只显示"前去更新"按钮，不可关闭
- 点击"前去更新"打开浏览器，对话框保持显示

#### 2.3 会员检查管理器修改
**文件**: `FootPrint/app/src/main/java/com/lfb/android/footprint/Manager/MembershipCheckManager.kt`
- 添加 `VersionConfig` 数据类
- 修改 `RemoteCheckResult` 包含版本配置
- 修改 `checkRemoteMembershipStatus()` 解析版本配置
- 修改 `checkMembershipStatus()` 添加 `onVersionCheck` 回调参数
- 在联网检查成功后触发版本检查回调

#### 2.4 主界面集成
**文件**: `FootPrint/app/src/main/java/com/lfb/android/footprint/MainMapActivity.kt`
- 添加版本对话框状态管理
- 传递 `onVersionCheck` 回调到 `MapScreen`
- 根据版本检查结果显示对应的对话框

#### 2.5 MapScreen 修改
**文件**: `FootPrint/app/src/main/java/com/lfb/android/footprint/ui/components/mapScreen/MapScreen.kt`
- 添加 `onVersionCheck` 参数
- 在 `ON_RESUME` 生命周期事件中触发会员检查
- 会员检查时传递版本检查回调

## 使用流程

### 后端配置

1. 登录管理后台
2. 访问 `/admin/settings` 页面
3. 配置三个参数：
   - 最新版本号（如：2）
   - 最低可用版本号（如：1）
   - 下载地址（如：https://example.com/footprint-v2.apk）
4. 点击"保存设置"

### Android 端行为

1. 用户打开应用或恢复应用时
2. 如果满足会员检查条件（3天间隔）
3. 调用 `/billing/member/status` 接口
4. 解析返回的版本配置
5. 比较当前版本与配置的版本号：
   - **当前版本 < 最低版本**: 显示强制更新对话框
   - **最低版本 <= 当前版本 < 最新版本**: 显示可选更新对话框
   - **当前版本 >= 最新版本**: 不显示对话框
6. 用户点击"前去更新"打开浏览器下载
7. 强制更新时对话框保持显示，用户必须更新

## 版本号说明

- 使用 `versionCode`（整数）进行版本比较
- `versionCode` 在 `build.gradle.kts` 中定义
- 每次发布新版本时递增 `versionCode`
- 示例：
  ```kotlin
  versionCode = 1  // 第一版
  versionCode = 2  // 第二版
  versionCode = 3  // 第三版
  ```

## 部署步骤

### 后端部署

1. 运行数据库迁移：
   ```bash
   cd FootPrintSever
   flask db upgrade
   ```

2. 重启服务：
   ```bash
   # 如果使用 Docker
   docker-compose restart
   
   # 如果使用 Gunicorn
   sudo systemctl restart footprint
   ```

3. 登录管理后台配置版本信息

### Android 端部署

1. 确保 `build.gradle.kts` 中的 `versionCode` 已正确设置
2. 编译并发布新版本 APK
3. 将 APK 上传到服务器
4. 在管理后台配置下载地址

## 测试建议

### 后端测试

1. 访问管理后台设置页面，验证表单显示和保存
2. 测试输入验证（最低版本 > 最新版本应报错）
3. 调用 `/billing/member/status` 接口，验证返回包含 `version_config`

### Android 端测试

1. **可选更新测试**:
   - 设置后台：latest=2, min=1
   - 应用版本：versionCode=1
   - 预期：显示可选更新对话框，可以取消

2. **强制更新测试**:
   - 设置后台：latest=3, min=2
   - 应用版本：versionCode=1
   - 预期：显示强制更新对话框，不可取消

3. **无需更新测试**:
   - 设置后台：latest=2, min=1
   - 应用版本：versionCode=2
   - 预期：不显示对话框

4. **下载链接测试**:
   - 点击"前去更新"
   - 预期：打开浏览器访问配置的下载地址
   - 对话框保持显示

## 注意事项

1. **版本号管理**: 每次发布新版本必须递增 `versionCode`
2. **下载地址**: 确保配置的下载地址可访问
3. **强制更新**: 谨慎使用强制更新，会影响用户体验
4. **检查频率**: 当前设置为3天检查一次，可在 `MembershipCheckManager` 中调整
5. **对话框持久性**: 点击"前去更新"后对话框不会关闭，符合需求
6. **网络异常**: 如果网络请求失败，不会显示版本更新对话框

## 相关文件清单

### 后端文件
- `FootPrintSever/app/models/app_settings.py` - 数据模型
- `FootPrintSever/migrations/versions/20251102200032_create_app_settings_table.py` - 数据库迁移
- `FootPrintSever/app/templates/admin/settings.html` - 管理页面
- `FootPrintSever/app/routes/admin.py` - 管理路由（修改）
- `FootPrintSever/app/routes/billing.py` - API 接口（修改）

### Android 文件
- `FootPrint/app/src/main/java/com/lfb/android/footprint/Manager/VersionManager.kt` - 版本管理器
- `FootPrint/app/src/main/java/com/lfb/android/footprint/ui/components/VersionUpdateDialog.kt` - 对话框组件
- `FootPrint/app/src/main/java/com/lfb/android/footprint/Manager/MembershipCheckManager.kt` - 会员检查（修改）
- `FootPrint/app/src/main/java/com/lfb/android/footprint/MainMapActivity.kt` - 主界面（修改）
- `FootPrint/app/src/main/java/com/lfb/android/footprint/ui/components/mapScreen/MapScreen.kt` - 地图界面（修改）

## 完成状态

✅ 后端数据库模型和迁移
✅ 后端管理页面和路由
✅ 后端 API 接口修改
✅ Android 版本管理器
✅ Android 版本更新对话框
✅ Android 会员检查集成
✅ Android 主界面集成
✅ 代码语法检查通过

功能已完整实现，可以进行部署和测试。
