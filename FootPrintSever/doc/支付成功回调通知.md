支付成功回调通知
更新时间：2024.12.27
一、回调描述
用户使用普通支付（APP支付/H5支付/JSAPI支付/Native支付/小程序支付）功能，当用户成功支付订单后，微信支付会通过POST的请求方式，向商户预先设置的回调地址(APP支付/H5支付/JSAPI支付/Native支付/小程序支付下单接口传入的notify_url)发送回调通知，让商户知晓用户已完成支付。

注意： 商户侧对微信支付回调IP有防火墙策略限制的，需要对微信回调IP段开通白名单，否则会导致收不到回调(微信支付回调被商户防火墙拦截)，详情参考回调处理逻辑注意事项。

二、回调处理步骤
1、商户接收回调通知报文
微信支付会通过POST的方式向回调地址发送回调报文，回调通知的请求主体中会包含JSON格式的通知参数，具体的通知参数列表如下：

通知参数

 id   必填  string(36)

【通知ID】回调通知的唯一编号。

 create_time   必填  string(32)

【通知创建时间】

1、定义：本次回调通知创建的时间。

2、格式：遵循rfc3339标准格式：yyyy-MM-DDTHH:mm:ss+TIMEZONE。yyyy-MM-DD 表示年月日；T 字符用于分隔日期和时间部分；HH:mm:ss 表示具体的时分秒；TIMEZONE 表示时区（例如，+08:00 对应东八区时间，即北京时间）。

示例：2015-05-20T13:29:35+08:00 表示北京时间2015年5月20日13点29分35秒。

 event_type   必填  string(32)

【通知的类型】微信支付回调通知的类型。
支付成功通知的类型为TRANSACTION.SUCCESS。

 resource_type   必填  string(32)

【通知数据类型】通知的资源数据类型，固定为encrypt-resource。

 resource    必填 object

【通知数据】通知资源数据。

	属性
 summary   必填  string(64)

【回调摘要】微信支付对回调内容的摘要备注。

支付成功结果通知


1{
2    "id": "EV-2018022511223320873",
3    "create_time": "2015-05-20T13:29:35+08:00",
4    "resource_type": "encrypt-resource",
5    "event_type": "TRANSACTION.SUCCESS",
6    "summary": "支付成功",
7    "resource": {
8        "original_type": "transaction",
9        "algorithm": "AEAD_AES_256_GCM",
10        "ciphertext": "",
11        "associated_data": "",
12        "nonce": ""
13    }
14}
2、回调验签与应答
商户接收到回调通知报文后，需在5秒内完成对报文的验签，并应答回调通知。

2.1、对回调通知进行验签
回调报文的HTTP请求头中会包含报文的签名信息，用于验签，具体如下：

参数
描述
Wechatpay-Serial
验签的微信支付平台证书序列号/微信支付公钥ID
Wechatpay-Signature
验签的签名值
Wechatpay-Timestamp
验签的时间戳
Wechatpay-Nonce
验签的随机字符串
验签需使用请求头中的【Wechatpay-Timestamp】、【Wechatpay-Nonce】以及请求主体中JSON格式的通知参数构建出验签串，然后使用【Wechatpay-Serial】对应的微信支付平台证书/微信支付公钥对验签串和【Wechatpay-Signature】进行验签，确保接收的回调内容是来自微信支付。

商户可通过HTTP头"Wechatpay-Serial"中的证书序列号判断选择对应的证书验签，微信支付公钥的序列号固定采用"PUB_KEY_ID_数字串"格式（例如：PUB_KEY_ID_3000000001），若请求头中的序列号不符合该格式，则应使用平台证书进行验签。

详细验签步骤请参考：如何验证签名

微信支付会在极少数通知回调中返回以“WECHATPAY/SIGNTEST/”开头的错误【Wechatpay-Signature】，以检测商户系统是否正确验证签名。商户请参考：如何应对签名探测流量进行处理。

签名探测流量HTTP头中的Wechatpay-Signature示例：


1WECHATPAY/SIGNTEST/c0k+ZP6cSbveFpn0U5Bhq1Evz0A0rmmhGyuFXGqAtrlspDr3wrmaeauXJT6YYD4OmnDi767TImhRdV9hdmU0T5ZVfkOB/zka3mYthkxJ9V6UMoI
24QLGogSG1mnYjZGa6zGy1+8WInqosp0+6eBJuul55xwf3oEIpNMxAl4NL0QHr5nLfB0b0PZQSU9rZneOtDjdNtDCGEtcwV6H1eTdLpFrw2wCtiWJDw6tQwR1IfGVtdE4FK
3JQvYmOT7udgR6XfLdvzwbJsifpxvuG9q23OQF1i4PndT7AP8ykhKUEZayTrYGWdobrljFh2nu9Ng7divjg==
2.2、对回调通知进行应答
如何正确的对回调通知应答：

接收到通知后，立即进行验签，商户验签后，根据验签结果对回调进行应答：
验签通过：商户需告知微信支付接收回调成功，HTTP应答状态码需返回200或204，无需返回应答报文。
验签不通过：商户需告知微信支付接收回调失败，HTTP应答状态码需返回5XX或4XX，同时需返回应答报文：。
应答后再处理后面的业务逻辑（如更新订单状态），推荐异步处理，避免因业务处理耗时过长导致对回调通知应答超时；
 code   选填  string(32)

【返回状态码】错误码，FAIL为回调接收失败。

 message   选填  string(256)

【返回信息】返回信息，回调接收失败原因。

应答成功示例

200或204


1"无应答包体"
 

应答失败示例

5XX或4XX


1{  
2    "code": "FAIL",
3    "message": "失败"
4}
2.3、微信支付回调处理机制说明
商户系统不能仅依赖回调通知获取结果，需结合查询接口使用，避免遗漏或延迟问题。

微信支付接收到商户的应答后，会根据应答结果做对应的逻辑处理：

若商户应答回调接收成功，微信支付将不再重复发送该回调通知。若因网络或其他原因，商户收到了重复的回调通知，请做好重入设计并持续应答200

若商户应答回调接收失败，或超时(5s)未应答时，微信支付会按照（15s/15s/30s/3m/10m/20m/30m/30m/30m/60m/3h/3h/3h/6h/6h）的频次重复发送回调通知，直至微信支付接收到商户应答成功，或达到最大发送次数（15次）

3、对回调通知内容进行解密
为了保证业务信息的安全性，微信支付将业务信息进行了AES-256-GCM加密，并通过参数resource将加密信息回调给商户，商户需要进行解密后才能获取到业务信息。

解密步骤如下：

获取商户平台上设置的APIv3密钥，设置APIv3密钥可参考文档：APIv3密钥设置方法；
通过回调通知参数resource.algorithm确认加密算法（目前仅支持AEAD_AES_256_GCM，算法的接口细节，请参考：rfc5116）。
使用APIv3密钥与回调通知参数resource.nonce和resource.associated_data，对数据密文resource.ciphertext进行解密，最终可得到JSON格式的业务信息。
解密示例代码可参考文档：如何解密回调报文

注意

使用Java进行回调解密，取JSON串内的参数值时，只需取引号内的内容进行解密。例："nonce":"123"，只需取值123，不用取加上引号的"123"。
 

resource中ciphertext解密后字段

 appid   必填  string(32)

【公众账号ID】商户下单时传入的公众账号ID。

 mchid   必填  string(32)

【商户号】商户下单时传入的商户号。

 out_trade_no   必填  string(32)

【商户订单号】商户下单时传入的商户系统内部订单号。

 transaction_id   必填  string(32)

【微信支付订单号】 微信支付侧订单的唯一标识。

 trade_type   必填  string(16)

【交易类型】 返回当前订单的交易类型，枚举值：

JSAPI：公众号支付、小程序支付
NATIVE：Native支付
APP：APP支付
MICROPAY：付款码支付
MWEB：H5支付
FACEPAY：刷脸支付
 trade_state   必填  string(32)

【交易状态】 交易状态，详细业务流转状态处理请参考开发指引-订单状态流转图。枚举值：

SUCCESS：支付成功
REFUND：转入退款
NOTPAY：未支付
CLOSED：已关闭
REVOKED：已撤销（仅付款码支付会返回）
USERPAYING：用户支付中（仅付款码支付会返回）
PAYERROR：支付失败（仅付款码支付会返回）
 trade_state_desc   必填  string(256)

【交易状态描述】 对交易状态的详细说明。

 bank_type   必填  string(32)

【银行类型】 用户支付方式说明，订单支付成功后返回，格式为：银行简码_具体类型(DEBIT借记卡/CREDIT信用卡/ECNY数字人民币)，例如ICBC_DEBIT代表工商银行借记卡，非银行卡支付类型(例如余额/零钱通等)统一为OTHERS，具体请参考《银行类型对照表》。

 attach   选填  string(128)

【商户数据包】商户下单时传入的自定义数据包，用户不可见，长度不超过128字符，若下单传入该参数，则订单支付成功后此接口和查询订单接口以及交易账单中会原样返回；若下单未传该参数，则不会返回。

 success_time   必填  string(64)

【支付完成时间】 

1、定义：用户完成订单支付的时间。

2、格式：遵循rfc3339标准格式：yyyy-MM-DDTHH:mm:ss+TIMEZONE。yyyy-MM-DD 表示年月日；T 字符用于分隔日期和时间部分；HH:mm:ss 表示具体的时分秒；TIMEZONE 表示时区（例如，+08:00 对应东八区时间，即北京时间）。

示例：2015-05-20T13:29:35+08:00 表示北京时间2015年5月20日13点29分35秒。

 payer   必填  object

【支付者信息】 订单的支付者信息。

	属性
 amount   必填  object

【订单金额】 订单金额信息，当支付成功时返回该字段。

	属性
 scene_info   选填  object

 【场景信息】 若下单传入该参数，则原样返回；若下单未传该参数，则不会返回。

	属性
 promotion_detail   选填  array

【优惠功能】 代金券信息，当订单支付时，有使用代金券时，该字段将返回所使用的代金券信息。

	数组
对resource中ciphertext进行解密后，得到的资源对象示例


1{
2    "transaction_id":"1217752501201407033233368018",
3    "amount":{
4        "payer_total":100,
5        "total":100,
6        "currency":"CNY",
7        "payer_currency":"CNY"
8    },
9    "mchid":"**********",
10    "trade_state":"SUCCESS",
11    "bank_type":"CMC",
12    "promotion_detail":[
13        {
14            "amount":100,
15            "wechatpay_contribute":0,
16            "coupon_id":"109519",
17            "scope":"GLOBAL",
18            "merchant_contribute":0,
19            "name":"单品惠-6",
20            "other_contribute":0,
21            "currency":"CNY",
22            "stock_id":"931386",
23            "goods_detail":[
24                {
25                    "goods_remark":"商品备注信息",
26                    "quantity":1,
27                    "discount_amount":1,
28                    "goods_id":"M1006",
29                    "unit_price":100
30                },
31                {
32                    "goods_remark":"商品备注信息",
33                    "quantity":1,
34                    "discount_amount":1,
35                    "goods_id":"M1006",
36                    "unit_price":100
37                }
38            ]
39        },
40        {
41            "amount":100,
42            "wechatpay_contribute":0,
43            "coupon_id":"109519",
44            "scope":"GLOBAL",
45            "merchant_contribute":0,
46            "name":"单品惠-6",
47            "other_contribute":0,
48            "currency":"CNY",
49            "stock_id":"931386",
50            "goods_detail":[
51                {
52                    "goods_remark":"商品备注信息",
53                    "quantity":1,
54                    "discount_amount":1,
55                    "goods_id":"M1006",
56                    "unit_price":100
57                },
58                {
59                    "goods_remark":"商品备注信息",
60                    "quantity":1,
61                    "discount_amount":1,
62                    "goods_id":"M1006",
63                    "unit_price":100
64                }
65            ]
66        }
67    ],
68    "success_time":"2018-06-08T10:34:56+08:00",
69    "payer":{
70        "openid":"oUpF8uMuAJO_M2pxb1Q9zNjWeS6o"
71    },
72    "out_trade_no":"1217752501201407033233368018",
73    "appid":"wxd678efh567hg6787",
74    "trade_state_desc":"支付成功",
75    "trade_type":"APP",
76    "attach":"自定义数据",
77    "scene_info":{
78        "device_id":"013467007045764"
79    }
80}
81