"""
WSGI entry point for the application
"""
import os
from dotenv import load_dotenv

# Load environment variables from .env files
# Only load .env.local in development environment for security
load_dotenv()

# Only load .env.local in development environment
flask_env = os.environ.get('FLASK_ENV', 'development')
if flask_env == 'development':
    load_dotenv('.env.local')

from app import create_app
from app.config import config

# Clear proxy environment variables to avoid SOCKS issues
proxy_vars = ['http_proxy', 'https_proxy', 'all_proxy', 'HTTP_PROXY', 'HTTPS_PROXY', 'ALL_PROXY']
for var in proxy_vars:
    if var in os.environ:
        del os.environ[var]

# Get configuration from environment
config_name = os.environ.get('FLASK_ENV', 'development')
app = create_app(config[config_name])

if __name__ == '__main__':
    import sys
    
    # 解析命令行参数
    port = int(os.environ.get('PORT', 5001))  # 默认使用5001端口
    
    # 检查命令行参数中是否指定了端口
    for i, arg in enumerate(sys.argv):
        if arg == '--port' and i + 1 < len(sys.argv):
            try:
                port = int(sys.argv[i + 1])
            except ValueError:
                print(f"Invalid port number: {sys.argv[i + 1]}")
                sys.exit(1)
    
    print(f"Starting FootPrint Server on port {port}")
    print(f"Admin Panel: http://localhost:{port}/admin/")
    print(f"API Base URL: http://localhost:{port}/")
    
    app.run(host='0.0.0.0', port=port, debug=True)
