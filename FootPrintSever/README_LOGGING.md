# FootPrint Subscription Service - Enhanced Logging System

## 概述

本项目已成功优化了structlog日志系统，实现了按天级别的日志文件分割和分类管理。新的日志系统提供了结构化日志记录、自动轮转、敏感数据屏蔽等功能。

## 主要特性

### 1. 按天自动轮转
- 每天午夜自动轮转日志文件
- 轮转后的文件自动添加日期后缀（如：`footprint_app.log.2024-01-15`）
- 可配置的保留期限，不同类型日志有不同的保留策略

### 2. 分类日志文件
- **footprint_app.log**: 主应用程序日志（保留30天）
- **footprint_api.log**: API请求/响应日志（保留30天）
- **footprint_errors.log**: 错误和警告日志（保留90天）
- **footprint_security.log**: 安全事件日志（保留365天）
- **footprint_payment.log**: 支付处理日志（保留180天）
- **footprint_audit.log**: 审计跟踪日志（保留7年）

### 3. 结构化JSON日志
- 所有日志都以JSON格式输出，便于解析和分析
- 自动添加时间戳、请求上下文、应用元数据
- 支持多种时间格式（ISO、Unix时间戳、日期/时间分离）

### 4. 敏感数据保护
- 自动识别和屏蔽敏感信息（密码、令牌、密钥等）
- 可配置的敏感字段列表
- 长字符串自动部分屏蔽

### 5. 环境配置支持
- 开发环境：详细日志，控制台输出
- 生产环境：优化性能，可选的日志聚合

## 文件结构

```
FootPrintSever/
├── app/utils/logging.py          # 核心日志功能
├── config/logging_config.py      # 日志配置管理
├── scripts/log_management.py     # 日志管理工具
├── examples/logging_example.py   # 使用示例
├── docs/logging_system.md        # 详细文档
└── logs/                         # 日志文件目录
    ├── footprint_app.log
    ├── footprint_api.log
    ├── footprint_errors.log
    ├── footprint_security.log
    ├── footprint_payment.log
    └── footprint_audit.log
```

## 快速开始

### 1. 配置日志系统

```python
from app.utils.logging import configure_logging
from flask import Flask

app = Flask(__name__)
app.config['LOG_LEVEL'] = 'INFO'
configure_logging(app)
```

### 2. 基本日志记录

```python
import structlog

logger = structlog.get_logger()
logger.info("应用启动", version="1.0.0", environment="production")
```

### 3. API日志记录

```python
from app.utils.logging import log_api_request, log_api_response

log_api_request("/api/v1/subscriptions", "POST", user_id="user_123")
log_api_response("/api/v1/subscriptions", "POST", 201, user_id="user_123")
```

### 4. 支付事件日志

```python
from app.utils.logging import log_payment_event

log_payment_event(
    "payment_completed",
    order_id="order_123",
    amount=29.99,
    currency="USD"
)
```

### 5. 安全事件日志

```python
from app.utils.logging import log_security_event

log_security_event(
    "failed_login",
    severity="medium",
    user_id="user_123",
    ip_address="*************"
)
```

### 6. 审计日志

```python
from app.utils.logging import log_audit_event

log_audit_event(
    "update",
    "subscription",
    resource_id="sub_123",
    user_id="user_456"
)
```

## 日志管理工具

### 查看日志状态
```bash
python scripts/log_management.py status
```

### 分析日志文件
```bash
python scripts/log_management.py analyze logs/footprint_api.log --hours 24
```

### 清理旧日志
```bash
python scripts/log_management.py cleanup --days 30
```

## 配置选项

### 环境变量
- `LOG_LEVEL`: 日志级别（DEBUG, INFO, WARNING, ERROR, CRITICAL）
- `LOG_DIR`: 日志文件目录
- `CONSOLE_LOGGING`: 是否启用控制台输出
- `FLASK_ENV`: 环境类型（development, production）

### 自定义配置
可以通过修改 `config/logging_config.py` 来自定义：
- 日志保留期限
- 敏感数据字段
- 轮转策略
- 日志格式

## 日志格式示例

```json
{
  "event": "API request",
  "timestamp": "2024-01-15T10:30:45.123456Z",
  "date": "2024-01-15",
  "time": "10:30:45",
  "timestamp_unix": 1705312245,
  "log_level": "INFO",
  "application": "footprint_subscription",
  "version": "1.0.0",
  "event_type": "api_request",
  "endpoint": "/api/v1/subscriptions",
  "method": "POST",
  "user_id": "user_123",
  "request_id": "req_456",
  "remote_addr": "*************"
}
```

## 性能优化

### 1. 异步日志处理
- 使用缓冲区减少I/O操作
- 批量写入提高性能

### 2. 日志级别控制
- 生产环境使用INFO级别
- 开发环境使用DEBUG级别

### 3. 文件轮转优化
- 按时间轮转避免大文件
- 压缩旧日志文件节省空间

## 监控和告警

### 1. 错误监控
- 自动检测ERROR和CRITICAL级别日志
- 可配置的告警阈值

### 2. 安全监控
- 监控安全事件模式
- 异常行为检测

### 3. 性能监控
- 慢请求检测
- API响应时间统计

## 最佳实践

### 1. 日志级别使用
- **DEBUG**: 详细的调试信息
- **INFO**: 一般的应用流程
- **WARNING**: 需要注意但不严重的问题
- **ERROR**: 严重问题需要处理
- **CRITICAL**: 可能导致程序中止的严重错误

### 2. 结构化数据
```python
# 推荐
logger.info("订单创建", order_id=123, user_id=456, amount=99.99)

# 不推荐
logger.info(f"用户{user_id}创建了订单{order_id}，金额{amount}")
```

### 3. 敏感数据处理
- 避免记录原始敏感数据
- 使用ID或哈希值代替敏感信息
- 定期审查日志内容

## 故障排除

### 常见问题
1. **日志文件未轮转**: 检查文件权限和磁盘空间
2. **日志丢失**: 验证日志目录存在且可写
3. **性能问题**: 调整日志级别或缓冲区大小

### 调试模式
```python
app.config['LOG_LEVEL'] = 'DEBUG'
```

## 升级说明

从旧的日志系统升级到新系统：

1. 备份现有日志文件
2. 更新应用配置
3. 重启应用服务
4. 验证新日志文件生成

## 技术支持

如有问题，请查看：
- `docs/logging_system.md` - 详细技术文档
- `examples/logging_example.py` - 完整使用示例
- 日志文件分析工具 - `scripts/log_management.py`
