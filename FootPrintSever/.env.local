# FootPrint 订阅服务 - 本地开发配置
# 复制此文件为 .env 并根据实际情况修改

# Flask Configuration
FLASK_ENV=development
FLASK_DEBUG=True
SECRET_KEY=d4fd60cec26b50c93d49d368e9a6e90c7e0eaa95042c3880

# Database Configuration (本地 MySQL)
DATABASE_URL=mysql+pymysql://footprint:footprint123@localhost:3306/footprint_subscription

# Redis Configuration (可选，如果没有安装 Redis 可以注释掉)
REDIS_URL=redis://localhost:6379/0

# WeChat OAuth Configuration
WECHAT_APPID=wxbd9c6afc23d98559
WECHAT_APPSECRET=c5a82f4ee1806acd62be144eb35b9314

# WeChat Pay Configuration
WXPAY_APPID=wxbd9c6afc23d98559
WXPAY_MCH_ID=1727996379
WXPAY_API_KEY=8wMuoF1k0F8hSEtM86WDg1jf5P2OrrNz
WXPAY_MCH_PRIVATE_KEY_PATH=./certs/apiclient_key.pem
WXPAY_CERT_SERIAL=7A70A0F2B47751DB4D29ECF93109A6995274FA40

WXPAY_PLATFORM_CERT_PATH=./certs/wechatpay_cert.pem

# Webhook Configuration (本地开发)
WEBHOOK_BASE_URL=http://localhost:8000

# JWT Configuration
JWT_SECRET_KEY=local-jwt-secret-key-for-development
JWT_ACCESS_TOKEN_EXPIRES=86400

# Logging
LOG_LEVEL=DEBUG

# APIv3解密的key
WECHAT_Private_Key=Hn0igP0c9zwpr9yJhsCWcTC1dwDLmfr2

WXPAY_PUB_KEY_ID=PUB_KEY_ID_0117279963792025101500382058003400
WXPAY_PUB_CERT_PATH=./certs/pub_key.pem