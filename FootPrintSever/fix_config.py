#!/usr/bin/env python3
"""
Fix WeChat Pay configuration by directly setting Flask app config
"""
import os
import sys
from dotenv import load_dotenv

# Load .env files
# Only load .env.local in development environment for security
load_dotenv()

# Only load .env.local in development environment
flask_env = os.environ.get('FLASK_ENV', 'development')
if flask_env == 'development':
    load_dotenv('.env.local')

from app import create_app
from app.config import config

def fix_config():
    """Fix configuration by directly setting app config"""
    print("Fixing WeChat Pay configuration...")
    
    # Create app
    config_name = os.environ.get('FLASK_ENV', 'development')
    app = create_app(config[config_name])
    
    with app.app_context():
        print("\n=== Current Environment Variables ===")
        print(f"WXPAY_APPID: {os.environ.get('WXPAY_APPID')}")
        print(f"WXPAY_MCH_ID: {os.environ.get('WXPAY_MCH_ID')}")
        print(f"WXPAY_API_KEY: {'*' * len(os.environ.get('WXPAY_API_KEY', '')) if os.environ.get('WXPAY_API_KEY') else 'None'}")
        print(f"WXPAY_MCH_PRIVATE_KEY_PATH: {os.environ.get('WXPAY_MCH_PRIVATE_KEY_PATH')}")
        print(f"WXPAY_CERT_SERIAL: {os.environ.get('WXPAY_CERT_SERIAL')}")
        
        print("\n=== Current Flask App Config ===")
        print(f"WXPAY_APPID: {app.config.get('WXPAY_APPID')}")
        print(f"WXPAY_MCH_ID: {app.config.get('WXPAY_MCH_ID')}")
        print(f"WXPAY_API_KEY: {'*' * len(app.config.get('WXPAY_API_KEY', '')) if app.config.get('WXPAY_API_KEY') else 'None'}")
        print(f"WXPAY_MCH_PRIVATE_KEY_PATH: {app.config.get('WXPAY_MCH_PRIVATE_KEY_PATH')}")
        print(f"WXPAY_CERT_SERIAL: {app.config.get('WXPAY_CERT_SERIAL')}")
        
        # Fix missing config
        if not app.config.get('WXPAY_API_KEY'):
            env_key = os.environ.get('WXPAY_API_KEY')
            if env_key:
                app.config['WXPAY_API_KEY'] = env_key
                print(f"\n✅ Fixed WXPAY_API_KEY in app config")
            else:
                print(f"\n❌ WXPAY_API_KEY not found in environment")
                return False
        
        print("\n=== Updated Flask App Config ===")
        print(f"WXPAY_APPID: {app.config.get('WXPAY_APPID')}")
        print(f"WXPAY_MCH_ID: {app.config.get('WXPAY_MCH_ID')}")
        print(f"WXPAY_API_KEY: {'*' * len(app.config.get('WXPAY_API_KEY', '')) if app.config.get('WXPAY_API_KEY') else 'None'}")
        print(f"WXPAY_MCH_PRIVATE_KEY_PATH: {app.config.get('WXPAY_MCH_PRIVATE_KEY_PATH')}")
        print(f"WXPAY_CERT_SERIAL: {app.config.get('WXPAY_CERT_SERIAL')}")
        
        # Test WeChat Pay service
        try:
            from app.services.wechat_pay import WeChatPayService
            wechat_pay_service = WeChatPayService()
            client = wechat_pay_service.wxpay
            if client:
                print("\n✅ WeChat Pay service initialized successfully!")
                return True
            else:
                print("\n❌ WeChat Pay service initialization failed")
                return False
        except Exception as e:
            print(f"\n❌ Error initializing WeChat Pay service: {str(e)}")
            return False

if __name__ == '__main__':
    success = fix_config()
    sys.exit(0 if success else 1)
