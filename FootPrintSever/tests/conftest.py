"""
Test configuration and fixtures
"""
import os
import pytest
import tempfile
from unittest.mock import Mock, patch

from app import create_app
from app.extensions import db
from app.config import Config


class TestConfig(Config):
    """Test configuration"""
    TESTING = True
    SQLALCHEMY_DATABASE_URI = 'sqlite:///:memory:'
    WTF_CSRF_ENABLED = False
    JWT_SECRET_KEY = 'test-jwt-secret-key'
    SECRET_KEY = 'test-secret-key'
    
    # WeChat test configuration
    WXPAY_APPID = 'test_appid'
    WXPAY_MCH_ID = 'test_mch_id'
    WXPAY_API_KEY = 'test_api_key'
    WXPAY_MCH_PRIVATE_KEY_PATH = '/tmp/test_private_key.pem'
    WXPAY_CERT_SERIAL = 'test_cert_serial'
    WXPAY_PLATFORM_CERT_PATH = '/tmp/test_platform_cert.pem'
    
    # WeChat OAuth test configuration
    WECHAT_APPID = 'test_wechat_appid'
    WECHAT_SECRET = 'test_wechat_secret'
    
    # Disable Redis for tests
    REDIS_URL = None


@pytest.fixture
def app():
    """Create application for testing"""
    app = create_app(TestConfig)
    
    with app.app_context():
        yield app


@pytest.fixture
def client(app):
    """Create test client"""
    return app.test_client()


@pytest.fixture
def db_session(app):
    """Create database session for testing"""
    db.create_all()
    yield db.session
    db.session.remove()
    db.drop_all()


@pytest.fixture
def sample_user(db_session):
    """Create a sample user for testing"""
    from app.models.user import User

    user = User(
        id=1,  # Explicitly set ID for testing
        openid='test_openid_123',
        unionid='test_unionid_123',
        nickname='Test User',
        avatar_url='https://example.com/avatar.jpg'
    )
    db_session.add(user)
    db_session.commit()
    return user


@pytest.fixture
def sample_plan(db_session):
    """Create a sample plan for testing"""
    from app.models.plan import Plan

    plan = Plan(
        id=1,  # Explicitly set ID for testing
        code='test_plan',
        name='Test Plan',
        period_days=30,
        price_cent=1999  # 19.99 yuan in cents
    )
    db_session.add(plan)
    db_session.commit()
    return plan


@pytest.fixture
def sample_order(db_session, sample_user, sample_plan):
    """Create a sample order for testing"""
    from app.models.order import Order, OrderStatus

    order = Order(
        id=1,  # Explicitly set ID for testing
        user_id=sample_user.id,
        plan_id=sample_plan.id,
        out_trade_no='TEST_ORDER_123',
        total_cent=sample_plan.price_cent,
        status=OrderStatus.PENDING
    )
    db_session.add(order)
    db_session.commit()
    return order


@pytest.fixture
def sample_subscription(db_session, sample_user, sample_plan):
    """Create a sample subscription for testing"""
    from app.models.subscription import Subscription, SubscriptionStatus
    from app.utils.time import utc_now, add_days

    subscription = Subscription(
        id=1,  # Explicitly set ID for testing
        user_id=sample_user.id,
        plan_id=sample_plan.id,
        status=SubscriptionStatus.ACTIVE,
        start_at=utc_now(),
        end_at=add_days(utc_now(), sample_plan.period_days)
    )
    db_session.add(subscription)
    db_session.commit()
    return subscription


@pytest.fixture
def auth_headers(sample_user):
    """Create authentication headers for testing"""
    from app.services.jwt_service import JWTService
    
    jwt_service = JWTService()
    token_response = jwt_service.create_token_response(sample_user)
    
    return {
        'Authorization': f"Bearer {token_response['access_token']}"
    }


@pytest.fixture
def mock_wechat_oauth():
    """Mock WeChat OAuth service"""
    with patch('app.services.wechat_oauth.WeChatOAuthService') as mock:
        mock_instance = Mock()
        mock.return_value = mock_instance
        
        # Mock successful login
        mock_instance.login_with_code.return_value = Mock(
            id=1,
            openid='test_openid_123',
            unionid='test_unionid_123',
            nickname='Test User',
            avatar_url='https://example.com/avatar.jpg'
        )
        
        yield mock_instance


@pytest.fixture
def mock_wechat_pay():
    """Mock WeChat Pay service"""
    with patch('app.services.wechat_pay.WeChatPayService') as mock:
        mock_instance = Mock()
        mock.return_value = mock_instance
        
        # Mock successful payment creation
        mock_instance.create_app_transaction.return_value = {
            'prepay_id': 'test_prepay_id_123'
        }
        
        # Mock successful callback verification
        mock_instance.verify_callback_signature.return_value = True
        mock_instance.decrypt_callback_resource.return_value = {
            'transaction_id': 'test_transaction_123',
            'out_trade_no': 'TEST_ORDER_123',
            'trade_state': 'SUCCESS',
            'amount': {'total': 1999}
        }
        
        yield mock_instance


@pytest.fixture
def mock_redis():
    """Mock Redis client"""
    with patch('app.extensions.redis_client') as mock:
        mock_redis = Mock()
        mock.return_value = mock_redis
        
        # Mock Redis operations
        mock_redis.get.return_value = None
        mock_redis.set.return_value = True
        mock_redis.delete.return_value = True
        mock_redis.ping.return_value = True
        
        yield mock_redis


@pytest.fixture
def create_test_certificates():
    """Create temporary test certificate files"""
    # Create temporary certificate files for testing
    private_key_content = """-----BEGIN PRIVATE KEY-----
MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC7VJTUt9Us8cKB
wQNfFNKCPCyg/VvhbSvvD6Y6cYvNNcSeWx5NXnH7tO2KuhooKoK9wTXiLmU3IOsx
XdkAEdDRSSuoRiQXcxUBjxqVHuuiAeMfxmvsL/0q8XfVBmXpXLVKHQlWgRRw2dTn
RcK/pAeeNiug2oLABFcISKBWnLH3Y8l8WaD+KZUIx9VCDpyelvWCcVMJn1Un/WJG
i/hgma4kwg1c3MjL2EBVXDyb1AcJjWnFdT+i6H1xRp5DGWpSHBV1CFuArt4gexQw
VCE5S4S5L0tztOXpWpzE4ydJqlsHiB6i+7qjSh6SYkZJjqE5owrQa03RjRKv/7QA
nVMRe0BrAgMBAAECggEBAKTmjaS6tkK8BlPXClTQ2vpz/N6uxDeS35mXpqasqskV
laAidgg/sWqpjXDbXr93otIMLlWsM+X0CqMDgSXKejLS2jx4GDjI1ZTXg++0AMJ8
sJ74pWzVDOfmCEQ/7wXs3+cbnXhKriO8Z036q92Qc1+N87SI38nkGa0ABH9CN83H
mQqt4fB7UdHzuIRe/me2PGhIq5ZBzj6h3BpoPGzEP+x3l9YmK8t/1cN0pqI+dQwY
dgfGjackLu/2qH80MCF7IyQaseZUOJyKrCLtSD/Iixv/hzDEUPfOCjFDgTpzf3cw
ta8+oE4wHCo1iI1/4TlPkwmXx4qSXtmw4aQPz7IDQvECgYEA8KNThCO2gsC2I9PQ
DM/8Cw0O983WCDY+ux+7x/f+TK2KAERYiE9AdxV5TOvFnPaH7PT7Ls2ckfhwpNVt
wD7BSsOEN+lEAr7UtMjcV+mgwfNp/bDhd9hn8892ZMGlNXm8lNZK2ecvHoZB1Qa4
aFwpUDVZLd8ckrNNHZzQHSK3XLkCgYEAx4VSqPFLA3VuHqDvLYwIeEsRBsXBxgKM
39PxzV5jdp9nBFcDwfu73t7VcmJz9Qd3lT8GYjuB/VXI9rwDYRxfNTjQoaq3UBCh
9pEhDbMK8ZapTQoUVN87p6JinbmUmtGo4f6+IgAXNOn8h/mqdefakVjgZHu7jUA0
u1U4Inz7cVMCgYB8aioFiXaPR4ozanQpjYDqJRHdV2yFXTewqBcbVYUEs2jTQjF5
KMjuwaPksYhGwpvMnaGeuWw0gSu0l9ZwXrdw1Zw9ygXGgb5wE5IpNsDVie27OVYJ
8VPaNq6fxoxmyKfpoZBTglKXkHCbLRxSBxbkiCqLQff7BWdUkqDbQR/W8QKBgFb8
YMhFe5D7cNwaBw8bGoQEK/gsqp9Q2Bc4FFrEIL+CiTLsPtGKpuiZ1ahm/LCIdHHa
d9k0x+r8Vf0IbcEqg3YTuktMmI9ajbgkBS5dIB6xqsVubEXLht8yzlbNyJDn4kEK
M8sB5VdNILLuMfuuK9382L2VNqHjLnmBioXdnJrDAoGAFjwOuFqjHh0dMw7Vg/AT
VuFdvpomQvGhAEKb1QI+agVntalNlx/AGBhdKQWGC4zGuqcOSmlHksm30PH9U+Dc
DCmWMKmh6uJVt7+UcbZAO4YnNQiE77ItdJrAX7b+WsmyhgiKSHAUwmBcHkYQiMdx
waUBFyYBUFMFpYGqkTM3VoY=
-----END PRIVATE KEY-----"""
    
    platform_cert_content = """-----BEGIN CERTIFICATE-----
MIIDQTCCAimgAwIBAgITBmyfz5m/jAo54vB4ikPmljZbyjANBgkqhkiG9w0BAQsF
ADA5MQswCQYDVQQGEwJVUzEPMA0GA1UEChMGQW1hem9uMRkwFwYDVQQDExBBbWF6
b24gUm9vdCBDQSAxMB4XDTE1MDUyNjAwMDAwMFoXDTM4MDExNzAwMDAwMFowOTEL
MAkGA1UEBhMCVVMxDzANBgNVBAoTBkFtYXpvbjEZMBcGA1UEAxMQQW1hem9uIFJv
b3QgQ0EgMTCCASIwDQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEBALJ4gHHKeNXj
ca9HgFB0fW7Y14h29Jlo91ghYPl0hAEvrAIthtOgQ3pOsqTQNroBvo3bSMgHFzZM
9O6II8c+6zf1tRn4SWiw3te5djgdYZ6k/oI2peVKVuRF4fn9tBb6dNqcmzU5L/qw
IFAGbHrQgLKm+a/sRxmPUDgH3KKHOVj4utWp+UhnMJbulHheb4mjUcAwhmahRWa6
VOujw5H5SNz/0egwLX0tdHA114gk957EWW67c4cX8jJGKLhD+rcdqsq08p8kDi1L
93FcXmn/6pUCyziKrlA4b9v7LWIbxcceVOF34GfID5yHI9Y/QCB/IIDEgEw+OyQm
jgSubJrIqg0CAwEAAaNCMEAwDwYDVR0TAQH/BAUwAwEB/zAOBgNVHQ8BAf8EBAMC
AYYwHQYDVR0OBBYEFIQYzIU07LwMlJQuCFmcx7IQTgoIMA0GCSqGSIb3DQEBCwUA
A4IBAQCY8jdaQZChGsV2USggNiMOruYou6r4lK5IpDB/G/wkjUu0yKGX9rbxenDI
U5PMCCjjmCXPI6T53iHTfIuJruydjsw2hUwsOBYy7n6Klf+z5TSWcRlbHBfLqurq
WhM/8XXnpEXMNz6GlFnhNTHuNHB+Pmk7Pj89waCV+fVr4WSyLGD1QdY+benRUOeq
3eUFWMSMSGbxAQD+nb3bQ9TrVKFrBk0CrKNNJ6kZS5MHAFkdwvv5hEwqesgHHouM
yMqt3VdNZS/SuTVYlQ+tmsA3yCJuawp12lT13n3QNVx1Q0B+tlEDMqpEr9BNNtAj
mCMFrXBWNa7psJkzVjJHE9+QLEk
-----END CERTIFICATE-----"""
    
    # Create temporary files
    with tempfile.NamedTemporaryFile(mode='w', suffix='.pem', delete=False) as f:
        f.write(private_key_content)
        private_key_path = f.name
    
    with tempfile.NamedTemporaryFile(mode='w', suffix='.pem', delete=False) as f:
        f.write(platform_cert_content)
        platform_cert_path = f.name
    
    # Update test config paths
    TestConfig.WXPAY_MCH_PRIVATE_KEY_PATH = private_key_path
    TestConfig.WXPAY_PLATFORM_CERT_PATH = platform_cert_path
    
    yield {
        'private_key_path': private_key_path,
        'platform_cert_path': platform_cert_path
    }
    
    # Cleanup
    try:
        os.unlink(private_key_path)
        os.unlink(platform_cert_path)
    except OSError:
        pass
