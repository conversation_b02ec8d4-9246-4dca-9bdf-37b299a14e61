"""
Test cases for WeChat Pay callback handling
"""
import json
import base64
import pytest
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime

from app.models.order import Order, OrderStatus
from app.models.payment_event import PaymentEvent
from app.models.subscription import Subscription, SubscriptionStatus
from app.services.payment_callback_service import PaymentCallbackService, PaymentCallbackError
from app.utils.time import utc_now


class TestPaymentCallbackService:
    """Test cases for PaymentCallbackService"""
    
    def test_decrypt_callback_resource_success(self, app):
        """Test successful callback resource decryption"""
        with app.app_context():
            # Mock encrypted data
            test_data = {
                'transaction_id': 'test_transaction_123',
                'out_trade_no': 'TEST_ORDER_123',
                'trade_state': 'SUCCESS',
                'amount': {'total': 1999}
            }
            
            # Mock the decryption process
            with patch('app.services.payment_callback_service.AESGCM') as mock_aesgcm:
                mock_cipher = Mock()
                mock_aesgcm.return_value = mock_cipher
                mock_cipher.decrypt.return_value = json.dumps(test_data).encode('utf-8')
                
                result = PaymentCallbackService._decrypt_callback_resource(
                    associated_data='test_associated_data',
                    nonce=base64.b64encode(b'test_nonce').decode(),
                    ciphertext=base64.b64encode(b'test_ciphertext').decode()
                )
                
                assert result == test_data
    
    def test_decrypt_callback_resource_invalid_data(self, app):
        """Test callback resource decryption with invalid data"""
        with app.app_context():
            with pytest.raises(Exception):
                PaymentCallbackService._decrypt_callback_resource(
                    associated_data='test_associated_data',
                    nonce='invalid_base64',
                    ciphertext='invalid_base64'
                )
    
    @patch('app.services.payment_callback_service.subscription_order_service')
    @patch('app.services.payment_callback_service.is_duplicate_payment_event')
    @patch('app.services.payment_callback_service.PaymentCallbackService._decrypt_callback_resource')
    @patch('app.services.payment_callback_service.PaymentEvent.create_event')
    @patch('app.services.payment_callback_service.mark_payment_event_processed')
    def test_process_callback_success(self, mock_mark_processed, mock_create_event,
                                    mock_decrypt, mock_duplicate, mock_service,
                                    app, db_session, sample_order):
        """Test successful callback processing"""
        with app.app_context():
            # Setup mocks
            mock_service.verify_callback_signature.return_value = True
            mock_duplicate.return_value = False
            mock_decrypt.return_value = {
                'transaction_id': 'test_transaction_123',
                'out_trade_no': sample_order.out_trade_no,
                'trade_state': 'SUCCESS',
                'success_time': '2023-10-15T10:35:00+08:00',
                'amount': {'total': sample_order.total_cent}
            }

            # Mock PaymentEvent creation
            mock_payment_event = Mock()
            mock_payment_event.id = 1
            mock_create_event.return_value = mock_payment_event
            
            # Mock callback data
            headers = {
                'Wechatpay-Timestamp': '1697123456',
                'Wechatpay-Nonce': 'test_nonce',
                'Wechatpay-Signature': 'test_signature',
                'Wechatpay-Serial': 'test_serial'
            }
            
            body = json.dumps({
                'id': 'test_event_123',
                'create_time': '2023-10-15T10:35:00+08:00',
                'event_type': 'TRANSACTION.SUCCESS',
                'resource_type': 'encrypt-resource',
                'resource': {
                    'algorithm': 'AEAD_AES_256_GCM',
                    'ciphertext': 'test_ciphertext',
                    'associated_data': 'test_associated_data',
                    'nonce': 'test_nonce'
                }
            })
            
            with patch('app.services.payment_callback_service.subscription_service') as mock_sub_service:
                mock_subscription = Mock()
                mock_subscription.id = 1
                mock_sub_service.activate_subscription.return_value = mock_subscription
                
                result = PaymentCallbackService.process_callback(headers, body)
                
                assert result['status'] == 'success'
                assert result['order_id'] == sample_order.id
                assert result['transaction_id'] == 'test_transaction_123'
                assert result['subscription_id'] == 1
    
    @patch('app.services.payment_callback_service.subscription_order_service')
    def test_process_callback_invalid_signature(self, mock_service, app):
        """Test callback processing with invalid signature"""
        with app.app_context():
            mock_service.verify_callback_signature.return_value = False
            
            headers = {
                'Wechatpay-Timestamp': '1697123456',
                'Wechatpay-Nonce': 'test_nonce',
                'Wechatpay-Signature': 'invalid_signature',
                'Wechatpay-Serial': 'test_serial'
            }
            
            body = json.dumps({
                'id': 'test_event_123',
                'event_type': 'TRANSACTION.SUCCESS'
            })
            
            with pytest.raises(PaymentCallbackError, match="Invalid signature"):
                PaymentCallbackService.process_callback(headers, body)
    
    @patch('app.services.payment_callback_service.subscription_order_service')
    @patch('app.services.payment_callback_service.is_duplicate_payment_event')
    def test_process_callback_duplicate_event(self, mock_duplicate, mock_service, app):
        """Test callback processing with duplicate event"""
        with app.app_context():
            mock_service.verify_callback_signature.return_value = True
            mock_duplicate.return_value = True
            
            headers = {
                'Wechatpay-Timestamp': '1697123456',
                'Wechatpay-Nonce': 'test_nonce',
                'Wechatpay-Signature': 'test_signature',
                'Wechatpay-Serial': 'test_serial'
            }
            
            body = json.dumps({
                'id': 'test_event_123',
                'event_type': 'TRANSACTION.SUCCESS'
            })
            
            result = PaymentCallbackService.process_callback(headers, body)
            
            assert result['status'] == 'duplicate'
            assert result['event_id'] == 'test_event_123'
    
    def test_handle_payment_success_new_order(self, app, db_session, sample_order):
        """Test handling successful payment for new order"""
        with app.app_context():
            payment_data = {
                'transaction_id': 'test_transaction_123',
                'success_time': '2023-10-15T10:35:00+08:00',
                'trade_state': 'SUCCESS'
            }

            # Create payment event with explicit ID
            payment_event = PaymentEvent(
                id=1,
                order_id=sample_order.id,
                event_id='test_event_123',
                event_type='TRANSACTION.SUCCESS'
            )
            db_session.add(payment_event)
            db_session.commit()
            
            with patch('app.services.payment_callback_service.subscription_service') as mock_sub_service:
                with patch.object(sample_order, 'mark_paid') as mock_mark_paid:
                    mock_subscription = Mock()
                    mock_subscription.id = 1
                    mock_sub_service.activate_subscription.return_value = mock_subscription

                    result = PaymentCallbackService._handle_payment_success(
                        sample_order, payment_data, payment_event
                    )

                    assert result['status'] == 'success'
                    assert result['order_id'] == sample_order.id
                    assert result['transaction_id'] == 'test_transaction_123'
                    assert result['subscription_id'] == 1

                    # Check that mark_paid was called with correct parameters
                    mock_mark_paid.assert_called_once()
                    call_args = mock_mark_paid.call_args
                    assert call_args[1]['transaction_id'] == 'test_transaction_123'
                
                # Check payment event marked as processed
                db_session.refresh(payment_event)
                assert payment_event.processed is True
    
    def test_handle_payment_success_already_paid(self, app, db_session, sample_order):
        """Test handling successful payment for already paid order"""
        with app.app_context():
            # Mark order as already paid
            sample_order.mark_paid('existing_transaction_123')

            payment_data = {
                'transaction_id': 'test_transaction_123',
                'success_time': '2023-10-15T10:35:00+08:00',
                'trade_state': 'SUCCESS'
            }

            # Create payment event with explicit ID
            payment_event = PaymentEvent(
                id=1,
                order_id=sample_order.id,
                event_id='test_event_123',
                event_type='TRANSACTION.SUCCESS'
            )
            db_session.add(payment_event)
            db_session.commit()
            
            result = PaymentCallbackService._handle_payment_success(
                sample_order, payment_data, payment_event
            )
            
            assert result['status'] == 'already_paid'
            assert result['order_id'] == sample_order.id
            
            # Check payment event marked as processed
            db_session.refresh(payment_event)
            assert payment_event.processed is True
    
    def test_handle_payment_closed(self, app, db_session, sample_order):
        """Test handling closed payment"""
        with app.app_context():
            payment_data = {
                'transaction_id': 'test_transaction_123',
                'trade_state': 'CLOSED'
            }

            # Create payment event with explicit ID
            payment_event = PaymentEvent(
                id=1,
                order_id=sample_order.id,
                event_id='test_event_123',
                event_type='TRANSACTION.CLOSED'
            )
            db_session.add(payment_event)
            db_session.commit()
            
            with patch.object(sample_order, 'mark_closed') as mock_mark_closed:
                result = PaymentCallbackService._handle_payment_closed(
                    sample_order, payment_data, payment_event
                )

                assert result['status'] == 'closed'
                assert result['order_id'] == sample_order.id

                # Check that mark_closed was called
                mock_mark_closed.assert_called_once()
            
            # Check payment event marked as processed
            db_session.refresh(payment_event)
            assert payment_event.processed is True


class TestWebhookRoutes:
    """Test cases for webhook routes"""
    
    @patch('app.routes.webhook.payment_callback_service')
    def test_wechatpay_callback_success(self, mock_service, client, app):
        """Test successful WeChat Pay callback"""
        with app.app_context():
            mock_service.process_callback.return_value = {
                'status': 'success',
                'order_id': 1,
                'transaction_id': 'test_transaction_123'
            }
            
            headers = {
                'Content-Type': 'application/json',
                'Wechatpay-Timestamp': '1697123456',
                'Wechatpay-Nonce': 'test_nonce',
                'Wechatpay-Signature': 'test_signature',
                'Wechatpay-Serial': 'test_serial'
            }
            
            data = {
                'id': 'test_event_123',
                'event_type': 'TRANSACTION.SUCCESS',
                'resource': {
                    'ciphertext': 'test_ciphertext',
                    'nonce': 'test_nonce'
                }
            }
            
            response = client.post('/webhook/wechatpay', 
                                 json=data, 
                                 headers=headers)
            
            assert response.status_code == 200
            assert response.data == b''  # Empty response body for success
    
    @patch('app.routes.webhook.payment_callback_service')
    def test_wechatpay_callback_signature_failure(self, mock_service, client, app):
        """Test WeChat Pay callback with signature verification failure"""
        with app.app_context():
            mock_service.process_callback.side_effect = PaymentCallbackError("Invalid signature")
            
            headers = {
                'Content-Type': 'application/json',
                'Wechatpay-Timestamp': '1697123456',
                'Wechatpay-Nonce': 'test_nonce',
                'Wechatpay-Signature': 'invalid_signature',
                'Wechatpay-Serial': 'test_serial'
            }
            
            data = {
                'id': 'test_event_123',
                'event_type': 'TRANSACTION.SUCCESS'
            }
            
            response = client.post('/webhook/wechatpay', 
                                 json=data, 
                                 headers=headers)
            
            assert response.status_code == 401
            response_data = response.get_json()
            assert response_data['code'] == 'FAIL'
            assert 'Signature verification failed' in response_data['message']
    
    def test_wechatpay_callback_missing_headers(self, client, app):
        """Test WeChat Pay callback with missing required headers"""
        with app.app_context():
            headers = {
                'Content-Type': 'application/json',
                # Missing required WeChat Pay headers
            }
            
            data = {
                'id': 'test_event_123',
                'event_type': 'TRANSACTION.SUCCESS'
            }
            
            response = client.post('/webhook/wechatpay', 
                                 json=data, 
                                 headers=headers)
            
            assert response.status_code == 400
            response_data = response.get_json()
            assert response_data['code'] == 'FAIL'
            assert 'Missing headers' in response_data['message']
    
    def test_wechatpay_callback_invalid_content_type(self, client, app):
        """Test WeChat Pay callback with invalid content type"""
        with app.app_context():
            headers = {
                'Content-Type': 'text/plain',  # Invalid content type
                'Wechatpay-Timestamp': '1697123456',
                'Wechatpay-Nonce': 'test_nonce',
                'Wechatpay-Signature': 'test_signature',
                'Wechatpay-Serial': 'test_serial'
            }
            
            response = client.post('/webhook/wechatpay', 
                                 data='invalid data',
                                 headers=headers)
            
            assert response.status_code == 400
            response_data = response.get_json()
            assert response_data['code'] == 'FAIL'
            assert 'Invalid content type' in response_data['message']
