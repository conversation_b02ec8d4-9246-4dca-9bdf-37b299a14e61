#!/usr/bin/env python3
"""
FootPrint 订阅服务启动文件
支持开发环境和生产环境启动
"""
import os
import sys
import argparse
from dotenv import load_dotenv

# 添加项目根目录到 Python 路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

# 加载环境变量
load_dotenv()

# 只在开发环境加载 .env.local
flask_env = os.environ.get('FLASK_ENV', 'development')
if flask_env == 'development':
    load_dotenv('.env.local')

from app import create_app
from app.config import config

def clear_proxy_vars():
    """清除代理环境变量，避免 SOCKS 问题"""
    proxy_vars = ['http_proxy', 'https_proxy', 'all_proxy', 'HTTP_PROXY', 'HTTPS_PROXY', 'ALL_PROXY']
    for var in proxy_vars:
        if var in os.environ:
            del os.environ[var]

def create_application():
    """创建 Flask 应用实例"""
    clear_proxy_vars()
    
    # 获取配置环境
    config_name = os.environ.get('FLASK_ENV', 'development')
    
    # 创建应用
    app = create_app(config[config_name])
    
    return app

def run_development_server(app, host='127.0.0.1', port=5001, debug=None):
    """运行开发服务器"""
    print(f"🚀 启动开发服务器...")
    print(f"📍 地址: http://{host}:{port}")
    print(f"🔧 环境: {os.environ.get('FLASK_ENV', 'development')}")
    print(f"🐛 调试模式: {debug if debug is not None else app.debug}")
    print("=" * 50)
    
    app.run(
        host=host,
        port=port,
        debug=debug if debug is not None else app.debug,
        threaded=True
    )

def run_production_server(app, host='0.0.0.0', port=5000, workers=4):
    """运行生产服务器 (Gunicorn)"""
    try:
        import gunicorn.app.wsgiapp as wsgi
        
        print(f"🚀 启动生产服务器 (Gunicorn)...")
        print(f"📍 地址: http://{host}:{port}")
        print(f"👥 工作进程: {workers}")
        print(f"🔧 环境: {os.environ.get('FLASK_ENV', 'production')}")
        print("=" * 50)
        
        # Gunicorn 配置
        sys.argv = [
            'gunicorn',
            '--bind', f'{host}:{port}',
            '--workers', str(workers),
            '--worker-class', 'gevent',
            '--worker-connections', '1000',
            '--timeout', '120',
            '--keepalive', '5',
            '--max-requests', '1000',
            '--max-requests-jitter', '100',
            '--preload',
            'wsgi:app'
        ]
        
        # 启动 Gunicorn
        wsgi.run()
        
    except ImportError:
        print("❌ Gunicorn 未安装，请运行: pip install gunicorn")
        print("🔄 回退到开发服务器...")
        run_development_server(app, host, port, debug=False)

def check_environment():
    """检查运行环境"""
    print("🔍 环境检查...")
    
    # 检查 Python 版本
    version = sys.version_info
    if version.major == 3 and version.minor >= 8:
        print(f"✅ Python {version.major}.{version.minor}.{version.micro}")
    else:
        print(f"⚠️  Python {version.major}.{version.minor}.{version.micro} (建议 3.8+)")
    
    # 检查关键环境变量
    required_vars = ['SECRET_KEY', 'DATABASE_URL']
    for var in required_vars:
        if os.environ.get(var):
            print(f"✅ {var}")
        else:
            print(f"❌ {var} 未设置")
    
    # 检查可选环境变量
    optional_vars = ['REDIS_URL', 'WECHAT_APPID', 'WXPAY_APPID']
    for var in optional_vars:
        if os.environ.get(var):
            print(f"✅ {var}")
        else:
            print(f"⚠️  {var} 未设置 (可选)")
    
    print("=" * 50)

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='FootPrint 订阅服务启动器')
    parser.add_argument('--host', default=None, help='绑定主机地址')
    parser.add_argument('--port', type=int, default=None, help='端口号')
    parser.add_argument('--workers', type=int, default=4, help='Gunicorn 工作进程数')
    parser.add_argument('--debug', action='store_true', help='启用调试模式')
    parser.add_argument('--no-debug', action='store_true', help='禁用调试模式')
    parser.add_argument('--env-check', action='store_true', help='只检查环境，不启动服务')
    parser.add_argument('--production', action='store_true', help='强制使用生产模式')
    parser.add_argument('--development', action='store_true', help='强制使用开发模式')
    
    args = parser.parse_args()
    
    # 环境检查
    if args.env_check:
        check_environment()
        return
    
    # 创建应用
    app = create_application()
    
    # 确定运行模式
    flask_env = os.environ.get('FLASK_ENV', 'development')
    
    if args.production:
        flask_env = 'production'
    elif args.development:
        flask_env = 'development'
    
    # 确定主机和端口
    if flask_env == 'production':
        default_host = '0.0.0.0'
        default_port = 5000
    else:
        default_host = '127.0.0.1'
        default_port = 5001
    
    host = args.host or default_host
    port = args.port or int(os.environ.get('PORT', default_port))
    
    # 确定调试模式
    debug = None
    if args.debug:
        debug = True
    elif args.no_debug:
        debug = False
    
    # 环境检查
    check_environment()
    
    # 启动服务器
    if flask_env == 'production' and not args.development:
        run_production_server(app, host, port, args.workers)
    else:
        run_development_server(app, host, port, debug)

if __name__ == '__main__':
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 服务已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        sys.exit(1)
