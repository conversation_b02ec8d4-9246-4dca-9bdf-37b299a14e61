# Enhanced Logging System with Daily Rotation

## Overview

The enhanced logging system provides structured logging with daily file rotation, organized by log type and severity. This system uses `structlog` for structured logging and `TimedRotatingFileHandler` for automatic daily log rotation.

## Features

### 1. Daily Log Rotation
- Logs are automatically rotated at midnight
- Files are named with date suffixes (e.g., `footprint_app.log.2024-01-15`)
- Configurable retention periods for different log types

### 2. Multiple Log Files by Category
- **footprint_app.log**: Main application logs
- **footprint_api.log**: API request/response logs
- **footprint_errors.log**: Error and warning logs (longer retention)
- **footprint_security.log**: Security events (longest retention)

### 3. Structured JSON Logging
- All logs are structured with consistent metadata
- Automatic addition of timestamps, request context, and application metadata
- Sensitive data masking for security

### 4. Log Retention Policies
- Application logs: 30 days
- API logs: 30 days
- Error logs: 90 days
- Security logs: 365 days

## Configuration

### Basic Setup

```python
from app.utils.logging import configure_logging
from flask import Flask

app = Flask(__name__)
app.config['LOG_LEVEL'] = 'INFO'  # DEBUG, INFO, WARNING, ERROR, CRITICAL
configure_logging(app)
```

### Environment Variables

You can configure logging through environment variables:

```bash
export LOG_LEVEL=DEBUG
export LOG_RETENTION_DAYS=60
```

## Usage Examples

### 1. Basic Logging

```python
import structlog

logger = structlog.get_logger()
logger.info("Application started", version="1.0.0")
logger.warning("Configuration missing", config_key="REDIS_URL")
logger.error("Database connection failed", error="Connection timeout")
```

### 2. API Logging

```python
from app.utils.logging import log_api_request, log_api_response

# Log API request
log_api_request(
    endpoint="/api/v1/subscriptions",
    method="POST",
    user_id="user_123",
    request_size=1024
)

# Log API response
log_api_response(
    endpoint="/api/v1/subscriptions",
    method="POST",
    status_code=201,
    user_id="user_123",
    response_time_ms=150
)
```

### 3. Payment Event Logging

```python
from app.utils.logging import log_payment_event

log_payment_event(
    event_type="payment_completed",
    order_id="order_123",
    transaction_id="txn_456",
    amount=29.99,
    currency="USD"
)
```

### 4. Security Event Logging

```python
from app.utils.logging import log_security_event

log_security_event(
    event_type="failed_login",
    severity="medium",
    user_id="user_123",
    ip_address="*************"
)
```

### 5. Module-Specific Logging

```python
from app.utils.logging import get_logger_for_module

logger = get_logger_for_module('payment_processor')
logger.info("Processing payment", payment_id="pay_123")
```

## Log File Structure

### File Naming Convention
- Current log: `footprint_app.log`
- Rotated logs: `footprint_app.log.2024-01-15`

### JSON Log Format

```json
{
  "event": "API request",
  "timestamp": "2024-01-15T10:30:45.123456",
  "date": "2024-01-15",
  "time": "10:30:45",
  "timestamp_unix": 1705312245,
  "log_level": "INFO",
  "application": "footprint_subscription",
  "version": "1.0.0",
  "event_type": "api_request",
  "endpoint": "/api/v1/subscriptions",
  "method": "POST",
  "user_id": "user_123",
  "request_id": "req_456",
  "remote_addr": "*************"
}
```

## Maintenance Functions

### Get Log Files Information

```python
from app.utils.logging import get_log_files_info

log_info = get_log_files_info()
for filename, info in log_info.items():
    print(f"{filename}: {info['size_mb']} MB")
```

### Clean Up Old Logs

```python
from app.utils.logging import cleanup_old_logs

# Remove logs older than 30 days
cleanup_old_logs(days_to_keep=30)
```

## Best Practices

### 1. Use Appropriate Log Levels
- **DEBUG**: Detailed diagnostic information
- **INFO**: General application flow
- **WARNING**: Something unexpected but not critical
- **ERROR**: Serious problems that need attention
- **CRITICAL**: Very serious errors that may abort the program

### 2. Include Context
Always include relevant context in your logs:

```python
logger.info(
    "User subscription created",
    user_id=user.id,
    subscription_id=subscription.id,
    plan=subscription.plan,
    billing_cycle=subscription.billing_cycle
)
```

### 3. Mask Sensitive Data
The system automatically masks sensitive data, but be mindful of what you log:

```python
# Good - sensitive data will be masked
logger.info("Payment processed", payment_token="tok_123", amount=29.99)

# Avoid logging raw sensitive data
# logger.info("Payment processed", credit_card_number="****************")
```

### 4. Use Structured Data
Prefer structured data over string formatting:

```python
# Good
logger.info("Order processed", order_id=123, items_count=5, total=99.99)

# Less ideal
logger.info(f"Order {order_id} processed with {items_count} items for ${total}")
```

## Monitoring and Alerting

### Log Analysis
- Use tools like ELK Stack, Splunk, or similar for log analysis
- Set up alerts for ERROR and CRITICAL level logs
- Monitor security logs for suspicious patterns

### Performance Monitoring
- Track API response times through log analysis
- Monitor error rates and patterns
- Set up dashboards for key metrics

## Troubleshooting

### Common Issues

1. **Logs not rotating**: Check file permissions and disk space
2. **Missing logs**: Verify the logs directory exists and is writable
3. **Large log files**: Adjust retention policies or log levels

### Debug Mode
Enable debug logging for troubleshooting:

```python
app.config['LOG_LEVEL'] = 'DEBUG'
```

This will provide more detailed information about application behavior.
