"""
FootPrint Subscription Service
Flask application factory and configuration
"""
import os
from flask import Flask
from flask_cors import CORS

from app.extensions import db, migrate, jwt, limiter, scheduler, init_redis
from app.config import Config

from flask_talisman import Talisman  # HTTPS强制
from flask_cors import CORS  # CORS控制
from flask_limiter import Limiter  # 请求限制

from flask_limiter.util import get_remote_address

def create_app(config_class=Config):
    """Application factory pattern"""
    
    app = Flask(__name__)

    app.config.from_object(config_class)

    # Configure logging
    from app.utils.logging import configure_logging
    configure_logging(app)

    # Initialize extensions
    db.init_app(app)
    migrate.init_app(app, db)
    jwt.init_app(app)

    # Initialize Redis first (needed for rate limiting)
    init_redis(app)

    # 配置 Limiter
    app.config['RATELIMIT_DEFAULT'] = "100/hour"  # 设置默认限制
    app.config['RATELIMIT_HEADERS_ENABLED'] = True  # 启用速率限制响应头
    limiter.init_app(app)

    # 从环境变量中读取允许的域名列表
    allowed_origins = os.environ.get('ALLOWED_ORIGINS', '').split(',')

    # 确保开发环境可用的默认值
    if not allowed_origins or app.debug:
        allowed_origins = [
            'http://localhost:3000',        # 前端开发服务器
            'http://localhost:8080',        # 可能的另一个开发端口
            'http://127.0.0.1:3000',
            'http://127.0.0.1:8080',
            'capacitor://localhost',        # 如果使用Capacitor进行混合开发
            'ionic://localhost',            # 如果使用Ionic进行混合开发
        ]

    # 生产环境域名（从环境变量读取）
    if not app.debug:
        prod_domains = [
            f"https://{os.environ.get('FRONTEND_DOMAIN', 'your-app-domain.com')}",
            f"https://{os.environ.get('ADMIN_DOMAIN', 'admin.your-app-domain.com')}",
        ]
        allowed_origins.extend(prod_domains)

    # CORS配置
    CORS(app, resources={
        r"/api/*": {
            "origins": allowed_origins,
            "methods": ["GET", "POST", "PUT", "DELETE"],
            "allow_headers": [
                "Content-Type", 
                "Authorization",
                "X-Requested-With",
                "Accept",
                "Origin",
                "Access-Control-Request-Method",
                "Access-Control-Request-Headers"
            ],
            "expose_headers": [
                "Content-Range", 
                "X-Total-Count"
            ],
            "supports_credentials": True
        },
        # 微信支付回调等特殊接口可以单独配置
        r"/webhook/*": {
            "origins": ["https://api.mch.weixin.qq.com"],  # 微信支付回调
            "methods": ["POST"],
            "supports_credentials": False
        }
    })

    # Setup security middleware
    from app.utils.security import setup_security_middleware
    setup_security_middleware(app)

    # Initialize scheduler and tasks (only if not in testing mode)
    if not app.config.get('TESTING'):
        try:
            # Check if scheduler is already initialized
            if not hasattr(scheduler, '_scheduler') or scheduler._scheduler is None:
                scheduler.init_app(app)

            # Only start if not already running
            if not scheduler.running:
                scheduler.start()

            # Import and initialize tasks
            from app.tasks.subscription_tasks import init_scheduler_tasks
            init_scheduler_tasks()
        except Exception as e:
            app.logger.warning(f"Scheduler initialization failed: {e}")
            # Continue without scheduler in case of errors

    # Register blueprints
    from app.routes.auth import bp as auth_bp
    from app.routes.billing import bp as billing_bp
    from app.routes.subscription import bp as subscription_bp
    from app.routes.webhook import bp as webhook_bp
    from app.routes.health import bp as health_bp
    from app.routes.admin import bp as admin_bp

    app.register_blueprint(auth_bp, url_prefix='/auth')
    app.register_blueprint(billing_bp, url_prefix='/billing')
    app.register_blueprint(subscription_bp, url_prefix='/subscription')
    app.register_blueprint(webhook_bp, url_prefix='/webhook')
    app.register_blueprint(health_bp)
    app.register_blueprint(admin_bp, url_prefix='/admin')

    # Error handlers
    from app.utils.error_handlers import register_error_handlers
    register_error_handlers(app)

    return app


# Import models to ensure they are registered with SQLAlchemy
from app.models import user, plan, order, subscription, payment_event, admin
