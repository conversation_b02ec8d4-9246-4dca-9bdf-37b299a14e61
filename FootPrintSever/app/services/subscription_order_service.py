"""
Subscription Order Service
Handles subscription orders using native WeChat Pay API
"""
import os
import time
import json
import uuid
import base64
import hashlib
import structlog
import requests
from datetime import datetime, timedelta
from cryptography.hazmat.primitives import hashes, serialization
from cryptography.hazmat.primitives.asymmetric import padding
from flask import current_app
from sqlalchemy.exc import SQLAlchemyError
from app.extensions import db
from app.models.user import User
from app.models.subscription import Subscription, SubscriptionStatus
from app.models.plan import Plan
from app.models.order import Order, OrderStatus

logger = structlog.get_logger()


class SubscriptionOrderError(Exception):
    """Subscription order service error"""
    pass

class ActiveSubscriptionError(SubscriptionOrderError):
    """
    Raised when user already has an active subscription.
    Carries the subscription object as .subscription for callers to use.
    """
    def __init__(self, message="User already has an active subscription", subscription=None):
        super().__init__(message)
        self.subscription = subscription

class WeChatPayError(Exception):
    """WeChat Pay related errors"""
    pass


class SubscriptionOrderService:
    """Service for handling subscription orders using native WeChat Pay API"""
    
    def __init__(self):
        self.api_base_url = "https://api.mch.weixin.qq.com"
        
    def _load_private_key(self):
        """Load merchant private key from certificate file"""
        private_key_path = current_app.config.get('WXPAY_MCH_PRIVATE_KEY_PATH')
        if not private_key_path:
            # Try default path
            private_key_path = os.path.join(os.getcwd(), 'certs', 'apiclient_key.pem')
        
        if not os.path.exists(private_key_path):
            raise WeChatPayError(f"Private key file not found: {private_key_path}")
        
        with open(private_key_path, 'rb') as f:
            private_key_data = f.read()
        
        return serialization.load_pem_private_key(private_key_data, password=None)
    
    def _generate_signature(self, method, url_path, timestamp, nonce_str, body):
        """
        Generate WeChat Pay signature according to official documentation
        
        Args:
            method: HTTP method (POST, GET, etc.)
            url_path: URL path without domain
            timestamp: Request timestamp
            nonce_str: Random string
            body: Request body (JSON string)
            
        Returns:
            str: Base64 encoded signature
        """
        # Construct signature string according to WeChat Pay documentation
        # Format: HTTP_METHOD\nURL\nTIMESTAMP\nNONCE_STR\nBODY\n
        sign_str = f"{method}\n{url_path}\n{timestamp}\n{nonce_str}\n{body}\n"
        
        logger.debug("Signature string constructed", 
                    method=method,
                    url_path=url_path, 
                    timestamp=timestamp,
                    nonce_str=nonce_str,
                    body_length=len(body),
                    sign_str_length=len(sign_str))
        
        # Load private key and sign
        private_key = self._load_private_key()
        
        # Sign with SHA256-RSA
        signature = private_key.sign(
            sign_str.encode('utf-8'),
            padding.PKCS1v15(),
            hashes.SHA256()
        )
        
        # Base64 encode
        signature_b64 = base64.b64encode(signature).decode('utf-8')
        
        logger.debug("Signature generated successfully", signature_length=len(signature_b64))
        
        return signature_b64
    
    def _build_authorization_header(self, method, url_path, timestamp, nonce_str, body):
        """
        Build Authorization header for WeChat Pay API
        
        Args:
            method: HTTP method
            url_path: URL path
            timestamp: Request timestamp  
            nonce_str: Random string
            body: Request body
            
        Returns:
            str: Authorization header value
        """
        # Get configuration
        mch_id = current_app.config.get('WXPAY_MCH_ID')
        cert_serial = current_app.config.get('WXPAY_CERT_SERIAL')
        
        if not mch_id or not cert_serial:
            raise WeChatPayError("WeChat Pay merchant ID or certificate serial not configured")
        
        # Generate signature
        signature = self._generate_signature(method, url_path, timestamp, nonce_str, body)
        
        # Build authorization header
        auth_header = (
            f'WECHATPAY2-SHA256-RSA2048 '
            f'mchid="{mch_id}",'
            f'nonce_str="{nonce_str}",'
            f'signature="{signature}",'
            f'timestamp="{timestamp}",'
            f'serial_no="{cert_serial}"'
        )
        
        logger.debug("Authorization header built", 
                    mch_id=mch_id,
                    cert_serial=cert_serial,
                    timestamp=timestamp,
                    nonce_str=nonce_str)
        
        return auth_header
    
    def _make_wechat_request(self, method, url_path, data=None):
        """
        Make authenticated request to WeChat Pay API
        
        Args:
            method: HTTP method
            url_path: API endpoint path
            data: Request data (dict)
            
        Returns:
            dict: API response
            
        Raises:
            WeChatPayError: If request fails
        """
        # Generate request parameters
        timestamp = str(int(time.time()))
        nonce_str = str(uuid.uuid4()).replace('-', '').upper()
        
        # Prepare request body
        body = json.dumps(data, separators=(',', ':'), ensure_ascii=False) if data else ""
        
        # Build headers
        headers = {
            'Accept': 'application/json',
            'Content-Type': 'application/json',
            'Authorization': self._build_authorization_header(method, url_path, timestamp, nonce_str, body),
            'User-Agent': 'FootprintApp/1.0'
        }
        
        # Make request
        url = f"{self.api_base_url}{url_path}"
        
        logger.info("Making WeChat Pay API request", 
                   method=method,
                   url=url,
                   timestamp=timestamp,
                   nonce_str=nonce_str,
                   body_size=len(body))
        
        try:
            response = requests.request(
                method=method,
                url=url,
                headers=headers,
                data=body.encode('utf-8') if body else None,
                timeout=30
            )
            
            logger.info("WeChat Pay API response received", 
                       status_code=response.status_code,
                       response_size=len(response.text))
            
            # Check response status
            if response.status_code != 200:
                error_msg = f"WeChat Pay API returned {response.status_code}: {response.text}"
                logger.error("WeChat Pay API error", 
                           status_code=response.status_code,
                           response_text=response.text)
                raise WeChatPayError(error_msg)
            
            # Parse JSON response
            try:
                return response.json()
            except json.JSONDecodeError as e:
                logger.error("Failed to parse WeChat Pay response JSON", 
                           response_text=response.text,
                           error=str(e))
                raise WeChatPayError(f"Invalid JSON response: {response.text}")
                
        except requests.RequestException as e:
            logger.error("WeChat Pay API request failed", error=str(e))
            raise WeChatPayError(f"Request failed: {str(e)}")
    
    def create_subscription_order(self, user_id, plan_id):
        """
        Create subscription order for user using native WeChat Pay API
        
        Args:
            user_id: User ID
            plan_id: Plan ID
            
        Returns:
            dict: Order creation response with prepay_id
            
        Raises:
            SubscriptionOrderError: If order creation fails
        """
        try:
            # Get user and plan
            user = User.query.get(user_id)
            if not user:
                raise SubscriptionOrderError(f"User not found: {user_id}")
            
            plan = Plan.query.get(plan_id)
            if not plan:
                raise SubscriptionOrderError(f"Plan not found: {plan_id}")
            
            # Check if user already has an active subscription
            existing_subscription = Subscription.find_by_user_id(user_id)
            if existing_subscription and existing_subscription.is_active():
                # raise a specific exception carrying the subscription object
                raise ActiveSubscriptionError("User already has an active subscription", subscription=existing_subscription)

            
            # Create order record
            order = Order.create_order(
                user_id=user_id,
                plan_id=plan_id,
                total_cent=plan.price_cent
            )
            
            # Get WeChat Pay configuration
            appid = current_app.config.get('WXPAY_APPID')
            mch_id = current_app.config.get('WXPAY_MCH_ID')
            notify_url = current_app.config.get('WEBHOOK_BASE_URL', 'https://localhost:5000') + '/webhook/wechatpay'
            
            if not appid or not mch_id:
                raise SubscriptionOrderError("WeChat Pay configuration incomplete")
            
            # Prepare payment data according to WeChat Pay APP API
            payment_data = {
                "appid": appid,
                "mchid": mch_id,
                "description": f"足迹Plus会员-{plan.name}",
                "out_trade_no": order.out_trade_no,
                "notify_url": notify_url,
                "amount": {
                    "total": plan.price_cent,
                    "currency": "CNY"
                }
            }
            
            # Add expiration time (2 hours from now)
            expire_time = datetime.now() + timedelta(hours=2)
            payment_data["time_expire"] = expire_time.strftime("%Y-%m-%dT%H:%M:%S+08:00")
            
            logger.info("Creating WeChat Pay APP transaction", 
                       user_id=user_id,
                       plan_id=plan_id,
                       order_id=order.id,
                       out_trade_no=order.out_trade_no,
                       amount=plan.price_cent,
                       notify_url=notify_url)
            
            # Make API request to WeChat Pay
            response = self._make_wechat_request('POST', '/v3/pay/transactions/app', payment_data)
            
            logger.info("Subscription order created successfully", 
                       user_id=user_id,
                       plan_id=plan_id,
                       order_id=order.id,
                       out_trade_no=order.out_trade_no,
                       prepay_id=response.get("prepay_id"))
            
            # Generate client payment parameters
            prepay_id = response.get("prepay_id")
            client_params = self._generate_client_payment_params(prepay_id) if prepay_id else {}
            
            return {
                "order_id": order.id,
                "out_trade_no": order.out_trade_no,
                "prepay_id": prepay_id,
                "client_params": client_params,
                "plan": {
                    "id": plan.id,
                    "name": plan.name,
                    "price": plan.price_yuan,
                    "duration_days": plan.period_days
                }
            }
            
        except WeChatPayError as e:
            logger.error("WeChat Pay order creation failed", 
                        user_id=user_id,
                        plan_id=plan_id,
                        error=str(e))
            raise SubscriptionOrderError(f"Payment order creation failed: {str(e)}")
        
        except Exception as e:
            logger.error("Failed to create subscription order", 
                        user_id=user_id,
                        plan_id=plan_id,
                        error=str(e))
            # 如果已经是 SubscriptionOrderError（或它的子类），保留并重新抛出，
            # 这样调用方（路由）可以根据具体的子异常（如 ActiveSubscriptionError）做不同处理。
            if isinstance(e, SubscriptionOrderError):
                raise
            raise SubscriptionOrderError(f"Failed to create order: {str(e)}")
    
    def process_payment_success(self, out_trade_no, transaction_id):
        """
        Process successful payment and activate subscription
        
        Args:
            out_trade_no: Merchant order number
            transaction_id: WeChat transaction ID
            
        Returns:
            dict: Subscription activation result
            
        Raises:
            SubscriptionOrderError: If processing fails
        """
        try:
            # Find the order
            order = Order.query.filter_by(out_trade_no=out_trade_no).first()
            if not order:
                raise SubscriptionOrderError(f"Order not found: {out_trade_no}")
            
            # Mark order as paid
            order.mark_paid(
                transaction_id=transaction_id,
                paid_at=datetime.utcnow()
            )
            
            # Get plan
            plan = Plan.query.get(order.plan_id)
            if not plan:
                raise SubscriptionOrderError(f"Plan not found: {order.plan_id}")
            
            # Create or renew subscription
            subscription = Subscription.create_or_renew(
                user_id=order.user_id,
                plan=plan,
                order_id=order.id
            )
            
            logger.info("Subscription activated successfully", 
                       order_id=order.id,
                       subscription_id=subscription.id,
                       user_id=order.user_id,
                       transaction_id=transaction_id)
            
            return {
                "subscription_id": subscription.id,
                "order_id": order.id,
                "status": "ACTIVE",
                "message": "订阅激活成功"
            }
            
        except Exception as e:
            logger.error("Failed to process payment success", 
                        out_trade_no=out_trade_no,
                        transaction_id=transaction_id,
                        error=str(e))
            raise SubscriptionOrderError(f"Failed to process payment: {str(e)}")
    
    def query_order_by_out_trade_no(self, out_trade_no):
        """
        Query order by merchant order number using native WeChat Pay API
        
        Args:
            out_trade_no: Merchant order number
            
        Returns:
            dict: Order information
            
        Raises:
            WeChatPayError: If query fails
        """
        try:
            mch_id = current_app.config.get('WXPAY_MCH_ID')
            url_path = f"/v3/pay/transactions/out-trade-no/{out_trade_no}"
            
            # Add mchid as query parameter
            url_path += f"?mchid={mch_id}"
            
            response = self._make_wechat_request('GET', url_path)
            
            logger.info("WeChat Pay order query successful", out_trade_no=out_trade_no)
            return response
            
        except Exception as e:
            logger.error("Failed to query WeChat Pay order", 
                        out_trade_no=out_trade_no, 
                        error=str(e))
            raise WeChatPayError(f"Failed to query order: {str(e)}")
    
    def _generate_client_payment_params(self, prepay_id):
        """
        Generate client payment parameters for APP payment
        
        Args:
            prepay_id: Prepay ID from WeChat Pay API
            
        Returns:
            dict: Client payment parameters for APP
        """
        try:
            # Get configuration
            appid = current_app.config.get('WXPAY_APPID')
            mch_id = current_app.config.get('WXPAY_MCH_ID')
            
            if not appid or not mch_id:
                raise WeChatPayError("WeChat Pay configuration incomplete")
            
            # Generate timestamp and nonce
            timestamp = str(int(time.time()))
            nonce_str = str(uuid.uuid4()).replace('-', '').upper()
            
            # Construct sign string for APP payment
            # Format: appid=xxx&noncestr=xxx&package=xxx&partnerid=xxx&prepayid=xxx&timestamp=xxx&key=xxx
            package = f"Sign=WXPay"
            
            # Build sign string according to WeChat Pay APP documentation
            sign_params = {
                'appid': appid,
                'noncestr': nonce_str,
                'package': package,
                'partnerid': mch_id,
                'prepayid': prepay_id,
                'timestamp': timestamp
            }
            
            # Sort parameters by key
            sorted_params = sorted(sign_params.items())
            sign_str = '&'.join([f"{k}={v}" for k, v in sorted_params])
            
            # Add API key for MD5 signature (for APP payment)
            api_key = current_app.config.get('WXPAY_API_KEY')
            if api_key:
                sign_str += f"&key={api_key}"
                
                # Calculate MD5 signature
                import hashlib
                sign = hashlib.md5(sign_str.encode('utf-8')).hexdigest().upper()
            else:
                # If no API key, use empty sign (will be handled by client)
                sign = ""
            
            # Return client parameters
            client_params = {
                'appid': appid,
                'partnerid': mch_id,
                'prepayid': prepay_id,
                'package': package,
                'noncestr': nonce_str,
                'timestamp': timestamp,
                'sign': sign
            }
            
            logger.info("Client payment parameters generated successfully", 
                       prepay_id=prepay_id,
                       timestamp=timestamp,
                       nonce_str=nonce_str)
            
            return client_params
            
        except Exception as e:
            logger.error("Failed to generate client payment parameters", 
                        prepay_id=prepay_id,
                        error=str(e))
            # Return empty params on error to avoid breaking the flow
            return {}
    
    def verify_callback_signature(self, headers, body):
        """
        Verify WeChat Pay callback signature using merchant certificate
        
        Args:
            headers: Request headers
            body: Request body (bytes or string)
            
        Returns:
            bool: True if signature is valid
        """
        try:
            # Get signature components from headers
            wechatpay_signature = headers.get('Wechatpay-Signature')
            wechatpay_timestamp = headers.get('Wechatpay-Timestamp')
            wechatpay_nonce = headers.get('Wechatpay-Nonce')
            wechatpay_serial = headers.get('Wechatpay-Serial')
            
            if not all([wechatpay_signature, wechatpay_timestamp, wechatpay_nonce, wechatpay_serial]):
                logger.error("Missing required signature headers")
                return False
            
            # Load WeChat Pay platform certificate for verification
            platform_cert_path = current_app.config.get('WXPAY_PLATFORM_CERT_PATH')
            if not platform_cert_path:
                platform_cert_path = os.path.join(os.getcwd(), 'certs', 'wechatpay_cert.pem')
            
            if not os.path.exists(platform_cert_path):
                logger.error("WeChat Pay platform certificate not found", path=platform_cert_path)
                return False
            
            with open(platform_cert_path, 'rb') as f:
                cert_data = f.read()
            
            # Load certificate and extract public key
            try:
                from cryptography.x509 import load_pem_x509_certificate
                cert = load_pem_x509_certificate(cert_data)
                public_key = cert.public_key()
            except Exception as e:
                logger.error("Failed to load platform certificate", error=str(e))
                return False
            
            # Construct message for verification
            if isinstance(body, bytes):
                body = body.decode('utf-8')
            
            message = f"{wechatpay_timestamp}\n{wechatpay_nonce}\n{body}\n"
            
            # Decode signature
            signature = base64.b64decode(wechatpay_signature)
            
            # Verify signature
            try:
                public_key.verify(
                    signature,
                    message.encode('utf-8'),
                    padding.PKCS1v15(),
                    hashes.SHA256()
                )
                
                logger.info("WeChat Pay callback signature verification successful")
                return True
                
            except Exception as e:
                logger.error("Signature verification failed", error=str(e))
                return False
                
        except Exception as e:
            logger.error("Failed to verify WeChat Pay callback signature", error=str(e))
            return False


# Global service instance
subscription_order_service = SubscriptionOrderService()
