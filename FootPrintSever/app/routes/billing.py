"""
Billing routes for order and payment operations
"""
from flask import Blueprint, request, jsonify, g
from datetime import datetime
import structlog

from app.extensions import limiter
from app.utils.auth import jwt_required, get_current_user_id
from app.utils.idempotency import idempotent
from app.services.order_service import order_service, OrderServiceError
from app.services.subscription_order_service import subscription_order_service, SubscriptionOrderError
from app.models.plan import Plan
from app.models.subscription import Subscription
from app.services.subscription_order_service import subscription_order_service, SubscriptionOrderError, ActiveSubscriptionError

logger = structlog.get_logger()

bp = Blueprint('billing', __name__)


@bp.route('/orders', methods=['POST'])
@jwt_required
@limiter.limit("5 per minute")
@idempotent(timeout=300)  # 5 minutes idempotency for order creation
def create_order():
    """
    Create a new order
    
    Request body:
    {
        "plan_code": "pro_month"
    }
    
    Response:
    {
        "order": {
            "id": 1,
            "out_trade_no": "FP1697123456abcd1234",
            "status": "PENDING",
            "total_cent": 1999,
            "total_yuan": 19.99,
            "created_at": "2023-10-15T10:30:00Z"
        },
        "prepay": {
            "prepay_id": "wx123456789"
        }
    }
    """
    try:
        # Validate request
        if not request.is_json:
            return jsonify({
                'error': 'Bad Request',
                'message': 'Content-Type must be application/json'
            }), 400
        
        data = request.get_json()
        plan_code = data.get('plan_code')
        
        if not plan_code:
            return jsonify({
                'error': 'Bad Request',
                'message': 'plan_code is required'
            }), 400
        
        # Get current user
        user_id = get_current_user_id()
        
        # Create order
        order, payment_response = order_service.create_order(user_id, plan_code)
        
        # Prepare response
        response_data = {
            'order': order.to_dict(),
            'prepay': {
                'prepay_id': payment_response.get('prepay_id')
            }
        }
        
        logger.info("Order created successfully", 
                   order_id=order.id,
                   out_trade_no=order.out_trade_no,
                   user_id=user_id,
                   plan_code=plan_code)
        
        return jsonify(response_data), 201
        
    except OrderServiceError as e:
        logger.warning("Order creation failed", 
                      user_id=get_current_user_id(),
                      plan_code=data.get('plan_code') if 'data' in locals() else None,
                      error=str(e))
        return jsonify({
            'error': 'Order Creation Failed',
            'message': str(e)
        }), 400
        
    except Exception as e:
        logger.error("Unexpected error during order creation", 
                    user_id=get_current_user_id(),
                    error=str(e))
        return jsonify({
            'error': 'Internal Server Error',
            'message': 'An unexpected error occurred'
        }), 500


@bp.route('/orders/<out_trade_no>', methods=['GET'])
@jwt_required
@limiter.limit("30 per minute")
def get_order(out_trade_no):
    """
    Get order details
    
    Response:
    {
        "order": {
            "id": 1,
            "out_trade_no": "FP1697123456abcd1234",
            "status": "PAID",
            "total_cent": 1999,
            "total_yuan": 19.99,
            "transaction_id": "wx123456789",
            "created_at": "2023-10-15T10:30:00Z",
            "paid_at": "2023-10-15T10:35:00Z"
        }
    }
    """
    try:
        user_id = get_current_user_id()
        
        # Get order
        order = order_service.get_order(out_trade_no, user_id)
        
        return jsonify({
            'order': order.to_dict()
        }), 200
        
    except OrderServiceError as e:
        logger.warning("Failed to get order", 
                      out_trade_no=out_trade_no,
                      user_id=get_current_user_id(),
                      error=str(e))
        return jsonify({
            'error': 'Order Not Found',
            'message': str(e)
        }), 404
        
    except Exception as e:
        logger.error("Unexpected error getting order", 
                    out_trade_no=out_trade_no,
                    user_id=get_current_user_id(),
                    error=str(e))
        return jsonify({
            'error': 'Internal Server Error',
            'message': 'An unexpected error occurred'
        }), 500


@bp.route('/orders', methods=['GET'])
@jwt_required
@limiter.limit("30 per minute")
def get_user_orders():
    """
    Get user's orders
    
    Query parameters:
    - limit: Maximum number of orders (default: 20, max: 100)
    - offset: Number of orders to skip (default: 0)
    
    Response:
    {
        "orders": [
            {
                "id": 1,
                "out_trade_no": "FP1697123456abcd1234",
                "status": "PAID",
                "total_cent": 1999,
                "total_yuan": 19.99,
                "created_at": "2023-10-15T10:30:00Z"
            }
        ],
        "pagination": {
            "limit": 20,
            "offset": 0,
            "total": 1
        }
    }
    """
    try:
        user_id = get_current_user_id()
        
        # Get pagination parameters
        limit = min(int(request.args.get('limit', 20)), 100)
        offset = max(int(request.args.get('offset', 0)), 0)
        
        # Get orders
        orders = order_service.get_user_orders(user_id, limit, offset)
        
        # Convert to dict
        orders_data = [order.to_dict() for order in orders]
        
        return jsonify({
            'orders': orders_data,
            'pagination': {
                'limit': limit,
                'offset': offset,
                'count': len(orders_data)
            }
        }), 200
        
    except Exception as e:
        logger.error("Failed to get user orders", 
                    user_id=get_current_user_id(),
                    error=str(e))
        return jsonify({
            'error': 'Internal Server Error',
            'message': 'An unexpected error occurred'
        }), 500


@bp.route('/orders/<out_trade_no>/status', methods=['GET'])
@jwt_required
@limiter.limit("10 per minute")
def query_payment_status(out_trade_no):
    """
    Query payment status from WeChat Pay and update subscription if paid
    
    Response:
    {
        "order": {
            "id": 1,
            "out_trade_no": "FP1697123456abcd1234",
            "status": "PAID",
            "transaction_id": "wx123456789"
        },
        "payment_info": {
            "trade_state": "SUCCESS",
            "trade_state_desc": "支付成功",
            "success_time": "2023-10-15T10:35:00+08:00"
        },
        "subscription_updated": true
    }
    """
    try:
        user_id = get_current_user_id()
        
        # Verify order ownership
        order_service.get_order(out_trade_no, user_id)
        
        # Query payment status and update subscription if needed
        status_info = order_service.query_payment_status_and_update_subscription(out_trade_no, user_id)
        
        return jsonify(status_info), 200
        
    except OrderServiceError as e:
        logger.warning("Failed to query payment status", 
                      out_trade_no=out_trade_no,
                      user_id=get_current_user_id(),
                      error=str(e))
        return jsonify({
            'error': 'Query Failed',
            'message': str(e)
        }), 400
        
    except Exception as e:
        logger.error("Unexpected error querying payment status", 
                    out_trade_no=out_trade_no,
                    user_id=get_current_user_id(),
                    error=str(e))
        return jsonify({
            'error': 'Internal Server Error',
            'message': 'An unexpected error occurred'
        }), 500


@bp.route('/plans', methods=['GET'])
@limiter.limit("60 per minute")
def get_plans():
    """
    Get available subscription plans
    
    Response:
    {
        "plans": [
            {
                "id": 1,
                "code": "pro_month",
                "name": "Pro Monthly",
                "description": "Monthly subscription to FootPrint Pro features",
                "period_days": 30,
                "price_cent": 1999,
                "price_yuan": 19.99,
                "active": true
            }
        ]
    }
    """
    try:
        plans = Plan.get_active_plans()
        plans_data = [plan.to_dict() for plan in plans]
        
        return jsonify({
            'plans': plans_data
        }), 200
        
    except Exception as e:
        logger.error("Failed to get plans", error=str(e))
        return jsonify({
            'error': 'Internal Server Error',
            'message': 'An unexpected error occurred'
        }), 500


@bp.route('/subscriptions', methods=['GET'])
@jwt_required
@limiter.limit("30 per minute")
def get_user_subscription():
    """
    Get current user's subscription information

    Response:
    {
        "subscription": {
            "id": 1,
            "plan_name": "Pro Monthly",
            "status": "ACTIVE",
            "start_time": 1697123456000,
            "end_time": 1699715456000,
            "is_active": true,
            "days_remaining": 30
        }
    }
    """
    try:
        user_id = get_current_user_id()

        # Get user's subscription
        subscription = Subscription.find_by_user_id(user_id)

        if not subscription:
            return jsonify({
                'subscription': None,
                'message': 'No active subscription found'
            }), 200

        # Get plan information
        plan = subscription.plan

        subscription_data = {
            'id': subscription.id,
            'plan_name': plan.name if plan else 'Unknown Plan',
            'plan_code': plan.code if plan else 'unknown',
            'status': subscription.status.value,
            'start_time': int(subscription.start_at.timestamp() * 1000) if subscription.start_at else 0,
            'end_time': int(subscription.end_at.timestamp() * 1000) if subscription.end_at else 0,
            'is_active': subscription.is_active(),
            'days_remaining': subscription.days_remaining(),
            'created_at': subscription.created_at.isoformat() if subscription.created_at else None
        }

        logger.info("User subscription retrieved",
                   user_id=user_id,
                   subscription_id=subscription.id,
                   is_active=subscription.is_active())

        return jsonify({
            'subscription': subscription_data
        }), 200

    except Exception as e:
        logger.error("Failed to get user subscription",
                    user_id=get_current_user_id(),
                    error=str(e))
        return jsonify({
            'error': 'Internal Server Error',
            'message': 'An unexpected error occurred'
        }), 500


@bp.route('/member/status', methods=['GET'])
@jwt_required
@limiter.limit("60 per minute")
def get_member_status():
    """
    Get user's membership status (for periodic checks)
    
    Response:
    {
        "user_id": 123,
        "is_vip": true,
        "vip_expire_time": 1699715456000,
        "last_device_id": "abc123",
        "subscription": {
            "id": 1,
            "plan_name": "Pro Monthly",
            "plan_code": "pro_month",
            "status": "ACTIVE",
            "start_time": 1697123456000,
            "end_time": 1699715456000,
            "is_active": true,
            "days_remaining": 30
        },
        "check_time": 1697123456000
    }
    """
    try:
        user_id = get_current_user_id()
        
        from app.models.user import User
        user = User.query.get(user_id)
        
        if not user:
            return jsonify({
                'error': 'User Not Found',
                'message': 'User does not exist'
            }), 404
        
        # Get user's subscription
        subscription = Subscription.find_by_user_id(user_id)
        
        subscription_data = None
        if subscription:
            plan = subscription.plan
            subscription_data = {
                'id': subscription.id,
                'plan_name': plan.name if plan else 'Unknown Plan',
                'plan_code': plan.code if plan else 'unknown',
                'status': subscription.status.value,
                'start_time': int(subscription.start_at.timestamp() * 1000) if subscription.start_at else 0,
                'end_time': int(subscription.end_at.timestamp() * 1000) if subscription.end_at else 0,
                'is_active': subscription.is_active(),
                'days_remaining': subscription.days_remaining()
            }
        
        # 获取版本配置
        from app.models.app_settings import AppSettings
        app_settings = AppSettings.get_settings()
        
        response_data = {
            'user_id': user.id,
            'is_vip': user.is_vip(),
            'vip_expire_time': user.get_vip_expire_time(),
            'last_device_id': user.device_id,  # 返回用户最后登录的设备ID
            'subscription': subscription_data,
            'check_time': int(datetime.utcnow().timestamp() * 1000),
            'version_config': {
                'latest_version_code': app_settings.latest_version_code,
                'min_version_code': app_settings.min_version_code,
                'download_url': app_settings.download_url
            }
        }
        
        logger.info("Member status checked",
                   user_id=user_id,
                   is_vip=user.is_vip(),
                   last_device_id=user.device_id)
        
        return jsonify(response_data), 200
        
    except Exception as e:
        logger.error("Failed to get member status",
                    user_id=get_current_user_id(),
                    error=str(e))
        return jsonify({
            'error': 'Internal Server Error',
            'message': 'An unexpected error occurred'
        }), 500


@bp.route('/subscription/order', methods=['POST'])
@jwt_required
@limiter.limit("3 per minute")
@idempotent(timeout=300)
def create_subscription_order():
    """
    Create subscription order for payment

    Request body:
    {
        "plan_id": 1
    }

    Response:
    {
        "order_id": 123,
        "out_trade_no": "FP1697123456abcd1234",
        "prepay_id": "wx123456789012345678901234567890",
        "plan": {
            "id": 1,
            "name": "Pro Monthly",
            "price": 19.99,
            "duration_days": 30
        }
    }
    """
    try:
        # Validate request
        if not request.is_json:
            return jsonify({
                'error': 'Bad Request',
                'message': 'Content-Type must be application/json'
            }), 400

        data = request.get_json()
        plan_id = data.get('plan_id')

        if not plan_id:
            return jsonify({
                'error': 'Bad Request',
                'message': 'plan_id is required'
            }), 400

        # Get current user
        user_id = get_current_user_id()

        # Create subscription order
        order_response = subscription_order_service.create_subscription_order(
            user_id=user_id,
            plan_id=plan_id
        )

        logger.info("Subscription order created successfully",
                   user_id=user_id,
                   plan_id=plan_id,
                   order_id=order_response.get('order_id'))

        return jsonify(order_response), 201

    except ActiveSubscriptionError as e:
        # Provide a clear response with the existing subscription info and 409 status
        sub = getattr(e, 'subscription', None)
        if not sub:
            # fallback: try to load from DB
            sub = Subscription.find_by_user_id(user_id)

        subscription_data = None
        if sub:
            plan = sub.plan
            subscription_data = {
                'id': sub.id,
                'plan_name': plan.name if plan else 'Unknown Plan',
                'plan_code': plan.code if plan else 'unknown',
                'status': sub.status.value,
                'start_time': int(sub.start_at.timestamp() * 1000) if sub.start_at else 0,
                'end_time': int(sub.end_at.timestamp() * 1000) if sub.end_at else 0,
                'is_active': sub.is_active(),
                'days_remaining': sub.days_remaining(),
                'created_at': sub.created_at.isoformat() if sub.created_at else None
            }

        logger.warning("User already has an active subscription, order creation blocked",
                       user_id=user_id,
                       subscription_id=(subscription_data['id'] if subscription_data else None))

        return jsonify({
            'error': 'User Already Subscribed',
            'message': str(e),
            'subscription': subscription_data
        }), 409
        
    except SubscriptionOrderError as e:
        logger.warning("Subscription order creation failed",
                      user_id=get_current_user_id(),
                      plan_id=data.get('plan_id') if 'data' in locals() else None,
                      error=str(e))
        return jsonify({
            'error': 'Order Creation Failed',
            'message': str(e)
        }), 400

    except Exception as e:
        logger.error("Unexpected error during order creation",
                    user_id=get_current_user_id(),
                    error=str(e))
        return jsonify({
            'error': 'Internal Server Error',
            'message': 'An unexpected error occurred'
        }), 500

