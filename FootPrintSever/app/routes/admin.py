"""
Admin routes for user management
"""
from flask import Blueprint, render_template, request, jsonify, current_app, redirect, url_for, session
from sqlalchemy import func, or_, and_, text
from sqlalchemy.orm import joinedload
from app.models.user import User
from app.models.subscription import Subscription, SubscriptionStatus
from app.models.plan import Plan
from app.models.order import Order, OrderStatus
from app.extensions import db
from app.utils.database_optimization import QueryOptimizer
from app.utils.admin_decorators import admin_required, super_admin_required
from app.services.admin_auth import AdminAuthService
import structlog

logger = structlog.get_logger()

bp = Blueprint('admin', __name__, url_prefix='/admin')


# 登录相关路由
@bp.route('/login', methods=['GET', 'POST'])
def login():
    """Admin login"""
    # 如果已经登录，重定向到首页
    if AdminAuthService.is_logged_in():
        next_url = request.args.get('next', url_for('admin.index'))
        return redirect(next_url)
    
    if request.method == 'POST':
        try:
            data = request.get_json()
            username = data.get('username', '').strip()
            password = data.get('password', '')
            remember_me = data.get('remember_me', False)
            
            if not username or not password:
                return jsonify({
                    'success': False,
                    'message': '请输入用户名和密码'
                }), 400
            
            # 验证用户
            admin = AdminAuthService.authenticate(username, password)
            if admin:
                # 创建会话
                AdminAuthService.create_session(admin)
                
                # 如果选择记住我，延长会话时间
                if remember_me:
                    session.permanent = True
                
                return jsonify({
                    'success': True,
                    'message': '登录成功',
                    'redirect': request.args.get('next', url_for('admin.index'))
                })
            else:
                return jsonify({
                    'success': False,
                    'message': '用户名或密码错误'
                }), 401
                
        except Exception as e:
            logger.error("Admin login error", error=str(e))
            return jsonify({
                'success': False,
                'message': '登录失败，请稍后重试'
            }), 500
    
    # GET 请求显示登录页面
    return render_template('admin/login.html')


@bp.route('/logout')
def logout():
    """Admin logout"""
    AdminAuthService.logout()
    return redirect(url_for('admin.login'))


@bp.route('/')
@admin_required
def index():
    """Admin dashboard"""
    current_admin = AdminAuthService.get_current_admin()
    return render_template('admin/index.html', current_admin=current_admin)


@bp.route('/users')
@admin_required
def users():
    """User management page"""
    return render_template('admin/users.html')


@bp.route('/api/users')
@admin_required
def api_users():
    """
    API endpoint for user data with pagination and search
    Optimized for large datasets
    """
    try:
        # 获取查询参数
        page = request.args.get('page', 1, type=int)
        per_page = min(request.args.get('per_page', 20, type=int), 100)  # 限制最大每页数量
        search = request.args.get('search', '').strip()
        sort_by = request.args.get('sort_by', 'created_at')
        sort_order = request.args.get('sort_order', 'desc')
        vip_filter = request.args.get('vip_filter', '')  # all, vip, non_vip
        
        # 使用优化的查询构建器
        query = QueryOptimizer.build_optimized_user_query(
            search=search,
            vip_filter=vip_filter,
            sort_by=sort_by,
            sort_order=sort_order
        )
        
        # 分页查询 - 使用 offset/limit 而不是 paginate 以提高性能
        total = query.count()
        offset = (page - 1) * per_page
        users = query.offset(offset).limit(per_page).all()
        
        # 构建响应数据
        users_data = []
        for user in users:
            user_dict = {
                'id': user.id,
                'openid': user.openid,
                'unionid': user.unionid,
                'nickname': user.nickname or '未设置',
                'avatar_url': user.avatar_url,
                'created_at': user.created_at.strftime('%Y-%m-%d %H:%M:%S'),
                'updated_at': user.updated_at.strftime('%Y-%m-%d %H:%M:%S'),
                'is_vip': False,
                'vip_status': '非VIP',
                'vip_expire_time': None,
                'subscription_info': None,
                'has_device': bool(user.device_id),
                'device_id': user.device_id,
                'app_version': user.app_version
            }
            
            # 添加VIP信息
            if user.subscription:
                subscription = user.subscription
                is_active = subscription.is_active()
                user_dict.update({
                    'is_vip': is_active,
                    'vip_status': '活跃VIP' if is_active else subscription.status.value,
                    'vip_expire_time': subscription.end_at.strftime('%Y-%m-%d %H:%M:%S') if subscription.end_at else None,
                    'subscription_info': {
                        'id': subscription.id,
                        'plan_name': subscription.plan.name if subscription.plan else '未知套餐',
                        'status': subscription.status.value,
                        'start_at': subscription.start_at.strftime('%Y-%m-%d %H:%M:%S'),
                        'end_at': subscription.end_at.strftime('%Y-%m-%d %H:%M:%S') if subscription.end_at else None,
                        'days_remaining': subscription.days_remaining()
                    }
                })
            
            users_data.append(user_dict)
        
        # 计算分页信息
        total_pages = (total + per_page - 1) // per_page
        has_prev = page > 1
        has_next = page < total_pages
        
        response_data = {
            'users': users_data,
            'pagination': {
                'page': page,
                'per_page': per_page,
                'total': total,
                'total_pages': total_pages,
                'has_prev': has_prev,
                'has_next': has_next,
                'prev_page': page - 1 if has_prev else None,
                'next_page': page + 1 if has_next else None
            },
            'filters': {
                'search': search,
                'sort_by': sort_by,
                'sort_order': sort_order,
                'vip_filter': vip_filter
            }
        }
        
        logger.info("User list retrieved", 
                   page=page, per_page=per_page, total=total, 
                   search=search, vip_filter=vip_filter)
        
        return jsonify(response_data)
        
    except Exception as e:
        logger.error("Failed to retrieve user list", error=str(e))
        return jsonify({'error': 'Failed to retrieve user data'}), 500


@bp.route('/api/users/<int:user_id>')
@admin_required
def api_user_detail(user_id):
    """Get detailed user information"""
    try:
        user = User.query.options(
            joinedload(User.subscription).joinedload(Subscription.plan)
        ).filter_by(id=user_id).first()

        if not user:
            return jsonify({'error': 'User not found'}), 404

        user_data = user.to_dict()

        # 添加详细的订阅信息
        if user.subscription:
            subscription = user.subscription
            user_data['subscription_info'] = {
                'id': subscription.id,
                'plan_name': subscription.plan.name if subscription.plan else '未知套餐',
                'plan_code': subscription.plan.code if subscription.plan else '',
                'plan_price_yuan': subscription.plan.price_cent / 100.0 if subscription.plan else 0,
                'status': subscription.status.value,
                'start_at': subscription.start_at.strftime('%Y-%m-%d %H:%M:%S'),
                'end_at': subscription.end_at.strftime('%Y-%m-%d %H:%M:%S') if subscription.end_at else None,
                'days_remaining': subscription.days_remaining(),
                'is_active': subscription.is_active(),
                'created_at': subscription.created_at.strftime('%Y-%m-%d %H:%M:%S'),
                'updated_at': subscription.updated_at.strftime('%Y-%m-%d %H:%M:%S')
            }

        # 添加订单信息（最近10个）
        orders_data = []
        recent_orders = user.orders.order_by(text('created_at DESC')).limit(10).all()
        for order in recent_orders:
            order_data = {
                'id': order.id,
                'out_trade_no': order.out_trade_no,
                'status': order.status.value,
                'total_cent': order.total_cent,
                'total_yuan': order.total_cent / 100.0,
                'created_at': order.created_at.strftime('%Y-%m-%d %H:%M:%S'),
                'paid_at': order.paid_at.strftime('%Y-%m-%d %H:%M:%S') if order.paid_at else None,
                'transaction_id': order.transaction_id
            }
            if hasattr(order, 'plan') and order.plan:
                order_data['plan_name'] = order.plan.name
            orders_data.append(order_data)

        user_data['recent_orders'] = orders_data
        user_data['total_orders'] = user.orders.count()
        user_data['paid_orders'] = user.orders.filter_by(status='PAID').count()
        
        # 添加设备历史信息
        from app.models.user_device import UserDevice
        devices = UserDevice.get_user_devices(user_id)
        devices_data = []
        for device in devices:
            device_dict = device.to_dict()
            # 格式化时间显示
            device_dict['first_login_at'] = device.first_login_at.strftime('%Y-%m-%d %H:%M:%S')
            device_dict['last_login_at'] = device.last_login_at.strftime('%Y-%m-%d %H:%M:%S')
            devices_data.append(device_dict)
        
        user_data['devices'] = devices_data
        user_data['device_count'] = len(devices_data)
        user_data['has_devices'] = len(devices_data) > 0
        user_data['current_device_id'] = user.device_id

        return jsonify(user_data)

    except Exception as e:
        logger.error("Failed to get user detail", user_id=user_id, error=str(e))
        return jsonify({'error': 'Failed to get user detail'}), 500


@bp.route('/api/users/<int:user_id>', methods=['DELETE'])
@super_admin_required
def api_delete_user(user_id):
    """Delete user and all related data"""
    try:
        user = User.query.filter_by(id=user_id).first()

        if not user:
            return jsonify({'error': 'User not found'}), 404

        # 记录删除操作
        logger.info("Deleting user", user_id=user_id, openid=user.openid, nickname=user.nickname)

        # 删除用户相关数据（正确处理外键约束）
        from app.models.order import Order
        from app.models.payment_event import PaymentEvent

        # 1. 首先删除订阅数据（清除对订单的引用）
        if user.subscription:
            # 清除 last_order_id 引用
            user.subscription.last_order_id = None
            db.session.flush()  # 立即执行更新
            # 然后删除订阅记录
            db.session.delete(user.subscription)
            db.session.flush()

        # 2. 删除支付事件（依赖订单）
        user_orders = user.orders.all()
        for order in user_orders:
            PaymentEvent.query.filter_by(order_id=order.id).delete()
        db.session.flush()

        # 3. 删除订单
        Order.query.filter_by(user_id=user_id).delete()
        db.session.flush()

        # 4. 最后删除用户
        db.session.delete(user)
        db.session.commit()

        logger.info("User deleted successfully", user_id=user_id)
        return jsonify({'message': 'User deleted successfully'})

    except Exception as e:
        db.session.rollback()
        logger.error("Failed to delete user", user_id=user_id, error=str(e))
        return jsonify({'error': 'Failed to delete user'}), 500


@bp.route('/orders')
@admin_required
def orders():
    """Order management page"""
    return render_template('admin/orders.html')


@bp.route('/subscriptions')
@admin_required
def subscriptions():
    """Subscription management page"""
    return render_template('admin/subscriptions.html')


@bp.route('/statistics')
@admin_required
def statistics():
    """Data statistics page"""
    return render_template('admin/statistics.html')


@bp.route('/settings', methods=['GET', 'POST'])
@super_admin_required
def settings():
    """System settings page"""
    from app.models.app_settings import AppSettings
    
    if request.method == 'POST':
        try:
            # 获取表单数据
            latest_version_code = request.form.get('latest_version_code', type=int)
            min_version_code = request.form.get('min_version_code', type=int)
            download_url = request.form.get('download_url', '').strip()
            
            # 验证数据
            if not latest_version_code or not min_version_code or not download_url:
                return render_template('admin/settings.html', 
                                     settings=AppSettings.get_settings(),
                                     message='所有字段都是必填的',
                                     message_type='error')
            
            if min_version_code > latest_version_code:
                return render_template('admin/settings.html',
                                     settings=AppSettings.get_settings(),
                                     message='最低可用版本号不能大于最新版本号',
                                     message_type='error')
            
            # 更新设置
            AppSettings.update_settings(
                latest_version_code=latest_version_code,
                min_version_code=min_version_code,
                download_url=download_url
            )
            
            logger.info("App settings updated", 
                       latest_version=latest_version_code,
                       min_version=min_version_code)
            
            return render_template('admin/settings.html',
                                 settings=AppSettings.get_settings(),
                                 message='设置保存成功',
                                 message_type='success')
            
        except Exception as e:
            logger.error("Failed to update settings", error=str(e))
            return render_template('admin/settings.html',
                                 settings=AppSettings.get_settings(),
                                 message='保存失败，请稍后重试',
                                 message_type='error')
    
    # GET 请求显示设置页面
    settings = AppSettings.get_settings()
    return render_template('admin/settings.html', settings=settings)


@bp.route('/config', methods=['GET', 'POST'])
@super_admin_required
def config():
    """System settings page"""
    from app.models.app_settings import AppSettings
    
    if request.method == 'POST':
        try:
            # 获取表单数据
            latest_version_code = request.form.get('latest_version_code', type=int)
            min_version_code = request.form.get('min_version_code', type=int)
            download_url = request.form.get('download_url', '').strip()
            
            # 验证数据
            if not latest_version_code or not min_version_code or not download_url:
                return render_template('admin/config.html', 
                                     settings=AppSettings.get_settings(),
                                     message='所有字段都是必填的',
                                     message_type='error')
            
            if min_version_code > latest_version_code:
                return render_template('admin/config.html',
                                     settings=AppSettings.get_settings(),
                                     message='最低可用版本号不能大于最新版本号',
                                     message_type='error')
            
            # 更新设置
            AppSettings.update_settings(
                latest_version_code=latest_version_code,
                min_version_code=min_version_code,
                download_url=download_url
            )
            
            logger.info("App settings updated", 
                       latest_version=latest_version_code,
                       min_version=min_version_code)
            
            return render_template('admin/config.html',
                                 settings=AppSettings.get_settings(),
                                 message='设置保存成功',
                                 message_type='success')
            
        except Exception as e:
            logger.error("Failed to update config", error=str(e))
            return render_template('admin/config.html',
                                 settings=AppSettings.get_settings(),
                                 message='保存失败，请稍后重试',
                                 message_type='error')
    
    # GET 请求显示设置页面
    settings = AppSettings.get_settings()
    return render_template('admin/config.html', settings=settings)


@bp.route('/api/orders')
@admin_required
def api_orders():
    """API endpoint for order data with pagination and search"""
    try:
        from app.models.order import Order, OrderStatus
        from app.models.plan import Plan
        from sqlalchemy.orm import joinedload
        
        # 获取查询参数
        page = request.args.get('page', 1, type=int)
        per_page = min(request.args.get('per_page', 20, type=int), 100)
        search = request.args.get('search', '').strip()
        status_filter = request.args.get('status_filter', '')
        sort_by = request.args.get('sort_by', 'created_at')
        sort_order = request.args.get('sort_order', 'desc')
        
        # 构建查询
        query = Order.query.options(
            joinedload(Order.user),
            joinedload(Order.plan)
        )
        
        # 搜索条件
        if search:
            query = query.filter(
                or_(
                    Order.out_trade_no.contains(search),
                    Order.transaction_id.contains(search),
                    Order.id == int(search) if search.isdigit() else False
                )
            )
        
        # 状态筛选
        if status_filter:
            query = query.filter(Order.status == OrderStatus(status_filter))
        
        # 排序
        if sort_order == 'desc':
            query = query.order_by(getattr(Order, sort_by).desc())
        else:
            query = query.order_by(getattr(Order, sort_by).asc())
        
        # 分页
        total = query.count()
        offset = (page - 1) * per_page
        orders = query.offset(offset).limit(per_page).all()
        
        # 构建响应数据
        orders_data = []
        for order in orders:
            order_dict = order.to_dict()
            order_dict['user_info'] = {
                'id': order.user.id,
                'nickname': order.user.nickname or '未设置',
                'openid': order.user.openid
            }
            order_dict['plan_info'] = {
                'name': order.plan.name,
                'code': order.plan.code
            } if order.plan else None
            orders_data.append(order_dict)
        
        # 分页信息
        total_pages = (total + per_page - 1) // per_page
        
        return jsonify({
            'orders': orders_data,
            'pagination': {
                'page': page,
                'per_page': per_page,
                'total': total,
                'total_pages': total_pages,
                'has_prev': page > 1,
                'has_next': page < total_pages
            }
        })
        
    except Exception as e:
        logger.error("Failed to retrieve orders", error=str(e))
        return jsonify({'error': 'Failed to retrieve orders'}), 500


@bp.route('/api/subscriptions')
@admin_required
def api_subscriptions():
    """API endpoint for subscription data"""
    try:
        from app.models.subscription import Subscription, SubscriptionStatus
        from sqlalchemy.orm import joinedload
        
        page = request.args.get('page', 1, type=int)
        per_page = min(request.args.get('per_page', 20, type=int), 100)
        search = request.args.get('search', '').strip()
        status_filter = request.args.get('status_filter', '')
        
        # 构建查询
        query = Subscription.query.options(
            joinedload(Subscription.user),
            joinedload(Subscription.plan)
        )
        
        # 搜索条件
        if search:
            query = query.join(User).filter(
                or_(
                    User.nickname.contains(search),
                    User.openid.contains(search),
                    Subscription.id == int(search) if search.isdigit() else False
                )
            )
        
        # 状态筛选
        if status_filter:
            if status_filter == 'active':
                query = query.filter(
                    Subscription.status == SubscriptionStatus.ACTIVE,
                    Subscription.end_at > func.now()
                )
            elif status_filter == 'expired':
                query = query.filter(
                    or_(
                        Subscription.status == SubscriptionStatus.EXPIRED,
                        and_(
                            Subscription.status == SubscriptionStatus.ACTIVE,
                            Subscription.end_at <= func.now()
                        )
                    )
                )
            else:
                query = query.filter(Subscription.status == SubscriptionStatus(status_filter))
        
        # 排序
        query = query.order_by(Subscription.created_at.desc())
        
        # 分页
        total = query.count()
        offset = (page - 1) * per_page
        subscriptions = query.offset(offset).limit(per_page).all()
        
        # 构建响应数据
        subscriptions_data = []
        for sub in subscriptions:
            sub_dict = sub.to_dict()
            sub_dict['user_info'] = {
                'id': sub.user.id,
                'nickname': sub.user.nickname or '未设置',
                'openid': sub.user.openid
            }
            sub_dict['plan_info'] = {
                'name': sub.plan.name,
                'code': sub.plan.code,
                'price_yuan': sub.plan.price_yuan
            } if sub.plan else None
            subscriptions_data.append(sub_dict)
        
        total_pages = (total + per_page - 1) // per_page
        
        return jsonify({
            'subscriptions': subscriptions_data,
            'pagination': {
                'page': page,
                'per_page': per_page,
                'total': total,
                'total_pages': total_pages,
                'has_prev': page > 1,
                'has_next': page < total_pages
            }
        })
        
    except Exception as e:
        logger.error("Failed to retrieve subscriptions", error=str(e))
        return jsonify({'error': 'Failed to retrieve subscriptions'}), 500


@bp.route('/api/statistics/overview')
@admin_required
def api_statistics_overview():
    """Get comprehensive statistics for dashboard"""
    try:
        from app.models.order import Order, OrderStatus
        from app.models.subscription import Subscription, SubscriptionStatus
        from app.models.plan import Plan
        from datetime import datetime, timedelta
        
        now = datetime.utcnow()
        today_start = now.replace(hour=0, minute=0, second=0, microsecond=0)
        week_start = today_start - timedelta(days=7)
        month_start = today_start - timedelta(days=30)
        
        # 用户统计
        total_users = User.query.count()
        today_new_users = User.query.filter(User.created_at >= today_start).count()
        week_new_users = User.query.filter(User.created_at >= week_start).count()
        month_new_users = User.query.filter(User.created_at >= month_start).count()
        
        # VIP用户统计
        active_vip_users = Subscription.query.filter(
            Subscription.status == SubscriptionStatus.ACTIVE,
            Subscription.end_at > now
        ).count()
        
        # 订单统计
        total_orders = Order.query.count()
        paid_orders = Order.query.filter(Order.status == OrderStatus.PAID).count()
        today_orders = Order.query.filter(Order.created_at >= today_start).count()
        today_paid_orders = Order.query.filter(
            Order.created_at >= today_start,
            Order.status == OrderStatus.PAID
        ).count()
        
        # 收入统计
        total_revenue = db.session.query(func.sum(Order.total_cent)).filter(
            Order.status == OrderStatus.PAID
        ).scalar() or 0
        
        today_revenue = db.session.query(func.sum(Order.total_cent)).filter(
            Order.created_at >= today_start,
            Order.status == OrderStatus.PAID
        ).scalar() or 0
        
        week_revenue = db.session.query(func.sum(Order.total_cent)).filter(
            Order.created_at >= week_start,
            Order.status == OrderStatus.PAID
        ).scalar() or 0
        
        month_revenue = db.session.query(func.sum(Order.total_cent)).filter(
            Order.created_at >= month_start,
            Order.status == OrderStatus.PAID
        ).scalar() or 0
        
        # 套餐统计
        plan_stats = db.session.query(
            Plan.name,
            func.count(Order.id).label('order_count'),
            func.sum(Order.total_cent).label('revenue')
        ).join(Order).filter(
            Order.status == OrderStatus.PAID
        ).group_by(Plan.id, Plan.name).all()
        
        return jsonify({
            'users': {
                'total': total_users,
                'today_new': today_new_users,
                'week_new': week_new_users,
                'month_new': month_new_users,
                'active_vip': active_vip_users,
                'vip_rate': round((active_vip_users / total_users * 100), 2) if total_users > 0 else 0
            },
            'orders': {
                'total': total_orders,
                'paid': paid_orders,
                'today_total': today_orders,
                'today_paid': today_paid_orders,
                'success_rate': round((paid_orders / total_orders * 100), 2) if total_orders > 0 else 0
            },
            'revenue': {
                'total_yuan': total_revenue / 100,
                'today_yuan': today_revenue / 100,
                'week_yuan': week_revenue / 100,
                'month_yuan': month_revenue / 100
            },
            'plans': [
                {
                    'name': stat.name,
                    'order_count': stat.order_count,
                    'revenue_yuan': stat.revenue / 100 if stat.revenue else 0
                }
                for stat in plan_stats
            ]
        })
        
    except Exception as e:
        logger.error("Failed to get statistics", error=str(e))
        return jsonify({'error': 'Failed to get statistics'}), 500


@bp.route('/api/statistics/charts')
@admin_required
def api_statistics_charts():
    """Get chart data for statistics"""
    try:
        from datetime import datetime, timedelta
        
        # 获取最近30天的数据
        end_date = datetime.utcnow().date()
        start_date = end_date - timedelta(days=29)
        
        # 用户注册趋势
        user_trend = []
        current_date = start_date
        while current_date <= end_date:
            next_date = current_date + timedelta(days=1)
            count = User.query.filter(
                User.created_at >= current_date,
                User.created_at < next_date
            ).count()
            user_trend.append({
                'date': current_date.strftime('%Y-%m-%d'),
                'count': count
            })
            current_date = next_date
        
        # 订单趋势
        order_trend = []
        revenue_trend = []
        current_date = start_date
        while current_date <= end_date:
            next_date = current_date + timedelta(days=1)
            
            # 订单数量
            order_count = Order.query.filter(
                Order.created_at >= current_date,
                Order.created_at < next_date
            ).count()
            
            paid_count = Order.query.filter(
                Order.created_at >= current_date,
                Order.created_at < next_date,
                Order.status == OrderStatus.PAID
            ).count()
            
            # 收入
            revenue = db.session.query(func.sum(Order.total_cent)).filter(
                Order.created_at >= current_date,
                Order.created_at < next_date,
                Order.status == OrderStatus.PAID
            ).scalar() or 0
            
            order_trend.append({
                'date': current_date.strftime('%Y-%m-%d'),
                'total': order_count,
                'paid': paid_count
            })
            
            revenue_trend.append({
                'date': current_date.strftime('%Y-%m-%d'),
                'revenue': revenue / 100
            })
            
            current_date = next_date
        
        return jsonify({
            'user_trend': user_trend,
            'order_trend': order_trend,
            'revenue_trend': revenue_trend
        })
        
    except Exception as e:
        logger.error("Failed to get chart data", error=str(e))
        return jsonify({'error': 'Failed to get chart data'}), 500


@bp.route('/api/health')
@admin_required
def api_health():
    """System health check endpoint"""
    try:
        # 检查数据库连接
        db.session.execute(text('SELECT 1'))
        
        # 获取基本统计
        user_count = User.query.count()
        order_count = Order.query.count()
        
        return jsonify({
            'status': 'healthy',
            'database': 'connected',
            'users': user_count,
            'orders': order_count,
            'timestamp': func.now()
        })
        
    except Exception as e:
        logger.error("Health check failed", error=str(e))
        return jsonify({
            'status': 'unhealthy',
            'error': str(e)
        }), 500


@bp.route('/api/plans')
@admin_required
def api_plans():
    """API endpoint for plan management"""
    try:
        # 获取所有套餐
        plans = Plan.query.order_by(Plan.created_at.desc()).all()
        
        plans_data = []
        for plan in plans:
            # 获取套餐的订单统计
            order_count = Order.query.filter_by(plan_id=plan.id).count()
            paid_order_count = Order.query.filter(
                Order.plan_id == plan.id,
                Order.status == OrderStatus.PAID
            ).count()
            
            # 获取套餐的收入统计
            total_revenue = db.session.query(func.sum(Order.total_cent)).filter(
                Order.plan_id == plan.id,
                Order.status == OrderStatus.PAID
            ).scalar() or 0
            
            # 获取当前使用该套餐的活跃订阅数
            active_subscriptions = Subscription.query.filter(
                Subscription.plan_id == plan.id,
                Subscription.status == SubscriptionStatus.ACTIVE,
                Subscription.end_at > func.now()
            ).count()
            
            plan_dict = plan.to_dict()
            plan_dict.update({
                'order_count': order_count,
                'paid_order_count': paid_order_count,
                'total_revenue_yuan': total_revenue / 100,
                'active_subscriptions': active_subscriptions,
                'conversion_rate': round((paid_order_count / order_count * 100), 2) if order_count > 0 else 0
            })
            plans_data.append(plan_dict)
        
        return jsonify({
            'plans': plans_data,
            'total': len(plans_data)
        })
        
    except Exception as e:
        logger.error("Failed to retrieve plans", error=str(e))
        return jsonify({'error': 'Failed to retrieve plans'}), 500


@bp.route('/api/plans/<int:plan_id>')
@admin_required
def api_plan_detail(plan_id):
    """Get detailed plan information"""
    try:
        plan = Plan.query.filter_by(id=plan_id).first()
        
        if not plan:
            return jsonify({'error': 'Plan not found'}), 404
        
        # 获取详细统计信息
        from datetime import datetime, timedelta
        
        now = datetime.utcnow()
        month_start = now - timedelta(days=30)
        
        # 订单统计
        total_orders = Order.query.filter_by(plan_id=plan_id).count()
        paid_orders = Order.query.filter(
            Order.plan_id == plan_id,
            Order.status == OrderStatus.PAID
        ).count()
        
        month_orders = Order.query.filter(
            Order.plan_id == plan_id,
            Order.created_at >= month_start
        ).count()
        
        # 收入统计
        total_revenue = db.session.query(func.sum(Order.total_cent)).filter(
            Order.plan_id == plan_id,
            Order.status == OrderStatus.PAID
        ).scalar() or 0
        
        month_revenue = db.session.query(func.sum(Order.total_cent)).filter(
            Order.plan_id == plan_id,
            Order.created_at >= month_start,
            Order.status == OrderStatus.PAID
        ).scalar() or 0
        
        # 订阅统计
        active_subscriptions = Subscription.query.filter(
            Subscription.plan_id == plan_id,
            Subscription.status == SubscriptionStatus.ACTIVE,
            Subscription.end_at > now
        ).count()
        
        expired_subscriptions = Subscription.query.filter(
            Subscription.plan_id == plan_id,
            or_(
                Subscription.status == SubscriptionStatus.EXPIRED,
                and_(
                    Subscription.status == SubscriptionStatus.ACTIVE,
                    Subscription.end_at <= now
                )
            )
        ).count()
        
        plan_data = plan.to_dict()
        plan_data.update({
            'statistics': {
                'orders': {
                    'total': total_orders,
                    'paid': paid_orders,
                    'month': month_orders,
                    'conversion_rate': round((paid_orders / total_orders * 100), 2) if total_orders > 0 else 0
                },
                'revenue': {
                    'total_yuan': total_revenue / 100,
                    'month_yuan': month_revenue / 100
                },
                'subscriptions': {
                    'active': active_subscriptions,
                    'expired': expired_subscriptions,
                    'total': active_subscriptions + expired_subscriptions
                }
            }
        })
        
        return jsonify(plan_data)
        
    except Exception as e:
        logger.error("Failed to get plan detail", plan_id=plan_id, error=str(e))
        return jsonify({'error': 'Failed to get plan detail'}), 500


@bp.route('/api/plans/<int:plan_id>', methods=['PUT'])
@super_admin_required
def api_update_plan(plan_id):
    """Update plan information"""
    try:
        plan = Plan.query.filter_by(id=plan_id).first()
        
        if not plan:
            return jsonify({'error': 'Plan not found'}), 404
        
        data = request.get_json()
        
        # 验证输入数据
        if not data:
            return jsonify({'error': 'No data provided'}), 400
        
        # 更新套餐信息
        if 'name' in data:
            if not data['name'].strip():
                return jsonify({'error': 'Plan name cannot be empty'}), 400
            plan.name = data['name'].strip()
        
        if 'description' in data:
            plan.description = data['description']
        
        if 'price_yuan' in data:
            try:
                price_yuan = float(data['price_yuan'])
                if price_yuan < 0:
                    return jsonify({'error': 'Price cannot be negative'}), 400
                plan.price_cent = int(price_yuan * 100)
            except (ValueError, TypeError):
                return jsonify({'error': 'Invalid price format'}), 400
        
        if 'period_days' in data:
            try:
                period_days = int(data['period_days'])
                if period_days <= 0:
                    return jsonify({'error': 'Period days must be positive'}), 400
                plan.period_days = period_days
            except (ValueError, TypeError):
                return jsonify({'error': 'Invalid period days format'}), 400
        
        if 'active' in data:
            plan.active = bool(data['active'])
        
        # 更新时间戳
        from app.utils.time import utc_now
        plan.updated_at = utc_now()
        
        db.session.commit()
        
        logger.info("Plan updated successfully", plan_id=plan_id, 
                   name=plan.name, price_yuan=plan.price_yuan)
        
        return jsonify({
            'message': 'Plan updated successfully',
            'plan': plan.to_dict()
        })
        
    except Exception as e:
        db.session.rollback()
        logger.error("Failed to update plan", plan_id=plan_id, error=str(e))
        return jsonify({'error': 'Failed to update plan'}), 500


@bp.route('/api/plans', methods=['POST'])
@super_admin_required
def api_create_plan():
    """Create new plan"""
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({'error': 'No data provided'}), 400
        
        # 验证必需字段
        required_fields = ['code', 'name', 'price_yuan', 'period_days']
        for field in required_fields:
            if field not in data or not data[field]:
                return jsonify({'error': f'Field {field} is required'}), 400
        
        # 检查套餐代码是否已存在
        existing_plan = Plan.query.filter_by(code=data['code']).first()
        if existing_plan:
            return jsonify({'error': 'Plan code already exists'}), 400
        
        # 验证数据格式
        try:
            price_yuan = float(data['price_yuan'])
            if price_yuan < 0:
                return jsonify({'error': 'Price cannot be negative'}), 400
            price_cent = int(price_yuan * 100)
        except (ValueError, TypeError):
            return jsonify({'error': 'Invalid price format'}), 400
        
        try:
            period_days = int(data['period_days'])
            if period_days <= 0:
                return jsonify({'error': 'Period days must be positive'}), 400
        except (ValueError, TypeError):
            return jsonify({'error': 'Invalid period days format'}), 400
        
        # 创建新套餐
        plan = Plan(
            code=data['code'].strip(),
            name=data['name'].strip(),
            description=data.get('description', ''),
            price_cent=price_cent,
            period_days=period_days,
            active=data.get('active', True)
        )
        
        db.session.add(plan)
        db.session.commit()
        
        logger.info("Plan created successfully", plan_id=plan.id, 
                   code=plan.code, name=plan.name)
        
        return jsonify({
            'message': 'Plan created successfully',
            'plan': plan.to_dict()
        }), 201
        
    except Exception as e:
        db.session.rollback()
        logger.error("Failed to create plan", error=str(e))
        return jsonify({'error': 'Failed to create plan'}), 500


@bp.route('/api/plans/<int:plan_id>', methods=['DELETE'])
@super_admin_required
def api_delete_plan(plan_id):
    """Delete plan (soft delete by setting active=False)"""
    try:
        plan = Plan.query.filter_by(id=plan_id).first()
        
        if not plan:
            return jsonify({'error': 'Plan not found'}), 404
        
        # 检查是否有活跃的订阅使用此套餐
        active_subscriptions = Subscription.query.filter(
            Subscription.plan_id == plan_id,
            Subscription.status == SubscriptionStatus.ACTIVE,
            Subscription.end_at > func.now()
        ).count()
        
        if active_subscriptions > 0:
            return jsonify({
                'error': f'Cannot delete plan with {active_subscriptions} active subscriptions. Please disable it instead.'
            }), 400
        
        # 软删除：设置为不活跃
        plan.active = False
        from app.utils.time import utc_now
        plan.updated_at = utc_now()
        
        db.session.commit()
        
        logger.info("Plan deactivated successfully", plan_id=plan_id, 
                   code=plan.code, name=plan.name)
        
        return jsonify({'message': 'Plan deactivated successfully'})
        
    except Exception as e:
        db.session.rollback()
        logger.error("Failed to delete plan", plan_id=plan_id, error=str(e))
        return jsonify({'error': 'Failed to delete plan'}), 500


@bp.route('/api/stats')
@admin_required
def api_stats():
    """Get user statistics for dashboard"""
    try:
        from datetime import datetime, timedelta
        
        now = datetime.utcnow()
        today_start = now.replace(hour=0, minute=0, second=0, microsecond=0)
        week_start = today_start - timedelta(days=7)
        month_start = today_start - timedelta(days=30)
        
        # 基础用户统计
        total_users = User.query.count()
        today_new_users = User.query.filter(User.created_at >= today_start).count()
        week_new_users = User.query.filter(User.created_at >= week_start).count()
        month_new_users = User.query.filter(User.created_at >= month_start).count()
        
        # VIP用户统计
        active_vip_users = Subscription.query.filter(
            Subscription.status == SubscriptionStatus.ACTIVE,
            Subscription.end_at > now
        ).count()
        
        stats_data = {
            'total_users': total_users,
            'today_new_users': today_new_users,
            'week_new_users': week_new_users,
            'month_new_users': month_new_users,
            'active_vip_users': active_vip_users,
            'vip_percentage': round((active_vip_users / total_users * 100), 2) if total_users > 0 else 0
        }
        
        return jsonify(stats_data)

    except Exception as e:
        logger.error("Failed to get user statistics", error=str(e))
        return jsonify({'error': 'Failed to get statistics'}), 500
