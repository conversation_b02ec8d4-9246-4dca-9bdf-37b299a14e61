"""
Webhook routes for handling external callbacks
"""
from flask import Blueprint, request, jsonify
import structlog

from app.extensions import limiter
from app.services.payment_callback_service import payment_callback_service, PaymentCallbackError

logger = structlog.get_logger()

bp = Blueprint('webhook', __name__)


@bp.route('/wechatpay', methods=['POST'])
@limiter.limit("100 per minute")  # Allow high frequency for legitimate callbacks
def wechatpay_callback():
    """
    WeChat Pay callback endpoint
    
    This endpoint receives payment notifications from WeChat Pay.
    It verifies the signature, decrypts the payload, and processes the payment.
    
    Expected headers:
    - Wechatpay-Timestamp: Request timestamp
    - Wechatpay-Nonce: Random string
    - Wechatpay-Signature: Request signature
    - Wechatpay-Serial: Certificate serial number
    
    Response:
    - 200: Success (required by WeChat Pay)
    - 401: Signature verification failed
    - 400: Invalid request format
    - 500: Internal processing error
    """
    try:
        # Log callback attempt
        wechatpay_headers = {k: v for k, v in request.headers.items() if k.startswith('Wechatpay')}
        logger.info("WeChat Pay callback received", 
                   headers=wechatpay_headers,
                   content_type=request.content_type,
                   content_length=request.content_length)
        
        # Check if we have the public key serial in headers
        from flask import current_app
        pub_key_id = current_app.config.get('WXPAY_PUB_KEY_ID')
        if pub_key_id and 'Wechatpay-Serial' in wechatpay_headers:
            expected_serial = pub_key_id
            actual_serial = wechatpay_headers['Wechatpay-Serial']
            if expected_serial != actual_serial:
                logger.warning("Public key serial mismatch", 
                              expected=expected_serial,
                              actual=actual_serial)
        
        # Validate content type
        if request.content_type != 'application/json':
            logger.warning("Invalid content type for WeChat Pay callback", 
                          content_type=request.content_type)
            return jsonify({
                'code': 'FAIL',
                'message': 'Invalid content type'
            }), 400
        
        # Get request data
        headers = dict(request.headers)
        body = request.get_data()
        
        # Validate required headers
        required_headers = ['Wechatpay-Timestamp', 'Wechatpay-Nonce', 'Wechatpay-Signature', 'Wechatpay-Serial']
        missing_headers = [h for h in required_headers if h not in headers]
        
        if missing_headers:
            logger.warning("Missing required headers for WeChat Pay callback", 
                          missing_headers=missing_headers)
            return jsonify({
                'code': 'FAIL',
                'message': f'Missing headers: {", ".join(missing_headers)}'
            }), 400
        
        # Process callback
        try:
            result = payment_callback_service.process_callback(headers, body)
            
            logger.info("WeChat Pay callback processed successfully", 
                       result=result)
            
            # WeChat Pay expects a 200 or 204 response with no body for success
            # According to WeChat Pay documentation, successful response should have no body
            return '', 200
            
        except PaymentCallbackError as e:
            logger.error("WeChat Pay callback processing failed", 
                        error=str(e))
            
            # Return appropriate error response
            if "Invalid signature" in str(e):
                return jsonify({
                    'code': 'FAIL',
                    'message': 'Signature verification failed'
                }), 401
            else:
                return jsonify({
                    'code': 'FAIL',
                    'message': 'Processing failed'
                }), 400
        
    except Exception as e:
        logger.error("Unexpected error in WeChat Pay callback", 
                    error=str(e),
                    exc_info=True)
        
        # Return 500 to trigger WeChat Pay retry
        return jsonify({
            'code': 'FAIL',
            'message': 'Internal server error'
        }), 500


@bp.route('/wechatpay/test', methods=['POST'])
@limiter.limit("10 per minute")
def wechatpay_test():
    """
    Test endpoint for WeChat Pay callback (development only)
    
    This endpoint can be used for testing callback processing
    without actual WeChat Pay integration.
    """
    try:
        # Only allow in development mode
        from flask import current_app
        if not current_app.config.get('DEBUG'):
            return jsonify({
                'error': 'Not Found'
            }), 404
        
        logger.info("WeChat Pay test callback received")
        
        # Mock callback data for testing
        test_data = request.get_json() or {}
        
        logger.info("Test callback data", data=test_data)
        
        return jsonify({
            'code': 'SUCCESS',
            'message': 'Test callback received',
            'data': test_data
        }), 200
        
    except Exception as e:
        logger.error("Error in test callback", error=str(e))
        return jsonify({
            'error': 'Internal Server Error',
            'message': str(e)
        }), 500


@bp.route('/health', methods=['GET'])
def webhook_health():
    """
    Health check endpoint for webhook service
    
    Response:
    {
        "status": "healthy",
        "timestamp": "2023-10-15T10:30:00Z"
    }
    """
    from app.utils.time import utc_now
    
    return jsonify({
        'status': 'healthy',
        'service': 'webhook',
        'timestamp': utc_now().isoformat()
    }), 200
