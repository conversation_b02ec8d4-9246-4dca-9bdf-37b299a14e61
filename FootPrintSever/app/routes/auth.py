"""
Authentication routes for WeChat login
"""
from flask import Blueprint, request, jsonify
from flask_limiter.util import get_remote_address
import structlog

from app.extensions import limiter
from app.services.wechat_oauth import wechat_oauth_service, WeChatOAuthError
from app.services.jwt_service import jwt_service
from app.utils.idempotency import idempotent

logger = structlog.get_logger()

bp = Blueprint('auth', __name__)


@bp.route('/wechat/login', methods=['POST'])
@limiter.limit("10 per minute")
@idempotent(timeout=60)  # 1 minute idempotency for login
def wechat_login():
    """
    WeChat login endpoint
    
    Request body:
    {
        "code": "authorization_code_from_wechat"
    }
    
    Response:
    {
        "access_token": "jwt_token",
        "token_type": "Bearer",
        "expires_in": 86400,
        "user": {
            "id": 1,
            "openid": "...",
            "nickname": "...",
            "avatar_url": "..."
        }
    }
    """
    try:
        # Validate request
        if not request.is_json:
            return jsonify({
                'error': 'Bad Request',
                'message': 'Content-Type must be application/json'
            }), 400
        
        data = request.get_json()
        code = data.get('code')
        device_id = data.get('device_id')
        
        if not code:
            return jsonify({
                'error': 'Bad Request',
                'message': 'Authorization code is required'
            }), 400
        
        # Perform WeChat login
        user = wechat_oauth_service.login_with_code(code)
        
        # Update device ID and app version if provided
        if device_id:
            user.device_id = device_id
            app_version = data.get('app_version')
            if app_version:
                user.app_version = app_version
            
            from app.extensions import db
            from app.models.user_device import UserDevice
            
            # Record device login history
            device_info = {
                'device_name': data.get('device_name'),
                'device_model': data.get('device_model'),
                'os_version': data.get('os_version'),
                'app_version': app_version
            }
            UserDevice.record_login(user.id, device_id, device_info)
            
            db.session.commit()
            logger.info("Device login recorded", user_id=user.id, device_id=device_id, app_version=app_version)
        
        # Create JWT token response
        token_response = jwt_service.create_token_response(user)
        
        logger.info("WeChat login successful", 
                   user_id=user.id, 
                   openid=user.openid,
                   remote_addr=get_remote_address())
        
        return jsonify(token_response), 200
        
    except WeChatOAuthError as e:
        logger.warning("WeChat login failed", 
                      error=str(e), 
                      remote_addr=get_remote_address())
        return jsonify({
            'error': 'WeChat Login Failed',
            'message': str(e)
        }), 400
        
    except Exception as e:
        logger.error("Unexpected error during WeChat login", 
                    error=str(e), 
                    remote_addr=get_remote_address())
        return jsonify({
            'error': 'Internal Server Error',
            'message': 'An unexpected error occurred during login'
        }), 500


@bp.route('/verify', methods=['POST'])
@limiter.limit("30 per minute")
def verify_token():
    """
    Verify JWT token endpoint
    
    Headers:
    Authorization: Bearer <jwt_token>
    
    Response:
    {
        "valid": true,
        "user": {
            "id": 1,
            "openid": "...",
            "nickname": "...",
            "avatar_url": "..."
        }
    }
    """
    try:
        auth_header = request.headers.get('Authorization')
        
        if not auth_header or not auth_header.startswith('Bearer '):
            return jsonify({
                'error': 'Unauthorized',
                'message': 'Authorization header is required'
            }), 401
        
        token = auth_header.split(' ')[1]
        
        # Decode and verify token
        decoded_token = jwt_service.decode_token(token)
        user_id = decoded_token.get('sub')  # 'sub' is the identity
        
        if not user_id:
            return jsonify({
                'error': 'Unauthorized',
                'message': 'Invalid token'
            }), 401
        
        # Get user info from token claims
        user_info = {
            'id': decoded_token.get('user_id'),
            'openid': decoded_token.get('openid'),
            'nickname': decoded_token.get('nickname'),
            'avatar_url': decoded_token.get('avatar_url'),
            'is_vip': decoded_token.get('is_vip', False),
            'vip_expire_time': decoded_token.get('vip_expire_time', 0)
        }
        
        return jsonify({
            'valid': True,
            'user': user_info
        }), 200
        
    except Exception as e:
        logger.warning("Token verification failed", 
                      error=str(e), 
                      remote_addr=get_remote_address())
        return jsonify({
            'error': 'Unauthorized',
            'message': 'Invalid or expired token'
        }), 401


@bp.route('/refresh', methods=['POST'])
@limiter.limit("20 per minute")
def refresh_token():
    """
    Refresh access token using refresh token
    
    Request body:
    {
        "refresh_token": "refresh_token_string"
    }
    
    Response:
    {
        "bearerToken": "new_access_token",
        "refreshToken": "new_refresh_token", 
        "token_type": "Bearer",
        "expires_in": 604800,
        "refresh_expires_in": 7776000,
        "user": {...}
    }
    """
    try:
        # Validate request
        if not request.is_json:
            return jsonify({
                'error': 'Bad Request',
                'message': 'Content-Type must be application/json'
            }), 400
        
        data = request.get_json()
        refresh_token = data.get('refresh_token')
        
        if not refresh_token:
            return jsonify({
                'error': 'Bad Request',
                'message': 'Refresh token is required'
            }), 400
        
        # Verify refresh token
        user = jwt_service.verify_refresh_token(refresh_token)
        
        if not user:
            return jsonify({
                'error': 'Unauthorized',
                'message': 'Invalid or expired refresh token'
            }), 401
        
        # Create new token response (includes new access and refresh tokens)
        token_response = jwt_service.create_token_response(user)
        
        logger.info("Token refreshed successfully", 
                   user_id=user.id, 
                   remote_addr=get_remote_address())
        
        return jsonify(token_response), 200
        
    except Exception as e:
        logger.error("Token refresh failed", 
                    error=str(e), 
                    remote_addr=get_remote_address())
        return jsonify({
            'error': 'Internal Server Error',
            'message': 'Failed to refresh token'
        }), 500


@bp.route('/logout', methods=['POST'])
@limiter.limit("10 per minute")
def logout():
    """
    Logout endpoint (revokes refresh token)
    
    Headers:
    Authorization: Bearer <jwt_token>
    
    Response:
    {
        "message": "Logged out successfully"
    }
    """
    try:
        # Try to get user from token to revoke refresh token
        auth_header = request.headers.get('Authorization')
        
        if auth_header and auth_header.startswith('Bearer '):
            try:
                token = auth_header.split(' ')[1]
                decoded_token = jwt_service.decode_token(token)
                user_id = decoded_token.get('user_id')
                
                if user_id:
                    from app.models.user import User
                    user = User.query.get(user_id)
                    if user:
                        jwt_service.revoke_refresh_token(user)
                        logger.info("Refresh token revoked during logout", user_id=user.id)
            except Exception as e:
                # Don't fail logout if token is invalid
                logger.warning("Failed to revoke refresh token during logout", error=str(e))
        
        logger.info("User logout", remote_addr=get_remote_address())
        
        return jsonify({
            'message': 'Logged out successfully'
        }), 200
        
    except Exception as e:
        logger.error("Error during logout", 
                    error=str(e), 
                    remote_addr=get_remote_address())
        return jsonify({
            'error': 'Internal Server Error',
            'message': 'An error occurred during logout'
        }), 500
