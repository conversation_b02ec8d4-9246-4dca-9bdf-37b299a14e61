"""
Application configuration
"""
import os
from datetime import timedelta
from dotenv import load_dotenv

# Load environment variables from .env files
# Only load .env.local in development environment for security
load_dotenv()

# Only load .env.local in development environment
flask_env = os.environ.get('FLASK_ENV', 'development')
if flask_env == 'development':
    load_dotenv('.env.local')


class Config:
    """Base configuration class"""
    
    # Flask Configuration
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'dev-secret-key-change-in-production'
    
    # Database Configuration
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or \
        'mysql+pymysql://root:password@localhost:3306/footprint_subscription'
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    SQLALCHEMY_ENGINE_OPTIONS = {
        'pool_pre_ping': True,
        'pool_recycle': 300,
    }
    
    # Redis Configuration
    REDIS_URL = os.environ.get('REDIS_URL') or 'redis://localhost:6379/0'
    
    # WeChat OAuth Configuration
    WECHAT_APPID = os.environ.get('WECHAT_APPID')
    WECHAT_APPSECRET = os.environ.get('WECHAT_APPSECRET')
    
    # WeChat Pay Configuration
    WXPAY_APPID = os.environ['WXPAY_APPID']
    WXPAY_MCH_ID = os.environ['WXPAY_MCH_ID']
    WXPAY_API_KEY = os.environ['WXPAY_API_KEY']
    WXPAY_MCH_PRIVATE_KEY_PATH = os.environ['WXPAY_MCH_PRIVATE_KEY_PATH']
    WXPAY_CERT_SERIAL = os.environ['WXPAY_CERT_SERIAL']
    WXPAY_PLATFORM_CERT_PATH = os.environ['WXPAY_PLATFORM_CERT_PATH']
    
    # WeChat Pay Public Key Configuration (for signature verification)
    WXPAY_PUB_KEY_ID = os.environ.get('WXPAY_PUB_KEY_ID')
    WXPAY_PUB_CERT_PATH = os.environ['WXPAY_PUB_CERT_PATH']

    # 兼容性配置（为了向后兼容）
    WECHAT_APP_ID = WXPAY_APPID
    WECHAT_MCH_ID = WXPAY_MCH_ID
    WECHAT_API_KEY = WXPAY_API_KEY
    
    # Webhook Configuration
    WEBHOOK_BASE_URL = os.environ.get('WEBHOOK_BASE_URL') or 'https://localhost:5000'
    
    # JWT Configuration
    JWT_SECRET_KEY = os.environ.get('JWT_SECRET_KEY') or SECRET_KEY
    JWT_ACCESS_TOKEN_EXPIRES = timedelta(seconds=int(os.environ.get('JWT_ACCESS_TOKEN_EXPIRES', 86400*30)))
    JWT_ALGORITHM = 'HS256'
    
    # Session Configuration
    PERMANENT_SESSION_LIFETIME = timedelta(hours=24)  # Admin session expires in 24 hours
    
    # Rate Limiting
    RATELIMIT_STORAGE_URL = REDIS_URL
    RATELIMIT_DEFAULT = "100 per hour"
    
    # Logging
    LOG_LEVEL = os.environ.get('LOG_LEVEL', 'INFO')
    
    # Scheduler Configuration
    SCHEDULER_API_ENABLED = True
    SCHEDULER_TIMEZONE = 'Asia/Shanghai'


class DevelopmentConfig(Config):
    """Development configuration"""
    DEBUG = True


class ProductionConfig(Config):
    """Production configuration"""
    DEBUG = False


class TestingConfig(Config):
    """Testing configuration"""
    TESTING = True
    SQLALCHEMY_DATABASE_URI = 'sqlite:///:memory:'
    WTF_CSRF_ENABLED = False


config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'testing': TestingConfig,
    'default': DevelopmentConfig
}
