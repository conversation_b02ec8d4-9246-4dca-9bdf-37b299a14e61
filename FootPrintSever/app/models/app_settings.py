"""
应用设置模型
存储应用级别的配置信息，如版本控制等
"""
from app.extensions import db
from datetime import datetime


class AppSettings(db.Model):
    """应用设置表"""
    __tablename__ = 'app_settings'
    
    id = db.Column(db.Integer, primary_key=True)
    # Android 版本配置
    latest_version_code = db.Column(db.Integer, nullable=False, default=1, comment='最新版本号')
    min_version_code = db.Column(db.Integer, nullable=False, default=1, comment='最低可用版本号')
    download_url = db.Column(db.String(512), nullable=False, default='', comment='新版本下载地址')
    
    # 时间戳
    created_at = db.Column(db.DateTime, nullable=False, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, nullable=False, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'latest_version_code': self.latest_version_code,
            'min_version_code': self.min_version_code,
            'download_url': self.download_url,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
    
    @staticmethod
    def get_settings():
        """获取设置（单例模式）"""
        settings = AppSettings.query.first()
        if not settings:
            # 如果不存在，创建默认设置
            settings = AppSettings(
                latest_version_code=1,
                min_version_code=1,
                download_url=''
            )
            db.session.add(settings)
            db.session.commit()
        return settings
    
    @staticmethod
    def update_settings(latest_version_code=None, min_version_code=None, download_url=None):
        """更新设置"""
        settings = AppSettings.get_settings()
        
        if latest_version_code is not None:
            settings.latest_version_code = latest_version_code
        if min_version_code is not None:
            settings.min_version_code = min_version_code
        if download_url is not None:
            settings.download_url = download_url
        
        settings.updated_at = datetime.utcnow()
        db.session.commit()
        return settings
