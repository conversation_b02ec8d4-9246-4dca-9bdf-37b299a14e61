"""
User model for storing WeChat user information
"""
from app.extensions import db
from app.utils.time import utc_now
from sqlalchemy import Index


class User(db.Model):
    """User model for WeChat users"""
    
    __tablename__ = 'users'
    
    id = db.Column(db.<PERSON>I<PERSON>ger, primary_key=True)
    openid = db.Column(db.String(128), unique=True, nullable=False, index=True)
    unionid = db.Column(db.String(128), unique=True, nullable=True, index=True)
    nickname = db.Column(db.String(128), nullable=True)
    avatar_url = db.Column(db.String(512), nullable=True)
    device_id = db.Column(db.String(128), nullable=True, index=True)
    app_version = db.Column(db.String(32), nullable=True)
    refresh_token = db.Column(db.String(512), nullable=True, index=True)
    refresh_token_expires_at = db.Column(db.DateTime, nullable=True)
    created_at = db.Column(db.DateTime, default=utc_now, nullable=False)
    updated_at = db.Column(db.DateTime, default=utc_now, onupdate=utc_now, nullable=False)
    
    # Relationships
    orders = db.relationship('Order', backref='user', lazy='dynamic')
    subscription = db.relationship('Subscription', backref='user', uselist=False)
    
    def __repr__(self):
        return f'<User {self.id}: {self.nickname or self.openid}>'
    
    def to_dict(self):
        """Convert user to dictionary"""
        subscription = self.subscription
        is_vip = subscription.is_active() if subscription else False
        vip_expire_time = int(subscription.end_at.timestamp() * 1000) if subscription and subscription.end_at else 0

        return {
            'id': self.id,
            'openid': self.openid,
            'unionid': self.unionid,
            'nickname': self.nickname,
            'avatar_url': self.avatar_url,
            'is_vip': is_vip,
            'vip_expire_time': vip_expire_time,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
    
    @classmethod
    def find_by_openid(cls, openid):
        """Find user by openid"""
        return cls.query.filter_by(openid=openid).first()
    
    @classmethod
    def find_by_unionid(cls, unionid):
        """Find user by unionid"""
        if not unionid:
            return None
        return cls.query.filter_by(unionid=unionid).first()
    
    @classmethod
    def create_or_update(cls, openid, unionid=None, nickname=None, avatar_url=None):
        """Create new user or update existing user"""
        user = cls.find_by_openid(openid)
        
        if not user and unionid:
            # Try to find by unionid if openid not found
            user = cls.find_by_unionid(unionid)
        
        if user:
            # Update existing user
            user.unionid = unionid or user.unionid
            user.nickname = nickname or user.nickname
            user.avatar_url = avatar_url or user.avatar_url
            user.updated_at = utc_now()
        else:
            # Create new user
            user = cls(
                openid=openid,
                unionid=unionid,
                nickname=nickname,
                avatar_url=avatar_url
            )
            db.session.add(user)
        
        db.session.commit()
        return user

    def is_vip(self):
        """Check if user has active VIP subscription"""
        return self.subscription and self.subscription.is_active()

    def get_vip_expire_time(self):
        """Get VIP expiration time in milliseconds"""
        if self.subscription and self.subscription.end_at:
            return int(self.subscription.end_at.timestamp() * 1000)
        return 0
