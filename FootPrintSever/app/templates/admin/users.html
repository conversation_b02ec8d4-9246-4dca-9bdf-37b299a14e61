{% extends "admin/base.html" %}

{% block title %}用户管理 - FootPrint 管理后台{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-users me-2"></i>
        用户管理
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="refreshUsers()">
                <i class="fas fa-sync-alt"></i> 刷新
            </button>
            <button type="button" class="btn btn-sm btn-outline-primary" onclick="exportUsers()">
                <i class="fas fa-download"></i> 导出
            </button>
        </div>
    </div>
</div>

<!-- 搜索和筛选 -->
<div class="card mb-4">
    <div class="card-body">
        <div class="row g-3">
            <div class="col-md-4">
                <label for="searchInput" class="form-label">搜索用户</label>
                <input type="text" class="form-control search-box" id="searchInput" 
                       placeholder="输入用户ID、昵称或OpenID">
            </div>
            <div class="col-md-3">
                <label for="vipFilter" class="form-label">VIP状态</label>
                <select class="form-select" id="vipFilter">
                    <option value="">全部用户</option>
                    <option value="vip">VIP用户</option>
                    <option value="non_vip">非VIP用户</option>
                </select>
            </div>
            <div class="col-md-2">
                <label for="sortBy" class="form-label">排序字段</label>
                <select class="form-select" id="sortBy">
                    <option value="created_at">注册时间</option>
                    <option value="updated_at">更新时间</option>
                    <option value="id">用户ID</option>
                    <option value="nickname">昵称</option>
                </select>
            </div>
            <div class="col-md-2">
                <label for="sortOrder" class="form-label">排序方式</label>
                <select class="form-select" id="sortOrder">
                    <option value="desc">降序</option>
                    <option value="asc">升序</option>
                </select>
            </div>
            <div class="col-md-1">
                <label class="form-label">&nbsp;</label>
                <button type="button" class="btn btn-primary w-100" onclick="searchUsers()">
                    <i class="fas fa-search"></i>
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 加载状态 -->
<div class="loading" id="loadingIndicator">
    <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">加载中...</span>
    </div>
    <p class="mt-2">正在加载用户数据...</p>
</div>

<!-- 用户列表 -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="card-title mb-0">
            <i class="fas fa-list me-2"></i>
            用户列表
        </h5>
        <span class="badge bg-primary" id="totalCount">0 用户</span>
    </div>
    <div class="card-body p-0">
        <div class="table-responsive">
            <table class="table table-hover mb-0" id="usersTable">
                <thead class="table-light">
                    <tr>
                        <th>ID</th>
                        <th>头像</th>
                        <th>昵称</th>
                        <th>OpenID</th>
                        <th>VIP状态</th>
                        <th>APP版本</th>
                        <th>注册时间</th>
                        <th>最后更新</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody id="usersTableBody">
                    <!-- 用户数据将通过JavaScript动态加载 -->
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- 分页 -->
<nav aria-label="用户列表分页" class="mt-4">
    <ul class="pagination justify-content-center" id="pagination">
        <!-- 分页按钮将通过JavaScript动态生成 -->
    </ul>
</nav>

<!-- 用户详情模态框 -->
<div class="modal fade" id="userDetailModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-user me-2"></i>
                    用户详情
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="userDetailContent">
                <!-- 用户详情内容将通过JavaScript动态加载 -->
            </div>
        </div>
    </div>
</div>

<!-- 删除确认模态框 -->
<div class="modal fade" id="deleteConfirmModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title text-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    确认删除用户
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-danger">
                    <i class="fas fa-warning me-2"></i>
                    <strong>警告：</strong>此操作不可撤销！删除用户将同时删除：
                    <ul class="mt-2 mb-0">
                        <li>用户的所有订单记录</li>
                        <li>用户的订阅信息</li>
                        <li>用户的支付记录</li>
                        <li>所有相关数据</li>
                    </ul>
                </div>
                <p>确定要删除用户 <strong id="deleteUserName"></strong> 吗？</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-danger" id="confirmDeleteBtn">
                    <i class="fas fa-trash me-2"></i>确认删除
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let currentPage = 1;
let currentFilters = {};

$(document).ready(function() {
    loadUsers();

    // 搜索框回车事件
    $('#searchInput').on('keypress', function(e) {
        if (e.which === 13) {
            searchUsers();
        }
    });

    // 筛选条件变化时自动搜索
    $('#vipFilter, #sortBy, #sortOrder').on('change', function() {
        searchUsers();
    });

    // 删除确认按钮事件
    $('#confirmDeleteBtn').on('click', function() {
        if (deleteUserId) {
            deleteUser(deleteUserId);
        }
    });
});

function loadUsers(page = 1) {
    showLoading(true);

    const params = {
        page: page,
        per_page: 20,
        search: $('#searchInput').val().trim(),
        vip_filter: $('#vipFilter').val(),
        sort_by: $('#sortBy').val(),
        sort_order: $('#sortOrder').val()
    };

    currentFilters = params;
    currentPage = page;

    $.get('/admin/api/users', params)
        .done(function(data) {
            renderUsersTable(data.users);
            renderPagination(data.pagination);
            updateTotalCount(data.pagination.total);
        })
        .fail(function(xhr) {
            showError('加载用户数据失败: ' + (xhr.responseJSON?.error || '未知错误'));
        })
        .always(function() {
            showLoading(false);
        });
}

function renderUsersTable(users) {
    const tbody = $('#usersTableBody');
    tbody.empty();

    if (users.length === 0) {
        tbody.append(`
            <tr>
                <td colspan="10" class="text-center py-4">
                    <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                    <p class="text-muted">暂无用户数据</p>
                </td>
            </tr>
        `);
        return;
    }

    users.forEach(function(user) {
        const vipBadge = user.is_vip ?
            `<span class="badge vip-badge"><i class="fas fa-crown me-1"></i>VIP</span>` :
            `<span class="badge bg-secondary">普通</span>`;

        const avatar = user.avatar_url ?
            `<img src="${user.avatar_url}" class="avatar-img" alt="头像">` :
            `<div class="avatar-img bg-secondary d-flex align-items-center justify-content-center">
                <i class="fas fa-user text-white"></i>
             </div>`;

        const appVersion = user.app_version ?
            `<span class="badge bg-primary">v${user.app_version}</span>` :
            `<span class="text-muted small">-</span>`;

        const row = `
            <tr>
                <td><strong>${user.id}</strong></td>
                <td>${avatar}</td>
                <td>${user.nickname}</td>
                <td><code class="small">${user.openid.substring(0, 16)}...</code></td>
                <td>${vipBadge}</td>
                <td>${appVersion}</td>
                <td><small>${user.created_at}</small></td>
                <td><small>${user.updated_at}</small></td>
                <td>
                    <div class="btn-group" role="group">
                        <button class="btn btn-sm btn-outline-primary" onclick="showUserDetail(${user.id})" title="查看详情">
                            <i class="fas fa-eye"></i> 详情
                        </button>
                        <button class="btn btn-sm btn-outline-danger" onclick="confirmDeleteUser(${user.id}, '${user.nickname || user.openid}')" title="删除用户">
                            <i class="fas fa-trash"></i> 删除
                        </button>
                    </div>
                </td>
            </tr>
        `;
        tbody.append(row);
    });
}

function renderPagination(pagination) {
    const nav = $('#pagination');
    nav.empty();

    if (pagination.total_pages <= 1) {
        return;
    }

    // 上一页
    const prevDisabled = !pagination.has_prev ? 'disabled' : '';
    nav.append(`
        <li class="page-item ${prevDisabled}">
            <a class="page-link" href="#" onclick="loadUsers(${pagination.prev_page || 1})">
                <i class="fas fa-chevron-left"></i>
            </a>
        </li>
    `);

    // 页码
    const startPage = Math.max(1, pagination.page - 2);
    const endPage = Math.min(pagination.total_pages, pagination.page + 2);

    if (startPage > 1) {
        nav.append(`<li class="page-item"><a class="page-link" href="#" onclick="loadUsers(1)">1</a></li>`);
        if (startPage > 2) {
            nav.append(`<li class="page-item disabled"><span class="page-link">...</span></li>`);
        }
    }

    for (let i = startPage; i <= endPage; i++) {
        const active = i === pagination.page ? 'active' : '';
        nav.append(`
            <li class="page-item ${active}">
                <a class="page-link" href="#" onclick="loadUsers(${i})">${i}</a>
            </li>
        `);
    }

    if (endPage < pagination.total_pages) {
        if (endPage < pagination.total_pages - 1) {
            nav.append(`<li class="page-item disabled"><span class="page-link">...</span></li>`);
        }
        nav.append(`
            <li class="page-item">
                <a class="page-link" href="#" onclick="loadUsers(${pagination.total_pages})">${pagination.total_pages}</a>
            </li>
        `);
    }

    // 下一页
    const nextDisabled = !pagination.has_next ? 'disabled' : '';
    nav.append(`
        <li class="page-item ${nextDisabled}">
            <a class="page-link" href="#" onclick="loadUsers(${pagination.next_page || pagination.total_pages})">
                <i class="fas fa-chevron-right"></i>
            </a>
        </li>
    `);
}

function updateTotalCount(total) {
    $('#totalCount').text(total.toLocaleString() + ' 用户');
}

function searchUsers() {
    loadUsers(1);
}

function refreshUsers() {
    loadUsers(currentPage);
    showToast('数据已刷新', 'success');
}

function showUserDetail(userId) {
    $.get(`/admin/api/users/${userId}`)
        .done(function(user) {
            const content = `
                <div class="row">
                    <!-- 用户基本信息 -->
                    <div class="col-md-4 text-center">
                        ${user.avatar_url ?
                            `<img src="${user.avatar_url}" class="img-fluid rounded-circle mb-3" style="max-width: 150px;">` :
                            `<div class="bg-secondary rounded-circle mx-auto mb-3 d-flex align-items-center justify-content-center" style="width: 150px; height: 150px;">
                                <i class="fas fa-user fa-4x text-white"></i>
                             </div>`
                        }
                        <h5>${user.nickname || '未设置昵称'}</h5>
                        ${user.is_vip ?
                            `<span class="badge vip-badge fs-6"><i class="fas fa-crown me-1"></i>VIP用户</span>` :
                            `<span class="badge bg-secondary fs-6">普通用户</span>`
                        }
                        <div class="mt-3">
                            <small class="text-muted">用户ID: ${user.id}</small>
                        </div>
                    </div>

                    <!-- 用户详细信息 -->
                    <div class="col-md-8">
                        <ul class="nav nav-tabs" id="userDetailTabs" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="basic-tab" data-bs-toggle="tab" data-bs-target="#basic" type="button" role="tab">
                                    <i class="fas fa-user me-1"></i>基本信息
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="device-tab" data-bs-toggle="tab" data-bs-target="#device" type="button" role="tab">
                                    <i class="fas fa-mobile-alt me-1"></i>设备信息
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="subscription-tab" data-bs-toggle="tab" data-bs-target="#subscription" type="button" role="tab">
                                    <i class="fas fa-crown me-1"></i>订阅信息
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="orders-tab" data-bs-toggle="tab" data-bs-target="#orders" type="button" role="tab">
                                    <i class="fas fa-shopping-cart me-1"></i>订单记录
                                </button>
                            </li>
                        </ul>

                        <div class="tab-content mt-3" id="userDetailTabContent">
                            <!-- 基本信息标签页 -->
                            <div class="tab-pane fade show active" id="basic" role="tabpanel">
                                <table class="table table-borderless">
                                    <tr><td><strong>OpenID:</strong></td><td><code class="small">${user.openid}</code></td></tr>
                                    <tr><td><strong>UnionID:</strong></td><td><code class="small">${user.unionid || '未绑定'}</code></td></tr>
                                    <tr><td><strong>昵称:</strong></td><td>${user.nickname || '未设置'}</td></tr>
                                    <tr><td><strong>注册时间:</strong></td><td>${user.created_at}</td></tr>
                                    <tr><td><strong>最后更新:</strong></td><td>${user.updated_at}</td></tr>
                                    <tr><td><strong>总订单数:</strong></td><td>${user.total_orders || 0}</td></tr>
                                    <tr><td><strong>已付款订单:</strong></td><td>${user.paid_orders || 0}</td></tr>
                                </table>
                            </div>

                            <!-- 设备信息标签页 -->
                            <div class="tab-pane fade" id="device" role="tabpanel">
                                ${user.has_devices ? `
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <h6 class="mb-0">
                                            <i class="fas fa-mobile-alt me-2"></i>
                                            登录设备历史
                                            <span class="badge bg-primary ms-2">${user.device_count} 台设备</span>
                                        </h6>
                                        ${user.current_device_id ? `
                                            <small class="text-muted">
                                                当前设备: <code>${user.current_device_id.substring(0, 12)}...</code>
                                            </small>
                                        ` : ''}
                                    </div>
                                    
                                    <div class="table-responsive">
                                        <table class="table table-hover">
                                            <thead class="table-light">
                                                <tr>
                                                    <th>设备ID</th>
                                                    <th>设备信息</th>
                                                    <th>APP版本</th>
                                                    <th>首次登录</th>
                                                    <th>最后登录</th>
                                                    <th>登录次数</th>
                                                    <th>状态</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                ${user.devices.map(device => `
                                                    <tr class="${device.is_active ? 'table-success' : ''}">
                                                        <td>
                                                            <code class="small user-select-all">${device.device_id}</code>
                                                            <button class="btn btn-sm btn-link p-0 ms-1" onclick="copyToClipboard('${device.device_id}')" title="复制">
                                                                <i class="fas fa-copy"></i>
                                                            </button>
                                                        </td>
                                                        <td>
                                                            ${device.device_name || device.device_model || device.os_version ? `
                                                                <div>
                                                                    ${device.device_name ? `<div><strong>${device.device_name}</strong></div>` : ''}
                                                                    ${device.device_model ? `<div class="small text-muted">${device.device_model}</div>` : ''}
                                                                    ${device.os_version ? `<div class="small text-muted">系统: ${device.os_version}</div>` : ''}
                                                                </div>
                                                            ` : '<span class="text-muted">未知设备</span>'}
                                                        </td>
                                                        <td>
                                                            ${device.app_version ? 
                                                                `<span class="badge bg-primary">v${device.app_version}</span>` : 
                                                                '<span class="text-muted">-</span>'
                                                            }
                                                        </td>
                                                        <td><small>${device.first_login_at}</small></td>
                                                        <td><small>${device.last_login_at}</small></td>
                                                        <td>
                                                            <span class="badge bg-info">${device.login_count}</span>
                                                        </td>
                                                        <td>
                                                            ${device.is_active ? 
                                                                '<span class="badge bg-success"><i class="fas fa-check-circle me-1"></i>活跃</span>' :
                                                                '<span class="badge bg-secondary"><i class="fas fa-history me-1"></i>历史</span>'
                                                            }
                                                        </td>
                                                    </tr>
                                                `).join('')}
                                            </tbody>
                                        </table>
                                    </div>
                                    
                                    <div class="alert alert-info mt-3 mb-0">
                                        <i class="fas fa-info-circle me-2"></i>
                                        <strong>说明：</strong>
                                        <ul class="mb-0 mt-2">
                                            <li><strong>活跃设备</strong>：用户当前正在使用的设备（绿色高亮）</li>
                                            <li><strong>历史设备</strong>：用户曾经登录过但已不再活跃的设备</li>
                                            <li>VIP用户只能在一个设备上使用会员功能</li>
                                            <li>用户在新设备登录时，旧设备会自动变为历史设备</li>
                                            <li>设备信息会在每次登录时自动更新</li>
                                        </ul>
                                    </div>
                                ` : `
                                    <div class="text-center py-5">
                                        <i class="fas fa-mobile-alt fa-4x text-muted mb-3"></i>
                                        <h5 class="text-muted">暂无设备记录</h5>
                                        <p class="text-muted mb-0">
                                            用户首次登录或更新到新版本客户端后，设备信息将自动记录
                                        </p>
                                    </div>
                                `}
                            </div>

                            <!-- 订阅信息标签页 -->
                            <div class="tab-pane fade" id="subscription" role="tabpanel">
                                ${user.subscription_info ? `
                                    <div class="card">
                                        <div class="card-body">
                                            <h6 class="card-title">
                                                <i class="fas fa-crown me-2"></i>
                                                ${user.subscription_info.plan_name}
                                                <span class="badge ${user.subscription_info.is_active ? 'bg-success' : 'bg-secondary'} ms-2">
                                                    ${user.subscription_info.status}
                                                </span>
                                            </h6>
                                            <table class="table table-sm">
                                                <tr><td><strong>套餐代码:</strong></td><td><code>${user.subscription_info.plan_code}</code></td></tr>
                                                <tr><td><strong>套餐价格:</strong></td><td>¥${user.subscription_info.plan_price_yuan}</td></tr>
                                                <tr><td><strong>开始时间:</strong></td><td>${user.subscription_info.start_at}</td></tr>
                                                <tr><td><strong>到期时间:</strong></td><td>${user.subscription_info.end_at || '永久'}</td></tr>
                                                <tr><td><strong>剩余天数:</strong></td><td>
                                                    ${user.subscription_info.days_remaining >= 0 ?
                                                        `<span class="badge bg-info">${user.subscription_info.days_remaining}天</span>` :
                                                        `<span class="badge bg-danger">已过期</span>`
                                                    }
                                                </td></tr>
                                                <tr><td><strong>创建时间:</strong></td><td>${user.subscription_info.created_at}</td></tr>
                                                <tr><td><strong>更新时间:</strong></td><td>${user.subscription_info.updated_at}</td></tr>
                                            </table>
                                        </div>
                                    </div>
                                ` : `
                                    <div class="text-center py-4">
                                        <i class="fas fa-crown fa-3x text-muted mb-3"></i>
                                        <p class="text-muted">该用户暂无订阅信息</p>
                                    </div>
                                `}
                            </div>

                            <!-- 订单记录标签页 -->
                            <div class="tab-pane fade" id="orders" role="tabpanel">
                                ${user.recent_orders && user.recent_orders.length > 0 ? `
                                    <div class="table-responsive">
                                        <table class="table table-sm">
                                            <thead>
                                                <tr>
                                                    <th>订单号</th>
                                                    <th>状态</th>
                                                    <th>金额</th>
                                                    <th>创建时间</th>
                                                    <th>支付时间</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                ${user.recent_orders.map(order => `
                                                    <tr>
                                                        <td><code class="small">${order.out_trade_no}</code></td>
                                                        <td>
                                                            <span class="badge ${getOrderStatusBadge(order.status)}">
                                                                ${order.status}
                                                            </span>
                                                        </td>
                                                        <td>¥${order.total_yuan}</td>
                                                        <td><small>${order.created_at}</small></td>
                                                        <td><small>${order.paid_at || '-'}</small></td>
                                                    </tr>
                                                `).join('')}
                                            </tbody>
                                        </table>
                                    </div>
                                    ${user.total_orders > 10 ? `
                                        <div class="text-center mt-3">
                                            <small class="text-muted">仅显示最近10条订单，共${user.total_orders}条</small>
                                        </div>
                                    ` : ''}
                                ` : `
                                    <div class="text-center py-4">
                                        <i class="fas fa-shopping-cart fa-3x text-muted mb-3"></i>
                                        <p class="text-muted">该用户暂无订单记录</p>
                                    </div>
                                `}
                            </div>
                        </div>
                    </div>
                </div>
            `;

            $('#userDetailContent').html(content);
            $('#userDetailModal').modal('show');
        })
        .fail(function() {
            showError('加载用户详情失败');
        });
}

function exportUsers() {
    showToast('导出功能开发中...', 'info');
}

function getOrderStatusBadge(status) {
    switch(status) {
        case 'PAID': return 'bg-success';
        case 'PENDING': return 'bg-warning';
        case 'CLOSED': return 'bg-secondary';
        case 'REFUNDED': return 'bg-danger';
        default: return 'bg-secondary';
    }
}

let deleteUserId = null;

function confirmDeleteUser(userId, userName) {
    deleteUserId = userId;
    $('#deleteUserName').text(userName);
    $('#deleteConfirmModal').modal('show');
}

function deleteUser(userId) {
    if (!userId) return;

    $.ajax({
        url: `/admin/api/users/${userId}`,
        type: 'DELETE',
        beforeSend: function() {
            $('#confirmDeleteBtn').prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-2"></i>删除中...');
        }
    })
    .done(function(response) {
        showToast('用户删除成功', 'success');
        $('#deleteConfirmModal').modal('hide');
        loadUsers(currentPage); // 重新加载当前页
    })
    .fail(function(xhr) {
        const error = xhr.responseJSON?.error || '删除失败';
        showError('删除用户失败: ' + error);
    })
    .always(function() {
        $('#confirmDeleteBtn').prop('disabled', false).html('<i class="fas fa-trash me-2"></i>确认删除');
        deleteUserId = null;
    });
}

function showLoading(show) {
    if (show) {
        $('#loadingIndicator').show();
        $('#usersTable').hide();
    } else {
        $('#loadingIndicator').hide();
        $('#usersTable').show();
    }
}

function showError(message) {
    showToast(message, 'error');
}

function showToast(message, type = 'info') {
    const alertClass = type === 'success' ? 'alert-success' :
                      type === 'error' ? 'alert-danger' : 'alert-info';

    const toast = $(`
        <div class="alert ${alertClass} alert-dismissible fade show position-fixed"
             style="top: 20px; right: 20px; z-index: 9999;">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `);

    $('body').append(toast);

    setTimeout(function() {
        toast.alert('close');
    }, 3000);
}

function copyToClipboard(text) {
    // 创建临时文本区域
    const textarea = document.createElement('textarea');
    textarea.value = text;
    textarea.style.position = 'fixed';
    textarea.style.opacity = '0';
    document.body.appendChild(textarea);
    
    // 选择并复制
    textarea.select();
    try {
        document.execCommand('copy');
        showToast('设备ID已复制到剪贴板', 'success');
    } catch (err) {
        showToast('复制失败，请手动复制', 'error');
    }
    
    // 清理
    document.body.removeChild(textarea);
}
</script>
{% endblock %}
