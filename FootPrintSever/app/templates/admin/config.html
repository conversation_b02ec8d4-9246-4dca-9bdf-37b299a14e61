{% extends "admin/base.html" %}

{% block title %}应用设置 - FootPrint 管理后台{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-cog me-2"></i>
        应用设置
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="window.location.reload()">
                <i class="fas fa-sync-alt"></i> 刷新
            </button>
        </div>
    </div>
</div>

<!-- 提示消息 -->
{% if message %}
<div class="alert alert-{{ 'success' if message_type == 'success' else 'danger' }} alert-dismissible fade show" role="alert">
    <i class="fas fa-{{ 'check-circle' if message_type == 'success' else 'exclamation-circle' }} me-2"></i>
    {{ message }}
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
{% endif %}

<!-- 版本控制说明 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header bg-info text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    版本控制说明
                </h5>
            </div>
            <div class="card-body">
                <ul class="mb-0">
                    <li class="mb-2">
                        <strong>最新版本号：</strong>用户当前版本低于此值时，会提示可选更新（可取消）
                    </li>
                    <li class="mb-2">
                        <strong>最低可用版本号：</strong>用户当前版本低于此值时，会强制要求更新
                    </li>
                    <li class="mb-2">
                        <strong>下载地址：</strong>用户点击"前去更新"时打开的链接
                    </li>
                    <li class="mb-0">
                        <strong>版本号格式：</strong>使用 versionCode（整数），例如：1, 2, 3...
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>

<!-- 设置表单 -->
<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-mobile-alt me-2"></i>
                    Android 应用版本配置
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ url_for('admin.settings') }}" id="settingsForm">
                    <div class="mb-4">
                        <label for="latest_version_code" class="form-label">
                            <i class="fas fa-arrow-up me-1"></i>
                            最新版本号 (Latest Version Code)
                        </label>
                        <input 
                            type="number" 
                            class="form-control" 
                            id="latest_version_code" 
                            name="latest_version_code" 
                            value="{{ settings.latest_version_code }}"
                            min="1"
                            required
                        >
                        <div class="form-text">当前最新的应用版本号</div>
                    </div>
                    
                    <div class="mb-4">
                        <label for="min_version_code" class="form-label">
                            <i class="fas fa-exclamation-triangle me-1"></i>
                            最低可用版本号 (Minimum Version Code)
                        </label>
                        <input 
                            type="number" 
                            class="form-control" 
                            id="min_version_code" 
                            name="min_version_code" 
                            value="{{ settings.min_version_code }}"
                            min="1"
                            required
                        >
                        <div class="form-text">低于此版本的用户将被强制更新</div>
                    </div>
                    
                    <div class="mb-4">
                        <label for="download_url" class="form-label">
                            <i class="fas fa-download me-1"></i>
                            下载地址 (Download URL)
                        </label>
                        <input 
                            type="text" 
                            class="form-control" 
                            id="download_url" 
                            name="download_url" 
                            value="{{ settings.download_url }}"
                            placeholder="https://example.com/footprint.apk"
                            required
                        >
                        <div class="form-text">新版本 APK 的下载链接</div>
                    </div>
                    
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary btn-custom">
                            <i class="fas fa-save me-2"></i>
                            保存设置
                        </button>
                        <button type="button" class="btn btn-secondary btn-custom" onclick="window.location.href='{{ url_for('admin.index') }}'">
                            <i class="fas fa-times me-2"></i>
                            取消
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- 当前配置信息 -->
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-clipboard-list me-2"></i>
                    当前配置
                </h5>
            </div>
            <div class="card-body">
                <table class="table table-borderless mb-0">
                    <tr>
                        <td class="text-muted">最新版本：</td>
                        <td class="text-end">
                            <strong class="text-primary">{{ settings.latest_version_code }}</strong>
                        </td>
                    </tr>
                    <tr>
                        <td class="text-muted">最低版本：</td>
                        <td class="text-end">
                            <strong class="text-warning">{{ settings.min_version_code }}</strong>
                        </td>
                    </tr>
                    <tr>
                        <td class="text-muted">下载地址：</td>
                        <td class="text-end">
                            {% if settings.download_url %}
                            <a href="{{ settings.download_url }}" target="_blank" class="text-decoration-none">
                                <i class="fas fa-external-link-alt"></i>
                            </a>
                            {% else %}
                            <span class="text-muted">未设置</span>
                            {% endif %}
                        </td>
                    </tr>
                    <tr>
                        <td class="text-muted">最后更新：</td>
                        <td class="text-end">
                            <small>{{ settings.updated_at.strftime('%Y-%m-%d %H:%M') if settings.updated_at else '-' }}</small>
                        </td>
                    </tr>
                </table>
            </div>
        </div>
        
        <!-- 版本检查逻辑说明 -->
        <div class="card mt-3">
            <div class="card-header bg-light">
                <h6 class="card-title mb-0">
                    <i class="fas fa-lightbulb me-2"></i>
                    更新逻辑
                </h6>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <div class="badge bg-success mb-2">无需更新</div>
                    <p class="small text-muted mb-0">
                        用户版本 ≥ 最新版本
                    </p>
                </div>
                <div class="mb-3">
                    <div class="badge bg-warning text-dark mb-2">可选更新</div>
                    <p class="small text-muted mb-0">
                        最低版本 ≤ 用户版本 &lt; 最新版本
                    </p>
                </div>
                <div>
                    <div class="badge bg-danger mb-2">强制更新</div>
                    <p class="small text-muted mb-0">
                        用户版本 &lt; 最低版本
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // 表单验证
    $('#settingsForm').on('submit', function(e) {
        const latestVersion = parseInt($('#latest_version_code').val());
        const minVersion = parseInt($('#min_version_code').val());
        
        if (minVersion > latestVersion) {
            e.preventDefault();
            alert('错误：最低可用版本号不能大于最新版本号！');
            return false;
        }
        
        const downloadUrl = $('#download_url').val().trim();
        if (!downloadUrl.startsWith('http://') && !downloadUrl.startsWith('https://')) {
            e.preventDefault();
            alert('错误：下载地址必须以 http:// 或 https:// 开头！');
            return false;
        }
    });
});
</script>
{% endblock %}
