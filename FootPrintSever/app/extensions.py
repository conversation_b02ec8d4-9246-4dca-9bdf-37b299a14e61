"""
Flask extensions initialization
"""
from flask_sqlalchemy import SQLAlchemy
from flask_migrate import Migrate
from flask_jwt_extended import <PERSON><PERSON><PERSON><PERSON><PERSON>
from flask_limiter import Limiter
from flask_limiter.util import get_remote_address
from flask_apscheduler import APScheduler
import redis

# Initialize extensions
db = SQLAlchemy()
migrate = Migrate()
jwt = JWTManager()
scheduler = APScheduler()

# Rate limiter - will be configured with Redis backend in init_redis
limiter = Limiter(
    key_func=get_remote_address,
    default_limits=["200 per day", "50 per hour"]
)

# Redis client (will be initialized in app factory if needed)
redis_client = None


def init_redis(app):
    """Initialize Redis client and configure Flask-Limiter"""
    global redis_client
    redis_url = app.config.get('REDIS_URL')

    if redis_url:
        try:
            redis_client = redis.from_url(redis_url)
            # Test connection
            redis_client.ping()
            app.logger.info("Redis connection established")

            # Configure Flask-Limiter to use Redis
            app.config['RATELIMIT_STORAGE_URL'] = redis_url

        except Exception as e:
            app.logger.warning(f"Redis connection failed: {e}")
            redis_client = None

            # Fall back to in-memory storage for Flask-Limiter
            app.config.pop('RATELIMIT_STORAGE_URL', None)
            app.logger.info("Flask-Limiter falling back to in-memory storage")
    else:
        app.logger.info("Redis URL not configured, using in-memory storage for rate limiting")
        redis_client = None

    return redis_client
