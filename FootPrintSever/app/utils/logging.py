"""
Logging configuration and utilities
"""
import os
import sys
import logging
from logging.handlers import TimedRotatingFileHandler
import structlog
from flask import request, g
import json
from datetime import datetime

# Import configuration
try:
    from config.logging_config import get_logging_config
except ImportError:
    # Fallback if config module is not available
    class DefaultConfig:
        LOG_LEVEL = 'INFO'
        LOG_DIR = 'logs'
        APP_NAME = 'footprint_subscription'
        APP_VERSION = '1.0.0'
        SENSITIVE_KEYS = ['password', 'secret', 'token', 'key']

    def get_logging_config():
        return DefaultConfig()


class DailyRotatingStructlogHandler(TimedRotatingFileHandler):
    """
    Custom handler for structlog with daily rotation and JSON formatting
    """

    def __init__(self, filename, when='midnight', interval=1, backupCount=30, encoding='utf-8'):
        super().__init__(filename, when, interval, backupCount, encoding=encoding)
        self.suffix = '%Y-%m-%d'

    def format(self, record):
        """Format log record as JSON if it contains structured data"""
        if hasattr(record, 'msg') and isinstance(record.msg, dict):
            # This is a structured log from structlog
            return json.dumps(record.msg, ensure_ascii=False, default=str)
        else:
            # Regular log message
            return super().format(record)


def configure_logging(app):
    """
    Configure structured logging for the application

    Args:
        app: Flask application instance
    """
    # Get logging configuration
    config = get_logging_config()

    # Configure standard logging
    log_level = app.config.get('LOG_LEVEL', config.LOG_LEVEL)

    # Create logs directory if it doesn't exist
    if not os.path.exists(config.LOG_DIR):
        os.makedirs(config.LOG_DIR)

    # Configure log handlers using configuration
    handlers = {}

    for log_type, log_config in config.LOG_FILES.items():
        handler = DailyRotatingStructlogHandler(
            log_config['filename'],
            when=config.ROTATION_WHEN,
            interval=config.ROTATION_INTERVAL,
            backupCount=log_config['backup_count'],
            encoding=config.ENCODING
        )
        handler.setFormatter(logging.Formatter(
            '%(asctime)s %(levelname)s: %(message)s [in %(pathname)s:%(lineno)d]'
        ))
        handler.setLevel(getattr(logging, log_config['level']))
        handlers[log_type] = handler
    
    # Configure console handler if enabled
    console_handler = None
    if config.CONSOLE_LOGGING['enabled']:
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setFormatter(logging.Formatter(
            config.CONSOLE_LOGGING['format']
        ))
        console_handler.setLevel(getattr(logging, config.CONSOLE_LOGGING['level']))

    # Configure root logger
    app.logger.setLevel(getattr(logging, log_level))
    app.logger.addHandler(handlers['app'])
    app.logger.addHandler(handlers['errors'])
    if console_handler:
        app.logger.addHandler(console_handler)

    # Configure specific loggers for different log types
    for log_type in ['api', 'security', 'payment', 'audit']:
        if log_type in handlers:
            logger = logging.getLogger(log_type)
            logger.setLevel(getattr(logging, config.LOG_FILES[log_type]['level']))
            logger.addHandler(handlers[log_type])
            if log_type != 'api':  # API logs don't need error handler to avoid duplication
                logger.addHandler(handlers['errors'])
            if console_handler:
                logger.addHandler(console_handler)
    
    # Configure structlog with enhanced processors
    structlog.configure(
        processors=[
            structlog.stdlib.filter_by_level,
            structlog.stdlib.add_logger_name,
            structlog.stdlib.add_log_level,
            structlog.stdlib.PositionalArgumentsFormatter(),
            add_request_context,
            add_log_metadata,
            structlog.processors.TimeStamper(fmt="iso"),
            structlog.processors.StackInfoRenderer(),
            structlog.processors.format_exc_info,
            structlog.processors.UnicodeDecoder(),
            structlog.processors.JSONRenderer()
        ],
        context_class=dict,
        logger_factory=structlog.stdlib.LoggerFactory(),
        wrapper_class=structlog.stdlib.BoundLogger,
        cache_logger_on_first_use=True,
    )


def add_log_metadata(logger, method_name, event_dict):
    """
    Add metadata to log entries for better organization

    Args:
        logger: Logger instance
        method_name: Log method name
        event_dict: Event dictionary

    Returns:
        dict: Updated event dictionary with metadata
    """
    # Add timestamp in multiple formats for easier parsing
    now = datetime.now()
    event_dict['date'] = now.strftime('%Y-%m-%d')
    event_dict['time'] = now.strftime('%H:%M:%S')
    event_dict['timestamp_unix'] = int(now.timestamp())

    # Add log level for easier filtering
    event_dict['log_level'] = method_name.upper()

    # Add application metadata from config
    config = get_logging_config()
    event_dict['application'] = config.APP_NAME
    event_dict['version'] = config.APP_VERSION

    return event_dict


def add_request_context(logger, method_name, event_dict):
    """
    Add request context to log entries
    
    Args:
        logger: Logger instance
        method_name: Log method name
        event_dict: Event dictionary
        
    Returns:
        dict: Updated event dictionary with request context
    """
    try:
        # Add request context if available
        if request:
            event_dict['request_id'] = getattr(request, 'id', None)
            event_dict['method'] = request.method
            event_dict['path'] = request.path
            event_dict['remote_addr'] = request.remote_addr
            event_dict['user_agent'] = request.headers.get('User-Agent', '')
            
            # Add user context if available
            if hasattr(g, 'current_user_id') and g.current_user_id:
                event_dict['user_id'] = g.current_user_id
    except RuntimeError:
        # Outside request context
        pass
    
    return event_dict


def mask_sensitive_data(data, sensitive_keys=None):
    """
    Mask sensitive data in logs

    Args:
        data: Data to mask (dict, list, or string)
        sensitive_keys: List of sensitive keys to mask

    Returns:
        Masked data
    """
    if sensitive_keys is None:
        config = get_logging_config()
        sensitive_keys = config.SENSITIVE_KEYS
    
    if isinstance(data, dict):
        masked_data = {}
        for key, value in data.items():
            if any(sensitive_key in key.lower() for sensitive_key in sensitive_keys):
                masked_data[key] = '***MASKED***'
            else:
                masked_data[key] = mask_sensitive_data(value, sensitive_keys)
        return masked_data
    elif isinstance(data, list):
        return [mask_sensitive_data(item, sensitive_keys) for item in data]
    elif isinstance(data, str) and len(data) > 20:
        # Mask long strings that might contain sensitive data
        return f"{data[:4]}***MASKED***{data[-4:]}"
    else:
        return data


def log_api_request(endpoint, method, user_id=None, **kwargs):
    """
    Log API request with context

    Args:
        endpoint: API endpoint
        method: HTTP method
        user_id: User ID (optional)
        **kwargs: Additional context
    """
    logger = structlog.get_logger('api')

    context = {
        'event_type': 'api_request',
        'endpoint': endpoint,
        'method': method,
        'user_id': user_id,
        **kwargs
    }

    # Mask sensitive data
    context = mask_sensitive_data(context)

    logger.info("API request", **context)


def log_api_response(endpoint, method, status_code, user_id=None, **kwargs):
    """
    Log API response with context

    Args:
        endpoint: API endpoint
        method: HTTP method
        status_code: HTTP status code
        user_id: User ID (optional)
        **kwargs: Additional context
    """
    logger = structlog.get_logger('api')

    context = {
        'event_type': 'api_response',
        'endpoint': endpoint,
        'method': method,
        'status_code': status_code,
        'user_id': user_id,
        **kwargs
    }

    # Mask sensitive data
    context = mask_sensitive_data(context)

    if status_code >= 400:
        logger.warning("API response", **context)
    else:
        logger.info("API response", **context)


def log_payment_event(event_type, order_id=None, transaction_id=None, amount=None, **kwargs):
    """
    Log payment-related events

    Args:
        event_type: Type of payment event
        order_id: Order ID (optional)
        transaction_id: Transaction ID (optional)
        amount: Payment amount (optional)
        **kwargs: Additional context
    """
    logger = structlog.get_logger('payment')

    context = {
        'event_type': 'payment_event',
        'payment_event_type': event_type,
        'order_id': order_id,
        'transaction_id': transaction_id,
        'amount': amount,
        **kwargs
    }

    # Mask sensitive data
    context = mask_sensitive_data(context)

    logger.info("Payment event", **context)


def log_subscription_event(event_type, subscription_id=None, user_id=None, **kwargs):
    """
    Log subscription-related events
    
    Args:
        event_type: Type of subscription event
        subscription_id: Subscription ID (optional)
        user_id: User ID (optional)
        **kwargs: Additional context
    """
    logger = structlog.get_logger()
    
    context = {
        'event_type': 'subscription_event',
        'subscription_event_type': event_type,
        'subscription_id': subscription_id,
        'user_id': user_id,
        **kwargs
    }
    
    # Mask sensitive data
    context = mask_sensitive_data(context)
    
    logger.info("Subscription event", **context)


def log_security_event(event_type, severity='medium', **kwargs):
    """
    Log security-related events

    Args:
        event_type: Type of security event
        severity: Event severity (low, medium, high, critical)
        **kwargs: Additional context
    """
    logger = structlog.get_logger('security')

    context = {
        'event_type': 'security_event',
        'security_event_type': event_type,
        'severity': severity,
        **kwargs
    }

    # Mask sensitive data
    context = mask_sensitive_data(context)

    if severity in ['high', 'critical']:
        logger.error("Security event", **context)
    elif severity == 'medium':
        logger.warning("Security event", **context)
    else:
        logger.info("Security event", **context)


def log_audit_event(action, resource_type, resource_id=None, user_id=None, **kwargs):
    """
    Log audit trail events for compliance and tracking

    Args:
        action: Action performed (create, read, update, delete, etc.)
        resource_type: Type of resource (user, subscription, payment, etc.)
        resource_id: ID of the resource (optional)
        user_id: User who performed the action (optional)
        **kwargs: Additional context
    """
    logger = structlog.get_logger('audit')

    context = {
        'event_type': 'audit_event',
        'action': action,
        'resource_type': resource_type,
        'resource_id': resource_id,
        'user_id': user_id,
        **kwargs
    }

    # Mask sensitive data
    context = mask_sensitive_data(context)

    logger.info("Audit event", **context)


def log_performance_event(operation, duration_ms, **kwargs):
    """
    Log performance-related events

    Args:
        operation: Operation name
        duration_ms: Duration in milliseconds
        **kwargs: Additional context
    """
    logger = structlog.get_logger()

    context = {
        'event_type': 'performance_event',
        'operation': operation,
        'duration_ms': duration_ms,
        **kwargs
    }

    # Determine log level based on duration
    config = get_logging_config()
    if hasattr(config, 'PERFORMANCE_LOGGING'):
        threshold_ms = config.PERFORMANCE_LOGGING.get('slow_request_threshold', 1.0) * 1000
        if duration_ms > threshold_ms:
            logger.warning("Slow operation detected", **context)
        else:
            logger.info("Performance event", **context)
    else:
        logger.info("Performance event", **context)


def get_log_files_info():
    """
    Get information about current log files

    Returns:
        dict: Information about log files including sizes and dates
    """
    log_info = {}
    logs_dir = 'logs'

    if os.path.exists(logs_dir):
        for filename in os.listdir(logs_dir):
            if filename.endswith('.log'):
                filepath = os.path.join(logs_dir, filename)
                stat = os.stat(filepath)
                log_info[filename] = {
                    'size_bytes': stat.st_size,
                    'size_mb': round(stat.st_size / (1024 * 1024), 2),
                    'modified': datetime.fromtimestamp(stat.st_mtime).isoformat(),
                    'created': datetime.fromtimestamp(stat.st_ctime).isoformat()
                }

    return log_info


def cleanup_old_logs(days_to_keep=30):
    """
    Clean up log files older than specified days

    Args:
        days_to_keep: Number of days to keep log files (default: 30)
    """
    logs_dir = 'logs'
    if not os.path.exists(logs_dir):
        return

    cutoff_time = datetime.now().timestamp() - (days_to_keep * 24 * 60 * 60)

    for filename in os.listdir(logs_dir):
        if filename.endswith('.log') and '-' in filename:  # Rotated log files
            filepath = os.path.join(logs_dir, filename)
            if os.path.getmtime(filepath) < cutoff_time:
                try:
                    os.remove(filepath)
                    print(f"Removed old log file: {filename}")
                except OSError as e:
                    print(f"Error removing log file {filename}: {e}")


def get_logger_for_module(module_name):
    """
    Get a structured logger for a specific module

    Args:
        module_name: Name of the module

    Returns:
        structlog logger instance
    """
    return structlog.get_logger(module_name)
