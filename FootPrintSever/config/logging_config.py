"""
Logging configuration settings for FootPrint Subscription Service
"""
import os
from datetime import datetime


class LoggingConfig:
    """Configuration class for logging settings"""
    
    # Base logging configuration
    LOG_LEVEL = os.getenv('LOG_LEVEL', 'INFO')
    LOG_DIR = os.getenv('LOG_DIR', 'logs')
    
    # Application metadata
    APP_NAME = 'footprint_subscription'
    APP_VERSION = '1.0.0'
    
    # Log rotation settings
    ROTATION_WHEN = 'midnight'  # When to rotate: 'midnight', 'H', 'D', 'W0'-'W6'
    ROTATION_INTERVAL = 1       # Rotation interval
    ENCODING = 'utf-8'
    
    # Retention policies (in days)
    RETENTION_POLICIES = {
        'app': 30,        # Application logs
        'api': 30,        # API logs
        'errors': 90,     # Error logs
        'security': 365,  # Security logs
        'payment': 180,   # Payment logs
        'audit': 2555     # Audit logs (7 years)
    }
    
    # Log file configurations
    LOG_FILES = {
        'app': {
            'filename': f'{LOG_DIR}/footprint_app.log',
            'backup_count': RETENTION_POLICIES['app'],
            'level': LOG_LEVEL,
            'description': 'Main application logs'
        },
        'api': {
            'filename': f'{LOG_DIR}/footprint_api.log',
            'backup_count': RETENTION_POLICIES['api'],
            'level': LOG_LEVEL,
            'description': 'API request/response logs'
        },
        'errors': {
            'filename': f'{LOG_DIR}/footprint_errors.log',
            'backup_count': RETENTION_POLICIES['errors'],
            'level': 'WARNING',
            'description': 'Error and warning logs'
        },
        'security': {
            'filename': f'{LOG_DIR}/footprint_security.log',
            'backup_count': RETENTION_POLICIES['security'],
            'level': 'INFO',
            'description': 'Security-related logs'
        },
        'payment': {
            'filename': f'{LOG_DIR}/footprint_payment.log',
            'backup_count': RETENTION_POLICIES['payment'],
            'level': 'INFO',
            'description': 'Payment processing logs'
        },
        'audit': {
            'filename': f'{LOG_DIR}/footprint_audit.log',
            'backup_count': RETENTION_POLICIES['audit'],
            'level': 'INFO',
            'description': 'Audit trail logs'
        }
    }
    
    # Sensitive data patterns to mask
    SENSITIVE_KEYS = [
        'password', 'secret', 'token', 'key', 'openid', 'unionid',
        'access_token', 'refresh_token', 'authorization', 'signature',
        'private_key', 'apiv3_key', 'ciphertext', 'nonce', 'credit_card',
        'card_number', 'cvv', 'ssn', 'social_security', 'bank_account'
    ]
    
    # Console logging settings
    CONSOLE_LOGGING = {
        'enabled': os.getenv('CONSOLE_LOGGING', 'true').lower() == 'true',
        'level': os.getenv('CONSOLE_LOG_LEVEL', LOG_LEVEL),
        'format': '%(asctime)s %(levelname)s: %(message)s'
    }
    
    # Structured logging processors
    STRUCTLOG_PROCESSORS = [
        'structlog.stdlib.filter_by_level',
        'structlog.stdlib.add_logger_name',
        'structlog.stdlib.add_log_level',
        'structlog.stdlib.PositionalArgumentsFormatter()',
        'add_request_context',
        'add_log_metadata',
        'structlog.processors.TimeStamper(fmt="iso")',
        'structlog.processors.StackInfoRenderer()',
        'structlog.processors.format_exc_info',
        'structlog.processors.UnicodeDecoder()',
        'structlog.processors.JSONRenderer()'
    ]
    
    # Performance monitoring
    PERFORMANCE_LOGGING = {
        'enabled': os.getenv('PERFORMANCE_LOGGING', 'true').lower() == 'true',
        'slow_request_threshold': float(os.getenv('SLOW_REQUEST_THRESHOLD', '1.0')),  # seconds
        'log_request_body': os.getenv('LOG_REQUEST_BODY', 'false').lower() == 'true',
        'log_response_body': os.getenv('LOG_RESPONSE_BODY', 'false').lower() == 'true'
    }
    
    # Log aggregation settings (for external log services)
    LOG_AGGREGATION = {
        'enabled': os.getenv('LOG_AGGREGATION_ENABLED', 'false').lower() == 'true',
        'service': os.getenv('LOG_AGGREGATION_SERVICE', 'elasticsearch'),  # elasticsearch, splunk, etc.
        'endpoint': os.getenv('LOG_AGGREGATION_ENDPOINT', ''),
        'api_key': os.getenv('LOG_AGGREGATION_API_KEY', ''),
        'index_pattern': os.getenv('LOG_INDEX_PATTERN', 'footprint-logs-%Y.%m.%d')
    }
    
    # Alert settings
    ALERT_SETTINGS = {
        'enabled': os.getenv('LOG_ALERTS_ENABLED', 'false').lower() == 'true',
        'error_threshold': int(os.getenv('ERROR_ALERT_THRESHOLD', '10')),  # errors per hour
        'security_alert_levels': ['high', 'critical'],
        'webhook_url': os.getenv('ALERT_WEBHOOK_URL', ''),
        'email_recipients': os.getenv('ALERT_EMAIL_RECIPIENTS', '').split(',')
    }
    
    @classmethod
    def get_log_file_config(cls, log_type):
        """
        Get configuration for a specific log file type
        
        Args:
            log_type: Type of log file (app, api, errors, security, etc.)
            
        Returns:
            dict: Configuration for the log file
        """
        return cls.LOG_FILES.get(log_type, cls.LOG_FILES['app'])
    
    @classmethod
    def get_retention_days(cls, log_type):
        """
        Get retention period for a specific log type
        
        Args:
            log_type: Type of log file
            
        Returns:
            int: Number of days to retain logs
        """
        return cls.RETENTION_POLICIES.get(log_type, 30)
    
    @classmethod
    def is_sensitive_key(cls, key):
        """
        Check if a key contains sensitive data
        
        Args:
            key: Key name to check
            
        Returns:
            bool: True if key is sensitive
        """
        key_lower = key.lower()
        return any(sensitive_key in key_lower for sensitive_key in cls.SENSITIVE_KEYS)
    
    @classmethod
    def get_current_log_level(cls):
        """
        Get current log level as logging constant
        
        Returns:
            int: Logging level constant
        """
        import logging
        return getattr(logging, cls.LOG_LEVEL.upper(), logging.INFO)


# Environment-specific configurations
class DevelopmentLoggingConfig(LoggingConfig):
    """Development environment logging configuration"""
    LOG_LEVEL = 'DEBUG'
    CONSOLE_LOGGING = {
        'enabled': True,
        'level': 'DEBUG',
        'format': '%(asctime)s %(levelname)s [%(name)s]: %(message)s'
    }
    PERFORMANCE_LOGGING = {
        'enabled': True,
        'slow_request_threshold': 0.5,
        'log_request_body': True,
        'log_response_body': True
    }


class ProductionLoggingConfig(LoggingConfig):
    """Production environment logging configuration"""
    LOG_LEVEL = 'INFO'
    CONSOLE_LOGGING = {
        'enabled': False,
        'level': 'ERROR',
        'format': '%(asctime)s %(levelname)s: %(message)s'
    }
    PERFORMANCE_LOGGING = {
        'enabled': True,
        'slow_request_threshold': 2.0,
        'log_request_body': False,
        'log_response_body': False
    }
    LOG_AGGREGATION = {
        'enabled': True,
        'service': 'elasticsearch',
        'endpoint': os.getenv('ELASTICSEARCH_ENDPOINT', ''),
        'api_key': os.getenv('ELASTICSEARCH_API_KEY', ''),
        'index_pattern': 'footprint-logs-%Y.%m.%d'
    }
    ALERT_SETTINGS = {
        'enabled': True,
        'error_threshold': 5,
        'security_alert_levels': ['medium', 'high', 'critical'],
        'webhook_url': os.getenv('ALERT_WEBHOOK_URL', ''),
        'email_recipients': os.getenv('ALERT_EMAIL_RECIPIENTS', '').split(',')
    }


def get_logging_config():
    """
    Get logging configuration based on environment
    
    Returns:
        LoggingConfig: Configuration class instance
    """
    env = os.getenv('FLASK_ENV', 'development').lower()
    
    if env == 'production':
        return ProductionLoggingConfig()
    elif env == 'development':
        return DevelopmentLoggingConfig()
    else:
        return LoggingConfig()
