"""
Example usage of the enhanced logging system with daily rotation
"""
import sys
import os

# Add the parent directory to the path so we can import from app
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.utils.logging import (
    configure_logging, 
    log_api_request, 
    log_api_response,
    log_payment_event,
    log_subscription_event,
    log_security_event,
    get_log_files_info,
    cleanup_old_logs,
    get_logger_for_module
)
import structlog
from flask import Flask


def demo_logging():
    """Demonstrate the enhanced logging capabilities"""
    
    # Create a Flask app for configuration
    app = Flask(__name__)
    app.config['LOG_LEVEL'] = 'INFO'
    
    # Configure logging
    configure_logging(app)
    
    # Get different types of loggers
    main_logger = structlog.get_logger()
    api_logger = structlog.get_logger('api')
    security_logger = structlog.get_logger('security')
    module_logger = get_logger_for_module('payment_processor')
    
    print("=== Demonstrating Enhanced Logging System ===")
    
    # 1. Basic application logging
    main_logger.info("Application started", version="1.0.0", environment="development")
    main_logger.warning("Configuration warning", missing_config="REDIS_URL")
    
    # 2. API request/response logging
    log_api_request(
        endpoint="/api/v1/subscriptions",
        method="POST",
        user_id="user_123",
        request_size=1024,
        client_ip="*************"
    )
    
    log_api_response(
        endpoint="/api/v1/subscriptions",
        method="POST",
        status_code=201,
        user_id="user_123",
        response_time_ms=150,
        subscription_id="sub_456"
    )
    
    # 3. Payment event logging
    log_payment_event(
        event_type="payment_initiated",
        order_id="order_789",
        transaction_id="txn_abc123",
        amount=29.99,
        currency="USD",
        payment_method="credit_card"
    )
    
    # 4. Subscription event logging
    log_subscription_event(
        event_type="subscription_created",
        subscription_id="sub_456",
        user_id="user_123",
        plan="premium",
        billing_cycle="monthly"
    )
    
    # 5. Security event logging
    log_security_event(
        event_type="failed_login_attempt",
        severity="medium",
        user_id="user_123",
        ip_address="*************",
        attempts=3
    )
    
    log_security_event(
        event_type="suspicious_activity",
        severity="high",
        user_id="user_456",
        activity_type="multiple_payment_failures",
        failure_count=5
    )
    
    # 6. Module-specific logging
    module_logger.info(
        "Payment processing started",
        payment_id="pay_123",
        processor="stripe",
        amount=29.99
    )
    
    # 7. Error logging with exception info
    try:
        raise ValueError("This is a test exception")
    except Exception as e:
        main_logger.error(
            "An error occurred",
            error_type=type(e).__name__,
            error_message=str(e),
            exc_info=True
        )
    
    print("\n=== Log Files Information ===")
    log_info = get_log_files_info()
    for filename, info in log_info.items():
        print(f"{filename}: {info['size_mb']} MB, modified: {info['modified']}")
    
    print("\n=== Log files have been created in the 'logs' directory ===")
    print("- footprint_app.log: Main application logs")
    print("- footprint_api.log: API request/response logs")
    print("- footprint_errors.log: Error and warning logs")
    print("- footprint_security.log: Security-related logs")
    print("\nEach file will be rotated daily at midnight with date suffixes.")


if __name__ == "__main__":
    demo_logging()
