#!/usr/bin/env python3
"""
宝塔面板生产环境启动文件
使用 Gunicorn WSGI 服务器
"""
import os
import sys
from dotenv import load_dotenv

# 确保项目根目录在 Python 路径中
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

# 加载环境变量
load_dotenv()

# 生产环境不加载 .env.local
flask_env = os.environ.get('FLASK_ENV', 'production')

# 清除代理设置
for var in ['http_proxy', 'https_proxy', 'all_proxy', 'HTTP_PROXY', 'HTTPS_PROXY', 'ALL_PROXY']:
    os.environ.pop(var, None)

# 导入应用
from app import create_app
from app.config import config

# 创建应用实例
app = create_app(config[flask_env])

# 宝塔需要的应用变量
application = app

def run_gunicorn():
    """使用 Gunicorn 运行应用"""
    port = int(os.environ.get('PORT', 8080))
    workers = int(os.environ.get('WORKERS', 2))
    
    print(f"🚀 FootPrint 生产服务启动")
    print(f"📍 端口: {port}")
    print(f"👥 工作进程: {workers}")
    print(f"🔧 环境: {flask_env}")
    print("=" * 40)
    
    try:
        import gunicorn.app.wsgiapp as wsgi
        
        # Gunicorn 配置
        sys.argv = [
            'gunicorn',
            '--bind', f'0.0.0.0:{port}',
            '--workers', str(workers),
            '--worker-class', 'sync',
            '--timeout', '120',
            '--keepalive', '5',
            '--max-requests', '1000',
            '--max-requests-jitter', '100',
            '--access-logfile', '-',
            '--error-logfile', '-',
            '--log-level', 'info',
            'bt_production:app'
        ]
        
        wsgi.run()
        
    except ImportError:
        print("❌ Gunicorn 未安装")
        print("请运行: pip install gunicorn")
        print("🔄 回退到 Flask 开发服务器...")
        
        app.run(
            host='0.0.0.0',
            port=port,
            debug=False,
            threaded=True
        )

if __name__ == '__main__':
    run_gunicorn()
