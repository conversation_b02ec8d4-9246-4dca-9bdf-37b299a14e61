#!/usr/bin/env python3
"""
宝塔面板专用启动文件
适用于宝塔 Python 项目管理器
"""
import os
import sys
from dotenv import load_dotenv

# 确保项目根目录在 Python 路径中
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

# 加载环境变量
load_dotenv()

# 只在开发环境加载 .env.local (生产环境安全)
flask_env = os.environ.get('FLASK_ENV', 'development')
if flask_env == 'development':
    load_dotenv('.env.local')

# 清除可能的代理设置
for var in ['http_proxy', 'https_proxy', 'all_proxy', 'HTTP_PROXY', 'HTTPS_PROXY', 'ALL_PROXY']:
    os.environ.pop(var, None)

# 导入应用
from app import create_app
from app.config import config

# 创建应用实例
app = create_app(config[flask_env])

# 应用实例 (宝塔需要这个变量名)
application = app

if __name__ == '__main__':
    # 获取端口 (宝塔会设置 PORT 环境变量)
    port = int(os.environ.get('PORT', 8080))

    print(f"FootPrint 服务启动 - 端口: {port}")
    print(f"环境: {flask_env}")

    # 生产环境尝试使用 Gunicorn
    if flask_env == 'production':
        try:
            import gunicorn.app.wsgiapp as wsgi
            print("使用 Gunicorn 生产服务器")

            # 设置 Gunicorn 参数
            sys.argv = [
                'gunicorn',
                '--bind', f'0.0.0.0:{port}',
                '--workers', '2',
                '--worker-class', 'sync',
                '--timeout', '120',
                '--keepalive', '5',
                '--max-requests', '1000',
                'bt_start:app'
            ]
            wsgi.run()
        except ImportError:
            print("Gunicorn 未安装，使用 Flask 开发服务器")
            app.run(host='0.0.0.0', port=port, debug=False)
    else:
        # 开发环境使用 Flask 开发服务器
        app.run(host='0.0.0.0', port=port, debug=True)
