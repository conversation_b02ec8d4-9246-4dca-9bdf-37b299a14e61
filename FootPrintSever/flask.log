/Users/<USER>/AndroidStudioProjects/FootPrintSever/venv/lib/python3.9/site-packages/flask_limiter/extension.py:336: UserWarning: Using the in-memory storage for tracking rate limits as no storage was explicitly specified. This is not recommended for production use. See: https://flask-limiter.readthedocs.io#configuring-a-storage-backend for documentation about configuring the storage backend.
  warnings.warn(
[2025-11-02 20:19:07,173] WARNING in extensions: Redis connection failed: Error 61 connecting to localhost:6379. Connection refused.
2025-11-02 20:19:07,173 WARNING: Redis connection failed: Error 61 connecting to localhost:6379. Connection refused.
[2025-11-02 20:19:07,188] INFO in subscription_tasks: {"event": "Initializing scheduler tasks", "logger": "app.tasks.subscription_tasks", "level": "info", "timestamp": "2025-11-02T12:19:07.188886Z"}
2025-11-02 20:19:07,188 INFO: {"event": "Initializing scheduler tasks", "logger": "app.tasks.subscription_tasks", "level": "info", "timestamp": "2025-11-02T12:19:07.188886Z"}
[2025-11-02 20:19:07,189] INFO in subscription_tasks: {"event": "Scheduler tasks initialized successfully", "logger": "app.tasks.subscription_tasks", "level": "info", "timestamp": "2025-11-02T12:19:07.188991Z"}
2025-11-02 20:19:07,189 INFO: {"event": "Scheduler tasks initialized successfully", "logger": "app.tasks.subscription_tasks", "level": "info", "timestamp": "2025-11-02T12:19:07.188991Z"}
/Users/<USER>/AndroidStudioProjects/FootPrintSever/venv/lib/python3.9/site-packages/urllib3/__init__.py:35: NotOpenSSLWarning: urllib3 v2 only supports OpenSSL 1.1.1+, currently the 'ssl' module is compiled with 'LibreSSL 2.8.3'. See: https://github.com/urllib3/urllib3/issues/3020
  warnings.warn(
 * Debug mode: on
[31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:8000
 * Running on http://**************:8000
[33mPress CTRL+C to quit[0m
 * Restarting with stat
/Users/<USER>/AndroidStudioProjects/FootPrintSever/venv/lib/python3.9/site-packages/flask_limiter/extension.py:336: UserWarning: Using the in-memory storage for tracking rate limits as no storage was explicitly specified. This is not recommended for production use. See: https://flask-limiter.readthedocs.io#configuring-a-storage-backend for documentation about configuring the storage backend.
  warnings.warn(
[2025-11-02 20:19:07,580] WARNING in extensions: Redis connection failed: Error 61 connecting to localhost:6379. Connection refused.
2025-11-02 20:19:07,580 WARNING: Redis connection failed: Error 61 connecting to localhost:6379. Connection refused.
[2025-11-02 20:19:07,591] INFO in subscription_tasks: {"event": "Initializing scheduler tasks", "logger": "app.tasks.subscription_tasks", "level": "info", "timestamp": "2025-11-02T12:19:07.591207Z"}
2025-11-02 20:19:07,591 INFO: {"event": "Initializing scheduler tasks", "logger": "app.tasks.subscription_tasks", "level": "info", "timestamp": "2025-11-02T12:19:07.591207Z"}
[2025-11-02 20:19:07,591] INFO in subscription_tasks: {"event": "Scheduler tasks initialized successfully", "logger": "app.tasks.subscription_tasks", "level": "info", "timestamp": "2025-11-02T12:19:07.591357Z"}
2025-11-02 20:19:07,591 INFO: {"event": "Scheduler tasks initialized successfully", "logger": "app.tasks.subscription_tasks", "level": "info", "timestamp": "2025-11-02T12:19:07.591357Z"}
/Users/<USER>/AndroidStudioProjects/FootPrintSever/venv/lib/python3.9/site-packages/urllib3/__init__.py:35: NotOpenSSLWarning: urllib3 v2 only supports OpenSSL 1.1.1+, currently the 'ssl' module is compiled with 'LibreSSL 2.8.3'. See: https://github.com/urllib3/urllib3/issues/3020
  warnings.warn(
 * Debugger is active!
 * Debugger PIN: 130-131-193
/Applications/Xcode.app/Contents/Developer/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/resource_tracker.py:216: UserWarning: resource_tracker: There appear to be 1 leaked semaphore objects to clean up at shutdown
  warnings.warn('resource_tracker: There appear to be %d '
