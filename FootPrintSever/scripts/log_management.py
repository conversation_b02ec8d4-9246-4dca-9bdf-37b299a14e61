#!/usr/bin/env python3
"""
Log management script for FootPrint Subscription Service
Provides utilities for monitoring, analyzing, and maintaining log files
"""
import os
import sys
import json
import argparse
from datetime import datetime, timedelta
from collections import defaultdict, Counter

# Add the parent directory to the path so we can import from app
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.utils.logging import get_log_files_info, cleanup_old_logs


def analyze_logs(log_file_path, hours=24):
    """
    Analyze log file for patterns and statistics
    
    Args:
        log_file_path: Path to the log file
        hours: Number of hours to analyze (default: 24)
    """
    if not os.path.exists(log_file_path):
        print(f"Log file not found: {log_file_path}")
        return
    
    cutoff_time = datetime.now() - timedelta(hours=hours)
    
    stats = {
        'total_entries': 0,
        'by_level': Counter(),
        'by_event_type': Counter(),
        'by_hour': defaultdict(int),
        'errors': [],
        'security_events': [],
        'api_endpoints': Counter(),
        'users': set()
    }
    
    print(f"Analyzing {log_file_path} for the last {hours} hours...")
    
    try:
        with open(log_file_path, 'r', encoding='utf-8') as f:
            for line in f:
                try:
                    log_entry = json.loads(line.strip())
                    
                    # Parse timestamp
                    if 'timestamp' in log_entry:
                        timestamp_str = log_entry['timestamp'].replace('Z', '+00:00')
                        timestamp = datetime.fromisoformat(timestamp_str)
                        # Convert to naive datetime for comparison
                        timestamp_naive = timestamp.replace(tzinfo=None)
                        if timestamp_naive < cutoff_time:
                            continue
                    
                    stats['total_entries'] += 1
                    
                    # Count by log level
                    if 'log_level' in log_entry:
                        stats['by_level'][log_entry['log_level']] += 1
                    
                    # Count by event type
                    if 'event_type' in log_entry:
                        stats['by_event_type'][log_entry['event_type']] += 1
                    
                    # Count by hour
                    if 'timestamp' in log_entry:
                        hour = timestamp_naive.strftime('%Y-%m-%d %H:00')
                        stats['by_hour'][hour] += 1
                    
                    # Collect errors
                    if log_entry.get('log_level') in ['ERROR', 'CRITICAL']:
                        stats['errors'].append({
                            'timestamp': log_entry.get('timestamp'),
                            'event': log_entry.get('event'),
                            'error_type': log_entry.get('error_type'),
                            'error_message': log_entry.get('error_message')
                        })
                    
                    # Collect security events
                    if log_entry.get('event_type') == 'security_event':
                        stats['security_events'].append({
                            'timestamp': log_entry.get('timestamp'),
                            'security_event_type': log_entry.get('security_event_type'),
                            'severity': log_entry.get('severity'),
                            'user_id': log_entry.get('user_id')
                        })
                    
                    # Count API endpoints
                    if log_entry.get('event_type') in ['api_request', 'api_response']:
                        endpoint = log_entry.get('endpoint', 'unknown')
                        stats['api_endpoints'][endpoint] += 1
                    
                    # Collect unique users
                    if 'user_id' in log_entry and log_entry['user_id']:
                        stats['users'].add(log_entry['user_id'])
                
                except json.JSONDecodeError:
                    # Skip non-JSON lines (might be regular log format)
                    continue
                except Exception as e:
                    print(f"Error parsing log entry: {e}")
                    continue
    
    except Exception as e:
        print(f"Error reading log file: {e}")
        return
    
    # Print analysis results
    print(f"\n=== Log Analysis Results ===")
    print(f"Total entries: {stats['total_entries']}")
    print(f"Unique users: {len(stats['users'])}")
    
    print(f"\n--- Log Levels ---")
    for level, count in stats['by_level'].most_common():
        print(f"{level}: {count}")
    
    print(f"\n--- Event Types ---")
    for event_type, count in stats['by_event_type'].most_common():
        print(f"{event_type}: {count}")
    
    print(f"\n--- Top API Endpoints ---")
    for endpoint, count in stats['api_endpoints'].most_common(10):
        print(f"{endpoint}: {count}")
    
    if stats['errors']:
        print(f"\n--- Recent Errors ({len(stats['errors'])}) ---")
        for error in stats['errors'][-5:]:  # Show last 5 errors
            print(f"[{error['timestamp']}] {error['event']}: {error.get('error_message', 'N/A')}")
    
    if stats['security_events']:
        print(f"\n--- Security Events ({len(stats['security_events'])}) ---")
        for event in stats['security_events'][-5:]:  # Show last 5 security events
            print(f"[{event['timestamp']}] {event['security_event_type']} ({event['severity']})")


def show_log_status():
    """Show status of all log files"""
    print("=== Log Files Status ===")
    
    log_info = get_log_files_info()
    if not log_info:
        print("No log files found.")
        return
    
    total_size = 0
    for filename, info in sorted(log_info.items()):
        total_size += info['size_mb']
        print(f"{filename}:")
        print(f"  Size: {info['size_mb']} MB")
        print(f"  Modified: {info['modified']}")
        print(f"  Created: {info['created']}")
        print()
    
    print(f"Total log size: {total_size:.2f} MB")


def cleanup_logs(days):
    """Clean up old log files"""
    print(f"Cleaning up log files older than {days} days...")
    cleanup_old_logs(days_to_keep=days)
    print("Cleanup completed.")


def main():
    parser = argparse.ArgumentParser(description='Log management utilities')
    subparsers = parser.add_subparsers(dest='command', help='Available commands')
    
    # Status command
    subparsers.add_parser('status', help='Show log files status')
    
    # Analyze command
    analyze_parser = subparsers.add_parser('analyze', help='Analyze log file')
    analyze_parser.add_argument('log_file', help='Path to log file')
    analyze_parser.add_argument('--hours', type=int, default=24, help='Hours to analyze (default: 24)')
    
    # Cleanup command
    cleanup_parser = subparsers.add_parser('cleanup', help='Clean up old log files')
    cleanup_parser.add_argument('--days', type=int, default=30, help='Days to keep (default: 30)')
    
    args = parser.parse_args()
    
    if args.command == 'status':
        show_log_status()
    elif args.command == 'analyze':
        analyze_logs(args.log_file, args.hours)
    elif args.command == 'cleanup':
        cleanup_logs(args.days)
    else:
        parser.print_help()


if __name__ == '__main__':
    main()
