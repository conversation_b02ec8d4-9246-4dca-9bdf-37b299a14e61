-- FootPrint 订阅服务 - 数据库迁移修复脚本
-- 用于修复部署时遇到的索引删除问题

-- 安全删除索引（如果存在）
-- 这个脚本会检查索引是否存在，然后安全地删除它们

-- 删除 orders 表的索引
DROP INDEX IF EXISTS idx_orders_status_created ON orders;
DROP INDEX IF EXISTS idx_orders_user_created ON orders;

-- 删除 payment_events 表的索引
DROP INDEX IF EXISTS idx_payment_events_processed_created ON payment_events;

-- 删除 subscriptions 表的索引
DROP INDEX IF EXISTS idx_subscriptions_end_at_desc ON subscriptions;
DROP INDEX IF EXISTS idx_subscriptions_status_end_at ON subscriptions;
DROP INDEX IF EXISTS idx_subscriptions_user_status ON subscriptions;

-- 删除 users 表的索引
DROP INDEX IF EXISTS idx_users_created_at_desc ON users;
DROP INDEX IF EXISTS idx_users_nickname_search ON users;
DROP INDEX IF EXISTS idx_users_updated_at_desc ON users;

-- 显示完成信息
SELECT 'Migration fix completed successfully' as status;
