#!/bin/bash

# FootPrint 订阅服务 - 服务器部署修复脚本
# 用于修复部署时遇到的数据库迁移和Redis连接问题

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目根目录
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
cd "$PROJECT_ROOT"

echo -e "${BLUE}🔧 FootPrint 订阅服务 - 部署修复脚本${NC}"
echo "项目目录: $PROJECT_ROOT"
echo

# 检查Redis服务状态
check_redis_status() {
    echo -e "${BLUE}检查Redis服务状态...${NC}"
    
    if command -v redis-cli &> /dev/null; then
        if redis-cli ping &> /dev/null; then
            echo -e "${GREEN}✅ Redis服务运行正常${NC}"
            return 0
        else
            echo -e "${YELLOW}⚠️  Redis服务未运行或连接失败${NC}"
            return 1
        fi
    else
        echo -e "${YELLOW}⚠️  Redis客户端未安装${NC}"
        return 1
    fi
}

# 启动Redis服务
start_redis() {
    echo -e "${BLUE}尝试启动Redis服务...${NC}"
    
    # 检查是否有systemctl
    if command -v systemctl &> /dev/null; then
        echo "使用systemctl启动Redis..."
        sudo systemctl start redis
        sudo systemctl enable redis
        sleep 2
        if check_redis_status; then
            echo -e "${GREEN}✅ Redis服务启动成功${NC}"
            return 0
        fi
    fi
    
    # 检查是否有service命令
    if command -v service &> /dev/null; then
        echo "使用service命令启动Redis..."
        sudo service redis-server start
        sleep 2
        if check_redis_status; then
            echo -e "${GREEN}✅ Redis服务启动成功${NC}"
            return 0
        fi
    fi
    
    # 尝试直接启动
    if command -v redis-server &> /dev/null; then
        echo "尝试直接启动Redis服务器..."
        nohup redis-server &> /dev/null &
        sleep 3
        if check_redis_status; then
            echo -e "${GREEN}✅ Redis服务启动成功${NC}"
            return 0
        fi
    fi
    
    echo -e "${YELLOW}⚠️  无法启动Redis服务，应用将使用内存存储${NC}"
    return 1
}

# 检查数据库连接
check_database() {
    echo -e "${BLUE}检查数据库连接...${NC}"
    
    # 激活虚拟环境
    if [ -f "venv/bin/activate" ]; then
        source venv/bin/activate
    fi
    
    export FLASK_APP=wsgi.py
    
    # 测试数据库连接
    python3 -c "
from app import create_app
from app.extensions import db
try:
    app = create_app()
    with app.app_context():
        db.engine.execute('SELECT 1')
    print('✅ 数据库连接正常')
except Exception as e:
    print(f'❌ 数据库连接失败: {e}')
    exit(1)
"
}

# 安全的数据库迁移
safe_database_migration() {
    echo -e "${BLUE}执行安全的数据库迁移...${NC}"
    
    # 激活虚拟环境
    if [ -f "venv/bin/activate" ]; then
        source venv/bin/activate
    fi
    
    export FLASK_APP=wsgi.py
    
    # 检查当前数据库版本
    echo "检查当前数据库版本..."
    CURRENT_VERSION=$(flask db current 2>/dev/null || echo "none")
    echo "当前版本: $CURRENT_VERSION"
    
    # 检查目标版本
    echo "检查目标版本..."
    TARGET_VERSION=$(flask db heads 2>/dev/null || echo "unknown")
    echo "目标版本: $TARGET_VERSION"
    
    if [ "$CURRENT_VERSION" = "$TARGET_VERSION" ]; then
        echo -e "${GREEN}✅ 数据库已是最新版本${NC}"
        return 0
    fi
    
    # 备份数据库（如果可能）
    echo -e "${YELLOW}建议在迁移前备份数据库${NC}"
    
    # 执行迁移
    echo "执行数据库迁移..."
    if flask db upgrade; then
        echo -e "${GREEN}✅ 数据库迁移成功${NC}"
    else
        echo -e "${RED}❌ 数据库迁移失败${NC}"
        echo -e "${YELLOW}尝试修复迁移问题...${NC}"
        
        # 尝试标记当前版本
        echo "尝试标记当前版本..."
        flask db stamp head || true
        
        # 再次尝试迁移
        echo "再次尝试迁移..."
        if flask db upgrade; then
            echo -e "${GREEN}✅ 数据库迁移修复成功${NC}"
        else
            echo -e "${RED}❌ 数据库迁移仍然失败，需要手动处理${NC}"
            return 1
        fi
    fi
}

# 修复索引问题
fix_index_issues() {
    echo -e "${BLUE}修复数据库索引问题...${NC}"
    
    # 激活虚拟环境
    if [ -f "venv/bin/activate" ]; then
        source venv/bin/activate
    fi
    
    export FLASK_APP=wsgi.py
    
    # 创建修复脚本
    cat > fix_indexes.py << 'EOF'
from app import create_app
from app.extensions import db
from sqlalchemy import text

app = create_app()

with app.app_context():
    connection = db.engine.connect()
    
    # 要检查和删除的索引
    indexes_to_check = [
        ('idx_orders_status_created', 'orders'),
        ('idx_orders_user_created', 'orders'),
        ('idx_payment_events_processed_created', 'payment_events'),
        ('idx_subscriptions_end_at_desc', 'subscriptions'),
        ('idx_subscriptions_status_end_at', 'subscriptions'),
        ('idx_subscriptions_user_status', 'subscriptions'),
        ('idx_users_created_at_desc', 'users'),
        ('idx_users_nickname_search', 'users'),
        ('idx_users_updated_at_desc', 'users')
    ]
    
    for index_name, table_name in indexes_to_check:
        try:
            # 检查索引是否存在
            result = connection.execute(text(
                "SELECT COUNT(*) FROM information_schema.statistics "
                "WHERE table_schema = DATABASE() AND table_name = :table_name AND index_name = :index_name"
            ), {"table_name": table_name, "index_name": index_name})
            
            if result.scalar() > 0:
                print(f"删除索引 {index_name} from {table_name}")
                connection.execute(text(f"DROP INDEX {index_name} ON {table_name}"))
            else:
                print(f"索引 {index_name} 不存在，跳过")
        except Exception as e:
            print(f"处理索引 {index_name} 时出错: {e}")
    
    connection.close()
    print("索引修复完成")
EOF
    
    # 运行修复脚本
    python3 fix_indexes.py
    
    # 清理临时文件
    rm -f fix_indexes.py
    
    echo -e "${GREEN}✅ 索引问题修复完成${NC}"
}

# 验证部署
verify_deployment() {
    echo -e "${BLUE}验证部署状态...${NC}"
    
    # 激活虚拟环境
    if [ -f "venv/bin/activate" ]; then
        source venv/bin/activate
    fi
    
    export FLASK_APP=wsgi.py
    
    # 检查应用是否可以启动
    echo "测试应用启动..."
    timeout 10 python3 -c "
from app import create_app
app = create_app()
print('✅ 应用可以正常启动')
" || echo "❌ 应用启动测试失败"
    
    # 检查数据库表
    echo "检查数据库表..."
    python3 -c "
from app import create_app
from app.extensions import db
app = create_app()
with app.app_context():
    tables = db.engine.table_names()
    print(f'数据库表数量: {len(tables)}')
    expected_tables = ['users', 'plans', 'orders', 'subscriptions', 'payment_events']
    for table in expected_tables:
        if table in tables:
            print(f'✅ 表 {table} 存在')
        else:
            print(f'❌ 表 {table} 不存在')
"
}

# 主修复流程
main_fix() {
    echo -e "${GREEN}🔧 开始部署修复流程...${NC}"
    
    # 1. 检查和启动Redis
    if ! check_redis_status; then
        start_redis
    fi
    
    # 2. 检查数据库连接
    check_database
    
    # 3. 修复索引问题
    fix_index_issues
    
    # 4. 执行安全的数据库迁移
    safe_database_migration
    
    # 5. 验证部署
    verify_deployment
    
    echo -e "${GREEN}✅ 部署修复完成！${NC}"
    echo
    echo -e "${YELLOW}注意事项:${NC}"
    echo "1. Redis服务状态已检查，如果无法启动，应用将使用内存存储"
    echo "2. 数据库迁移已安全执行"
    echo "3. 索引问题已修复"
    echo "4. 建议重启应用服务以确保所有更改生效"
}

# 显示使用说明
show_usage() {
    echo -e "${BLUE}使用说明:${NC}"
    echo "$0 [选项]"
    echo
    echo "选项:"
    echo "  --fix-all     执行完整修复流程"
    echo "  --redis       只检查和修复Redis问题"
    echo "  --database    只执行数据库迁移修复"
    echo "  --indexes     只修复索引问题"
    echo "  --verify      只验证部署状态"
    echo "  --help        显示此帮助信息"
}

# 主程序
case "${1:-}" in
    --fix-all)
        main_fix
        ;;
    --redis)
        if ! check_redis_status; then
            start_redis
        fi
        ;;
    --database)
        check_database
        safe_database_migration
        ;;
    --indexes)
        fix_index_issues
        ;;
    --verify)
        verify_deployment
        ;;
    --help)
        show_usage
        ;;
    "")
        main_fix
        ;;
    *)
        echo -e "${RED}❌ 未知选项: $1${NC}"
        show_usage
        exit 1
        ;;
esac
