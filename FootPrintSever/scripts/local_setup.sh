#!/bin/bash

# FootPrint 订阅服务 - 本地环境快速设置脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目根目录
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
cd "$PROJECT_ROOT"

echo -e "${BLUE}🚀 FootPrint 订阅服务 - 本地环境设置${NC}"
echo "项目目录: $PROJECT_ROOT"
echo

# 检查 Python 版本
check_python() {
    echo -e "${BLUE}检查 Python 版本...${NC}"
    if command -v python3 &> /dev/null; then
        PYTHON_VERSION=$(python3 --version | cut -d' ' -f2)
        echo -e "${GREEN}✅ Python $PYTHON_VERSION${NC}"
    else
        echo -e "${RED}❌ Python 3 未安装${NC}"
        exit 1
    fi
}

# 检查 MySQL
check_mysql() {
    echo -e "${BLUE}检查 MySQL...${NC}"
    if command -v mysql &> /dev/null; then
        MYSQL_VERSION=$(mysql --version | cut -d' ' -f3 | cut -d',' -f1)
        echo -e "${GREEN}✅ MySQL $MYSQL_VERSION${NC}"
        
        # 检查 MySQL 服务状态
        if pgrep -x "mysqld" > /dev/null; then
            echo -e "${GREEN}✅ MySQL 服务运行中${NC}"
        else
            echo -e "${YELLOW}⚠️  MySQL 服务未运行，尝试启动...${NC}"
            if command -v brew &> /dev/null; then
                brew services start mysql
            else
                echo -e "${RED}❌ 请手动启动 MySQL 服务${NC}"
            fi
        fi
    else
        echo -e "${RED}❌ MySQL 未安装${NC}"
        echo "请安装 MySQL: brew install mysql"
        exit 1
    fi
}

# 检查 Redis (可选)
check_redis() {
    echo -e "${BLUE}检查 Redis (可选)...${NC}"
    if command -v redis-server &> /dev/null; then
        REDIS_VERSION=$(redis-server --version | cut -d' ' -f3 | cut -d'=' -f2)
        echo -e "${GREEN}✅ Redis $REDIS_VERSION${NC}"
        
        # 检查 Redis 服务状态
        if pgrep -x "redis-server" > /dev/null; then
            echo -e "${GREEN}✅ Redis 服务运行中${NC}"
        else
            echo -e "${YELLOW}⚠️  Redis 服务未运行，尝试启动...${NC}"
            if command -v brew &> /dev/null; then
                brew services start redis
            fi
        fi
    else
        echo -e "${YELLOW}⚠️  Redis 未安装 (可选组件)${NC}"
        echo "如需安装: brew install redis"
    fi
}

# 设置虚拟环境
setup_venv() {
    echo -e "${BLUE}设置 Python 虚拟环境...${NC}"
    
    if [ ! -d "venv" ]; then
        echo "创建虚拟环境..."
        python3 -m venv venv
    fi
    
    echo "激活虚拟环境..."
    source venv/bin/activate
    
    echo "升级 pip..."
    pip install --upgrade pip
    
    echo "安装依赖..."
    pip install -r requirements.txt
    
    echo -e "${GREEN}✅ 虚拟环境设置完成${NC}"
}

# 设置环境配置
setup_env() {
    echo -e "${BLUE}设置环境配置...${NC}"
    
    if [ ! -f ".env" ]; then
        if [ -f ".env.local" ]; then
            echo "复制本地配置模板..."
            cp .env.local .env
        else
            echo "复制默认配置模板..."
            cp .env.example .env
        fi
        echo -e "${YELLOW}⚠️  请编辑 .env 文件，配置数据库和微信参数${NC}"
    else
        echo -e "${GREEN}✅ .env 文件已存在${NC}"
    fi
}

# 创建数据库
setup_database() {
    echo -e "${BLUE}设置数据库...${NC}"
    
    # 读取数据库配置
    if [ -f ".env" ]; then
        source .env
    fi
    
    # 提取数据库信息
    DB_NAME="footprint_subscription"
    DB_USER="footprint"
    DB_PASS="74LS00DB"
    
    echo "创建数据库和用户..."
    mysql -u root -p << EOF
CREATE DATABASE IF NOT EXISTS $DB_NAME CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER IF NOT EXISTS '$DB_USER'@'localhost' IDENTIFIED BY '$DB_PASS';
GRANT ALL PRIVILEGES ON $DB_NAME.* TO '$DB_USER'@'localhost';
FLUSH PRIVILEGES;
EOF
    
    echo -e "${GREEN}✅ 数据库设置完成${NC}"
}

# 检查迁移文件冲突
check_migration_conflicts() {
    echo -e "${BLUE}检查迁移文件冲突...${NC}"

    if [ -d "migrations/versions" ]; then
        # 检查是否有多个 initial migration 文件
        INITIAL_COUNT=$(ls migrations/versions/*initial_migration.py 2>/dev/null | wc -l)
        if [ "$INITIAL_COUNT" -gt 1 ]; then
            echo -e "${YELLOW}⚠️  发现多个初始迁移文件，这可能导致冲突${NC}"
            echo "发现的初始迁移文件："
            ls -la migrations/versions/*initial_migration.py
            echo
            echo -e "${YELLOW}建议操作：${NC}"
            echo "1. 保留最早的初始迁移文件"
            echo "2. 删除其他冲突的初始迁移文件"
            echo "3. 重新生成干净的迁移"
            echo
            read -p "是否自动修复迁移冲突？(y/N): " fix_conflicts
            if [[ $fix_conflicts =~ ^[Yy]$ ]]; then
                fix_migration_conflicts
            fi
        else
            echo -e "${GREEN}✅ 未发现迁移文件冲突${NC}"
        fi
    fi
}

# 修复迁移文件冲突
fix_migration_conflicts() {
    echo -e "${BLUE}修复迁移文件冲突...${NC}"

    # 备份迁移文件夹
    if [ -d "migrations" ]; then
        echo "备份当前迁移文件..."
        cp -r migrations migrations_backup_$(date +%Y%m%d_%H%M%S)
    fi

    # 找到最早的初始迁移文件（通常是创建表结构的）
    EARLIEST_MIGRATION=$(ls migrations/versions/*initial_migration.py 2>/dev/null | head -1)

    if [ -n "$EARLIEST_MIGRATION" ]; then
        echo "保留最早的迁移文件: $(basename $EARLIEST_MIGRATION)"

        # 删除其他初始迁移文件
        for file in migrations/versions/*initial_migration.py; do
            if [ "$file" != "$EARLIEST_MIGRATION" ]; then
                echo "删除冲突的迁移文件: $(basename $file)"
                rm "$file"
            fi
        done

        # 删除可能有问题的特定迁移文件
        PROBLEM_FILES=(
            "migrations/versions/43fb7ee4579b_initial_migration.py"
            "migrations/versions/ca0b3151aa9d_initial_migration.py"
            "migrations/versions/930173a96d33_initial_migration.py"
        )

        for file in "${PROBLEM_FILES[@]}"; do
            if [ -f "$file" ]; then
                echo "删除已知问题迁移文件: $(basename $file)"
                rm "$file"
            fi
        done

        echo -e "${GREEN}✅ 迁移文件冲突修复完成${NC}"
    else
        echo -e "${RED}❌ 未找到初始迁移文件${NC}"
    fi
}

# 安全的数据库迁移
safe_database_migration() {
    echo -e "${BLUE}执行安全的数据库迁移...${NC}"

    source venv/bin/activate
    export FLASK_APP=wsgi.py

    # 检查当前数据库版本
    echo "检查当前数据库版本..."
    CURRENT_VERSION=$(flask db current 2>/dev/null || echo "none")
    echo "当前版本: $CURRENT_VERSION"

    # 检查目标版本
    echo "检查目标版本..."
    TARGET_VERSION=$(flask db heads 2>/dev/null || echo "unknown")
    echo "目标版本: $TARGET_VERSION"

    if [ "$CURRENT_VERSION" = "$TARGET_VERSION" ] && [ "$CURRENT_VERSION" != "none" ]; then
        echo -e "${GREEN}✅ 数据库已是最新版本${NC}"
        return 0
    fi

    # 尝试执行迁移
    echo "执行数据库迁移..."
    if flask db upgrade; then
        echo -e "${GREEN}✅ 数据库迁移成功${NC}"
    else
        echo -e "${RED}❌ 数据库迁移失败${NC}"
        echo -e "${YELLOW}尝试修复迁移问题...${NC}"

        # 如果是首次部署且数据库为空，直接标记为最新版本
        if [ "$CURRENT_VERSION" = "none" ]; then
            echo "首次部署，尝试标记数据库版本..."
            flask db stamp head
            echo -e "${GREEN}✅ 数据库版本标记完成${NC}"
        else
            echo -e "${RED}❌ 需要手动处理迁移问题${NC}"
            return 1
        fi
    fi
}

# 初始化数据库
init_database() {
    echo -e "${BLUE}初始化数据库...${NC}"

    # 确保Redis服务运行（如果可用）
    echo "检查Redis服务状态..."
    if command -v redis-server &> /dev/null; then
        if ! pgrep -x "redis-server" > /dev/null; then
            echo -e "${YELLOW}⚠️  Redis服务未运行，尝试启动...${NC}"
            if command -v brew &> /dev/null; then
                brew services start redis
                sleep 2  # 等待服务启动
            fi
        fi
    fi

    source venv/bin/activate
    export FLASK_APP=wsgi.py

    # 检查迁移文件冲突
    check_migration_conflicts

    # 检查是否已经初始化
    if [ ! -d "migrations" ]; then
        echo "初始化数据库迁移..."
        flask db init
    fi

    # 只在没有迁移文件时创建新的迁移
    if [ -z "$(ls -A migrations/versions/ 2>/dev/null)" ]; then
        echo "创建初始迁移文件..."
        flask db migrate -m "Initial migration"
    else
        echo "发现现有迁移文件，跳过创建新迁移"
    fi

    echo "执行数据库迁移..."
    safe_database_migration

    echo "初始化基础数据..."
    if [ -f "init_db.py" ]; then
        python init_db.py
    fi

    echo -e "${GREEN}✅ 数据库初始化完成${NC}"
}

# 创建测试证书
create_test_certs() {
    echo -e "${BLUE}创建测试证书...${NC}"
    
    mkdir -p certs
    
    if [ ! -f "certs/apiclient_key.pem" ]; then
        echo "创建测试私钥..."
        openssl genrsa -out certs/apiclient_key.pem 2048
    fi
    
    if [ ! -f "certs/wechatpay_cert.pem" ]; then
        echo "创建测试证书..."
        openssl req -new -x509 -key certs/apiclient_key.pem -out certs/wechatpay_cert.pem -days 365 \
            -subj "/C=CN/ST=Beijing/L=Beijing/O=Test/CN=test.example.com"
    fi
    
    echo -e "${GREEN}✅ 测试证书创建完成${NC}"
    echo -e "${YELLOW}⚠️  生产环境请使用真实的微信支付证书${NC}"
}

# 运行测试
run_tests() {
    echo -e "${BLUE}运行测试...${NC}"
    
    source venv/bin/activate
    
    if [ -f "scripts/run_tests.py" ]; then
        python scripts/run_tests.py --unit
    else
        pytest tests/ -v
    fi
    
    echo -e "${GREEN}✅ 测试完成${NC}"
}

# 启动服务
start_service() {
    echo -e "${BLUE}启动服务...${NC}"
    
    source venv/bin/activate
    export FLASK_APP=wsgi.py
    
    echo -e "${GREEN}🚀 启动 FootPrint 订阅服务...${NC}"
    echo "访问地址: http://localhost:8000"
    echo "健康检查: http://localhost:8000/healthz"
    echo "API 文档: 请查看 API_DOCUMENTATION.md"
    echo
    echo "按 Ctrl+C 停止服务"
    echo

    flask run --host=0.0.0.0 --port=8000
}

# 启动 Web 管理服务
start_admin_service() {
    echo -e "${BLUE}启动 Web 管理服务...${NC}"
    
    source venv/bin/activate
    export FLASK_APP=wsgi.py
    
    
    echo -e "${GREEN}🚀 启动 FootPrint Web 管理后台...${NC}"
    echo "管理后台地址: http://localhost:5001/admin/"
    echo "登录页面: http://localhost:5001/admin/login"
    echo "默认账号: admin"
    echo "默认密码: admin123"
    echo "健康检查: http://localhost:5001/healthz"
    echo
    echo -e "${YELLOW}⚠️  首次登录后请修改默认密码！${NC}"
    echo "按 Ctrl+C 停止服务"
    echo

    python wsgi.py --port=$PORT
}

# 退出服务
stop_service() {
    echo -e "${BLUE}退出 API 服务...${NC}"
    
    # 找到 Flask API 服务进程并结束它
    FLASK_PID=$(pgrep -f "flask run")
    if [ -n "$FLASK_PID" ]; then
        echo -e "${YELLOW}停止 Flask API 服务 (PID: $FLASK_PID)...${NC}"
        kill -TERM $FLASK_PID
        sleep 2
        # 如果进程仍然存在，强制结束
        if kill -0 $FLASK_PID 2>/dev/null; then
            kill -9 $FLASK_PID
        fi
        echo -e "${GREEN}✅ Flask API 服务已停止${NC}"
    else
        echo -e "${GREEN}没有找到运行中的 Flask API 服务${NC}"
    fi
}

# 退出 Web 管理服务
stop_admin_service() {
    echo -e "${BLUE}退出 Web 管理服务...${NC}"
    
    # 找到 Web 管理服务进程并结束它
    ADMIN_PID=$(pgrep -f "wsgi.py")
    if [ -n "$ADMIN_PID" ]; then
        echo -e "${YELLOW}停止 Web 管理服务 (PID: $ADMIN_PID)...${NC}"
        kill -TERM $ADMIN_PID
        sleep 2
        # 如果进程仍然存在，强制结束
        if kill -0 $ADMIN_PID 2>/dev/null; then
            kill -9 $ADMIN_PID
        fi
        echo -e "${GREEN}✅ Web 管理服务已停止${NC}"
    else
        echo -e "${GREEN}没有找到运行中的 Web 管理服务${NC}"
    fi
}

# 退出所有服务
stop_all_services() {
    echo -e "${BLUE}退出所有服务...${NC}"
    
    stop_service
    stop_admin_service
    
    # 可选：停止 Redis 和 MySQL (通常不建议自动停止系统服务)
    read -p "是否同时停止 Redis 和 MySQL 服务？(y/N): " stop_deps
    if [[ $stop_deps =~ ^[Yy]$ ]]; then
        if pgrep -x "redis-server" > /dev/null; then
            echo -e "${YELLOW}停止 Redis 服务...${NC}"
            if command -v brew &> /dev/null; then
                brew services stop redis
            else
                pkill -x "redis-server"
            fi
        fi

        if pgrep -x "mysqld" > /dev/null; then
            echo -e "${YELLOW}停止 MySQL 服务...${NC}"
            if command -v brew &> /dev/null; then
                brew services stop mysql
            else
                pkill -x "mysqld"
            fi
        fi
    fi
    
    echo -e "${GREEN}✅ 所有服务已退出！${NC}"
}


# 主菜单
show_menu() {
    echo -e "${BLUE}请选择操作:${NC}"
    echo "1. 完整设置 (推荐首次使用)"
    echo "2. 检查环境"
    echo "3. 设置虚拟环境"
    echo "4. 设置数据库"
    echo "5. 初始化数据库"
    echo "6. 检查迁移文件冲突"
    echo "7. 修复迁移文件冲突"
    echo "8. 创建测试证书"
    echo "9. 运行测试"
    echo "10. 启动 API 服务"
    echo "11. 启动 Web 管理服务"
    echo "12. 退出 API 服务"
    echo "13. 退出 Web 管理服务"
    echo "14. 退出所有服务"
    echo "15. 退出脚本"
    echo
    read -p "请输入选项 (1-15): " choice
}

# 完整设置
full_setup() {
    echo -e "${GREEN}🔧 开始完整设置...${NC}"
    check_python
    check_mysql
    check_redis  # 检查并启动Redis（如果可用）
    setup_venv
    setup_env
    setup_database
    init_database  # 现在会确保Redis运行
    create_test_certs
    echo -e "${GREEN}✅ 完整设置完成！${NC}"
    echo
    echo -e "${YELLOW}下一步:${NC}"
    echo "1. 编辑 .env 文件，配置真实的微信参数"
    echo "2. 运行测试: ./scripts/local_setup.sh 选择选项 7"
    echo "3. 启动服务: ./scripts/local_setup.sh 选择选项 8"
    echo
    echo -e "${YELLOW}注意:${NC}"
    echo "- Redis服务已启动（如果可用），用于速率限制"
    echo "- 如果Redis不可用，应用将使用内存存储"
}

# 主程序
main() {
    if [ "$1" = "--auto" ]; then
        full_setup
        return
    fi
    
    while true; do
        show_menu
        case $choice in
            1)
                full_setup
                ;;
            2)
                check_python
                check_mysql
                check_redis
                ;;
            3)
                setup_venv
                ;;
            4)
                setup_database
                ;;
            5)
                init_database
                ;;
            6)
                check_migration_conflicts
                ;;
            7)
                fix_migration_conflicts
                ;;
            8)
                create_test_certs
                ;;
            9)
                run_tests
                ;;
            10)
                start_service
                ;;
            11)
                start_admin_service
                ;;
            12)
                stop_service
                ;;
            13)
                stop_admin_service
                ;;
            14)
                stop_all_services
                ;;
            15)
                echo -e "${GREEN}👋 再见！${NC}"
                exit 0
                ;;
            *)
                echo -e "${RED}❌ 无效选项，请重新选择${NC}"
                ;;
        esac
        echo
        read -p "按回车键继续..."
        echo
    done
}

# 运行主程序
main "$@"
