#!/bin/bash

# FootPrint 订阅服务 - 服务器部署脚本
# 专门用于服务器环境的部署和问题修复

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目根目录
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
cd "$PROJECT_ROOT"

echo -e "${BLUE}🚀 FootPrint 订阅服务 - 服务器部署脚本${NC}"
echo "项目目录: $PROJECT_ROOT"
echo "服务器环境: $(uname -a)"
echo

# 检查并修复迁移文件冲突
fix_migration_conflicts() {
    echo -e "${BLUE}检查并修复迁移文件冲突...${NC}"
    
    if [ ! -d "migrations/versions" ]; then
        echo -e "${YELLOW}⚠️  迁移文件夹不存在${NC}"
        return 0
    fi
    
    # 检查是否有多个 initial migration 文件
    INITIAL_FILES=($(ls migrations/versions/*initial_migration.py 2>/dev/null || true))
    INITIAL_COUNT=${#INITIAL_FILES[@]}
    
    if [ "$INITIAL_COUNT" -gt 1 ]; then
        echo -e "${YELLOW}⚠️  发现 $INITIAL_COUNT 个初始迁移文件，需要清理${NC}"
        
        # 备份迁移文件夹
        echo "备份迁移文件..."
        cp -r migrations migrations_backup_$(date +%Y%m%d_%H%M%S)
        
        # 删除已知有问题的迁移文件
        PROBLEM_FILES=(
            "migrations/versions/43fb7ee4579b_initial_migration.py"
            "migrations/versions/ca0b3151aa9d_initial_migration.py"
            "migrations/versions/930173a96d33_initial_migration.py"
        )
        
        for file in "${PROBLEM_FILES[@]}"; do
            if [ -f "$file" ]; then
                echo "删除问题迁移文件: $(basename $file)"
                rm "$file"
            fi
        done
        
        echo -e "${GREEN}✅ 迁移文件冲突修复完成${NC}"
    else
        echo -e "${GREEN}✅ 未发现迁移文件冲突${NC}"
    fi
}

# 检查Redis服务
check_and_start_redis() {
    echo -e "${BLUE}检查Redis服务...${NC}"
    
    # 检查Redis是否已安装
    if ! command -v redis-cli &> /dev/null; then
        echo -e "${YELLOW}⚠️  Redis未安装，应用将使用内存存储${NC}"
        return 1
    fi
    
    # 检查Redis是否运行
    if redis-cli ping &> /dev/null; then
        echo -e "${GREEN}✅ Redis服务运行正常${NC}"
        return 0
    fi
    
    echo -e "${YELLOW}⚠️  Redis服务未运行，尝试启动...${NC}"
    
    # 尝试不同的启动方式
    if command -v systemctl &> /dev/null; then
        # 尝试不同的服务名称
        for service_name in redis redis-server redisd; do
            if systemctl list-unit-files | grep -q "^${service_name}.service"; then
                echo "尝试启动 ${service_name} 服务..."
                if sudo systemctl start "$service_name" 2>/dev/null; then
                    sudo systemctl enable "$service_name" 2>/dev/null || true
                    sleep 2
                    if redis-cli ping &> /dev/null; then
                        echo -e "${GREEN}✅ Redis服务启动成功${NC}"
                        return 0
                    fi
                fi
            fi
        done
    fi
    
    # 尝试使用service命令
    if command -v service &> /dev/null; then
        for service_name in redis redis-server; do
            if sudo service "$service_name" start 2>/dev/null; then
                sleep 2
                if redis-cli ping &> /dev/null; then
                    echo -e "${GREEN}✅ Redis服务启动成功${NC}"
                    return 0
                fi
            fi
        done
    fi
    
    echo -e "${YELLOW}⚠️  无法启动Redis服务，应用将使用内存存储${NC}"
    return 1
}

# 安全的数据库迁移
safe_database_migration() {
    echo -e "${BLUE}执行数据库迁移...${NC}"
    
    # 激活虚拟环境
    if [ -f "venv/bin/activate" ]; then
        source venv/bin/activate
    else
        echo -e "${RED}❌ 虚拟环境不存在${NC}"
        return 1
    fi
    
    export FLASK_APP=wsgi.py
    
    # 检查当前数据库版本
    echo "检查当前数据库版本..."
    CURRENT_VERSION=$(flask db current 2>/dev/null || echo "none")
    echo "当前版本: $CURRENT_VERSION"
    
    # 检查目标版本
    echo "检查目标版本..."
    TARGET_VERSION=$(flask db heads 2>/dev/null || echo "unknown")
    echo "目标版本: $TARGET_VERSION"
    
    if [ "$CURRENT_VERSION" = "$TARGET_VERSION" ] && [ "$CURRENT_VERSION" != "none" ]; then
        echo -e "${GREEN}✅ 数据库已是最新版本${NC}"
        return 0
    fi
    
    # 尝试执行迁移
    echo "执行数据库迁移..."
    if flask db upgrade 2>/dev/null; then
        echo -e "${GREEN}✅ 数据库迁移成功${NC}"
    else
        echo -e "${YELLOW}⚠️  标准迁移失败，尝试修复...${NC}"
        
        # 如果是首次部署，直接标记为最新版本
        if [ "$CURRENT_VERSION" = "none" ]; then
            echo "首次部署，标记数据库版本..."
            flask db stamp head
            echo -e "${GREEN}✅ 数据库版本标记完成${NC}"
        else
            echo -e "${RED}❌ 数据库迁移失败，需要手动处理${NC}"
            return 1
        fi
    fi
}

# 验证部署
verify_deployment() {
    echo -e "${BLUE}验证部署状态...${NC}"
    
    # 激活虚拟环境
    if [ -f "venv/bin/activate" ]; then
        source venv/bin/activate
    fi
    
    export FLASK_APP=wsgi.py
    
    # 检查应用是否可以启动
    echo "测试应用启动..."
    if timeout 10 python3 -c "
from app import create_app
try:
    app = create_app()
    print('✅ 应用可以正常启动')
except Exception as e:
    print(f'❌ 应用启动失败: {e}')
    exit(1)
" 2>/dev/null; then
        echo -e "${GREEN}✅ 应用启动测试通过${NC}"
    else
        echo -e "${RED}❌ 应用启动测试失败${NC}"
        return 1
    fi
    
    # 检查数据库连接
    echo "测试数据库连接..."
    if python3 -c "
from app import create_app
from app.extensions import db
try:
    app = create_app()
    with app.app_context():
        db.engine.execute('SELECT 1')
    print('✅ 数据库连接正常')
except Exception as e:
    print(f'❌ 数据库连接失败: {e}')
    exit(1)
" 2>/dev/null; then
        echo -e "${GREEN}✅ 数据库连接测试通过${NC}"
    else
        echo -e "${RED}❌ 数据库连接测试失败${NC}"
        return 1
    fi
}

# 主部署流程
main_deploy() {
    echo -e "${GREEN}🔧 开始服务器部署流程...${NC}"
    
    # 1. 修复迁移文件冲突
    fix_migration_conflicts
    
    # 2. 检查和启动Redis
    check_and_start_redis
    
    # 3. 执行数据库迁移
    safe_database_migration
    
    # 4. 验证部署
    verify_deployment
    
    echo -e "${GREEN}✅ 服务器部署完成！${NC}"
    echo
    echo -e "${YELLOW}部署总结:${NC}"
    echo "1. 迁移文件冲突已检查和修复"
    echo "2. Redis服务状态已检查"
    echo "3. 数据库迁移已安全执行"
    echo "4. 应用部署验证通过"
    echo
    echo -e "${BLUE}下一步:${NC}"
    echo "1. 启动应用服务: python wsgi.py"
    echo "2. 检查应用日志确认运行状态"
    echo "3. 测试API接口: curl http://localhost:port/healthz"
}

# 显示使用说明
show_usage() {
    echo -e "${BLUE}使用说明:${NC}"
    echo "$0 [选项]"
    echo
    echo "选项:"
    echo "  --deploy      执行完整部署流程（默认）"
    echo "  --fix-migrations  只修复迁移文件冲突"
    echo "  --redis       只检查和启动Redis"
    echo "  --migrate     只执行数据库迁移"
    echo "  --verify      只验证部署状态"
    echo "  --help        显示此帮助信息"
}

# 主程序
case "${1:---deploy}" in
    --deploy)
        main_deploy
        ;;
    --fix-migrations)
        fix_migration_conflicts
        ;;
    --redis)
        check_and_start_redis
        ;;
    --migrate)
        safe_database_migration
        ;;
    --verify)
        verify_deployment
        ;;
    --help)
        show_usage
        ;;
    *)
        echo -e "${RED}❌ 未知选项: $1${NC}"
        show_usage
        exit 1
        ;;
esac
