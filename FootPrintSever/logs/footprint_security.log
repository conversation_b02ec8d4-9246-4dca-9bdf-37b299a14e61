{"event_type": "security_event", "security_event_type": "failed_login_attempt", "severity": "medium", "user_id": "user_123", "ip_address": "*************", "attempts": 3, "event": "Security event", "logger": "security", "level": "warning", "date": "2025-11-12", "time": "15:57:15", "timestamp_unix": 1762934235, "log_level": "WARNING", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T07:57:15.120758Z"}
{"event_type": "security_event", "security_event_type": "suspicious_activity", "severity": "high", "user_id": "user_456", "activity_type": "mult***MASKED***ures", "failure_count": 5, "event": "Security event", "logger": "security", "level": "error", "date": "2025-11-12", "time": "15:57:15", "timestamp_unix": 1762934235, "log_level": "ERROR", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T07:57:15.120948Z"}
2025-11-12 16:01:29,724 WARNING: {"event_type": "security_event", "security_event_type": "failed_login_attempt", "severity": "medium", "user_id": "user_123", "ip_address": "*************", "attempts": 3, "event": "Security event", "logger": "security", "level": "warning", "date": "2025-11-12", "time": "16:01:29", "timestamp_unix": 1762934489, "log_level": "WARNING", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T08:01:29.724556Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/venv/lib/python3.9/site-packages/structlog/_base.py:223]
2025-11-12 16:01:29,724 ERROR: {"event_type": "security_event", "security_event_type": "suspicious_activity", "severity": "high", "user_id": "user_456", "activity_type": "mult***MASKED***ures", "failure_count": 5, "event": "Security event", "logger": "security", "level": "error", "date": "2025-11-12", "time": "16:01:29", "timestamp_unix": 1762934489, "log_level": "ERROR", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T08:01:29.724627Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/venv/lib/python3.9/site-packages/structlog/_base.py:223]
