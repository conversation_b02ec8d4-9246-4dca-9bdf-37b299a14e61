2025-11-12 15:54:54,591 WARNING: Redis connection failed: Error 61 connecting to localhost:6379. Connection refused. [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:39]
2025-11-12 15:54:54,618 INFO: {"event": "Initializing scheduler tasks", "logger": "app.tasks.subscription_tasks", "level": "info", "timestamp": "2025-11-12T07:54:54.618617Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:119]
2025-11-12 15:54:54,618 INFO: {"event": "Scheduler tasks initialized successfully", "logger": "app.tasks.subscription_tasks", "level": "info", "timestamp": "2025-11-12T07:54:54.618776Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:124]
2025-11-12 15:55:20,117 WARNING: Redis connection failed: Error 61 connecting to localhost:6379. Connection refused. [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:39]
2025-11-12 15:55:20,132 INFO: {"event": "Initializing scheduler tasks", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "15:55:20", "timestamp_unix": 1762934120, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T07:55:20.132893Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:119]
2025-11-12 15:55:20,133 INFO: {"event": "Scheduler tasks initialized successfully", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "15:55:20", "timestamp_unix": 1762934120, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T07:55:20.133018Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:124]
2025-11-12 15:55:32,886 WARNING: Redis connection failed: Error 61 connecting to localhost:6379. Connection refused. [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:39]
2025-11-12 15:55:32,902 INFO: {"event": "Initializing scheduler tasks", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "15:55:32", "timestamp_unix": 1762934132, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T07:55:32.902402Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:119]
2025-11-12 15:55:32,902 INFO: {"event": "Scheduler tasks initialized successfully", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "15:55:32", "timestamp_unix": 1762934132, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T07:55:32.902525Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:124]
2025-11-12 15:55:44,650 WARNING: Redis connection failed: Error 61 connecting to localhost:6379. Connection refused. [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:39]
2025-11-12 15:55:44,664 INFO: {"event": "Initializing scheduler tasks", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "15:55:44", "timestamp_unix": 1762934144, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T07:55:44.664276Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:119]
2025-11-12 15:55:44,664 INFO: {"event": "Scheduler tasks initialized successfully", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "15:55:44", "timestamp_unix": 1762934144, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T07:55:44.664382Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:124]
2025-11-12 15:55:57,415 WARNING: Redis connection failed: Error 61 connecting to localhost:6379. Connection refused. [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:39]
2025-11-12 15:55:57,428 INFO: {"event": "Initializing scheduler tasks", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "15:55:57", "timestamp_unix": 1762934157, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T07:55:57.428759Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:119]
2025-11-12 15:55:57,428 INFO: {"event": "Scheduler tasks initialized successfully", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "15:55:57", "timestamp_unix": 1762934157, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T07:55:57.428854Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:124]
2025-11-12 15:56:12,227 WARNING: Redis connection failed: Error 61 connecting to localhost:6379. Connection refused. [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:39]
2025-11-12 15:56:12,241 INFO: {"event": "Initializing scheduler tasks", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "15:56:12", "timestamp_unix": 1762934172, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T07:56:12.241407Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:119]
2025-11-12 15:56:12,241 INFO: {"event": "Scheduler tasks initialized successfully", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "15:56:12", "timestamp_unix": 1762934172, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T07:56:12.241514Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:124]
2025-11-12 15:58:25,115 WARNING: Redis connection failed: Error 61 connecting to localhost:6379. Connection refused. [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:39]
2025-11-12 15:58:25,129 INFO: {"event": "Initializing scheduler tasks", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "15:58:25", "timestamp_unix": 1762934305, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T07:58:25.129737Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:119]
2025-11-12 15:58:25,129 INFO: {"event": "Scheduler tasks initialized successfully", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "15:58:25", "timestamp_unix": 1762934305, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T07:58:25.129863Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:124]
2025-11-12 15:58:34,837 WARNING: Redis connection failed: Error 61 connecting to localhost:6379. Connection refused. [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:39]
2025-11-12 15:58:34,851 INFO: {"event": "Initializing scheduler tasks", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "15:58:34", "timestamp_unix": 1762934314, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T07:58:34.851076Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:119]
2025-11-12 15:58:34,851 INFO: {"event": "Scheduler tasks initialized successfully", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "15:58:34", "timestamp_unix": 1762934314, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T07:58:34.851174Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:124]
2025-11-12 15:59:42,712 WARNING: Redis connection failed: Error 61 connecting to localhost:6379. Connection refused. [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:39]
2025-11-12 15:59:42,725 INFO: {"event": "Initializing scheduler tasks", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "15:59:42", "timestamp_unix": 1762934382, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T07:59:42.725533Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:119]
2025-11-12 15:59:42,725 INFO: {"event": "Scheduler tasks initialized successfully", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "15:59:42", "timestamp_unix": 1762934382, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T07:59:42.725628Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:124]
2025-11-12 15:59:55,541 WARNING: Redis connection failed: Error 61 connecting to localhost:6379. Connection refused. [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:39]
2025-11-12 15:59:55,555 INFO: {"event": "Initializing scheduler tasks", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "15:59:55", "timestamp_unix": 1762934395, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T07:59:55.555699Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:119]
2025-11-12 15:59:55,555 INFO: {"event": "Scheduler tasks initialized successfully", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "15:59:55", "timestamp_unix": 1762934395, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T07:59:55.555805Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:124]
2025-11-12 16:00:28,158 WARNING: Redis connection failed: Error 61 connecting to localhost:6379. Connection refused. [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:39]
2025-11-12 16:00:28,171 INFO: {"event": "Initializing scheduler tasks", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "16:00:28", "timestamp_unix": 1762934428, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T08:00:28.171702Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:119]
2025-11-12 16:00:28,171 INFO: {"event": "Scheduler tasks initialized successfully", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "16:00:28", "timestamp_unix": 1762934428, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T08:00:28.171801Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:124]
2025-11-12 16:00:38,904 WARNING: Redis connection failed: Error 61 connecting to localhost:6379. Connection refused. [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:39]
2025-11-12 16:00:38,918 INFO: {"event": "Initializing scheduler tasks", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "16:00:38", "timestamp_unix": 1762934438, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T08:00:38.918317Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:119]
2025-11-12 16:00:38,918 INFO: {"event": "Scheduler tasks initialized successfully", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "16:00:38", "timestamp_unix": 1762934438, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T08:00:38.918420Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:124]
2025-11-12 16:00:50,682 WARNING: Redis connection failed: Error 61 connecting to localhost:6379. Connection refused. [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:39]
2025-11-12 16:00:50,696 INFO: {"event": "Initializing scheduler tasks", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "16:00:50", "timestamp_unix": 1762934450, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T08:00:50.696177Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:119]
2025-11-12 16:00:50,696 INFO: {"event": "Scheduler tasks initialized successfully", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "16:00:50", "timestamp_unix": 1762934450, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T08:00:50.696285Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:124]
2025-11-12 16:01:04,532 WARNING: Redis connection failed: Error 61 connecting to localhost:6379. Connection refused. [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:39]
2025-11-12 16:01:04,546 INFO: {"event": "Initializing scheduler tasks", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "16:01:04", "timestamp_unix": 1762934464, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T08:01:04.546710Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:119]
2025-11-12 16:01:04,546 INFO: {"event": "Scheduler tasks initialized successfully", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "16:01:04", "timestamp_unix": 1762934464, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T08:01:04.546822Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:124]
2025-11-12 16:01:19,392 WARNING: Redis connection failed: Error 61 connecting to localhost:6379. Connection refused. [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:39]
2025-11-12 16:01:19,405 INFO: {"event": "Initializing scheduler tasks", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "16:01:19", "timestamp_unix": 1762934479, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T08:01:19.405896Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:119]
2025-11-12 16:01:19,406 INFO: {"event": "Scheduler tasks initialized successfully", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "16:01:19", "timestamp_unix": 1762934479, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T08:01:19.406004Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:124]
2025-11-12 16:22:14,414 WARNING: Redis connection failed: Error 61 connecting to localhost:6379. Connection refused. [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:39]
2025-11-12 16:22:14,428 INFO: {"event": "Initializing scheduler tasks", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "16:22:14", "timestamp_unix": 1762935734, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T08:22:14.428528Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:119]
2025-11-12 16:22:14,428 INFO: {"event": "Scheduler tasks initialized successfully", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "16:22:14", "timestamp_unix": 1762935734, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T08:22:14.428623Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:124]
2025-11-12 16:22:14,832 WARNING: Redis connection failed: Error 61 connecting to localhost:6379. Connection refused. [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:39]
2025-11-12 16:22:14,843 INFO: {"event": "Initializing scheduler tasks", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "16:22:14", "timestamp_unix": 1762935734, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T08:22:14.843453Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:119]
2025-11-12 16:22:14,843 INFO: {"event": "Scheduler tasks initialized successfully", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "16:22:14", "timestamp_unix": 1762935734, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T08:22:14.843592Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:124]
2025-11-12 16:22:27,652 INFO: {"user_id": 10, "plan_id": 1, "order_id": 75, "out_trade_no": "FP17629357478a7a8c42", "amount": 1, "notify_url": "http://localhost:8000/webhook/wechatpay", "event": "Creating WeChat Pay APP transaction", "logger": "app.services.subscription_order_service", "level": "info", "request_id": "6d8a67de920e66bf", "method": "POST", "path": "/billing/subscription/order", "remote_addr": "**************", "user_agent": "Dalvik/2.1.0 (Linux; U; Android 13; 22041211AC Build/TP1A.220624.014)", "date": "2025-11-12", "time": "16:22:27", "timestamp_unix": 1762935747, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T08:22:27.652005Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/services/subscription_order_service.py:288]
2025-11-12 16:22:27,652 DEBUG: {"method": "POST", "url_path": "/v3/pay/transactions/app", "timestamp": "2025-11-12T08:22:27.652421Z", "nonce_str": "D0E54573023C47D9949A24596741D976", "body_length": 261, "sign_str_length": 336, "event": "Signature string constructed", "logger": "app.services.subscription_order_service", "level": "debug", "request_id": "6d8a67de920e66bf", "path": "/billing/subscription/order", "remote_addr": "**************", "user_agent": "Dalvik/2.1.0 (Linux; U; Android 13; 22041211AC Build/TP1A.220624.014)", "user_id": 10, "date": "2025-11-12", "time": "16:22:27", "timestamp_unix": 1762935747, "log_level": "DEBUG", "application": "footprint_subscription", "version": "1.0.0"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/services/subscription_order_service.py:84]
2025-11-12 16:22:27,726 DEBUG: {"signature_length": 344, "event": "Signature generated successfully", "logger": "app.services.subscription_order_service", "level": "debug", "request_id": "6d8a67de920e66bf", "method": "POST", "path": "/billing/subscription/order", "remote_addr": "**************", "user_agent": "Dalvik/2.1.0 (Linux; U; Android 13; 22041211AC Build/TP1A.220624.014)", "user_id": 10, "date": "2025-11-12", "time": "16:22:27", "timestamp_unix": 1762935747, "log_level": "DEBUG", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T08:22:27.726078Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/services/subscription_order_service.py:105]
2025-11-12 16:22:27,726 DEBUG: {"mch_id": "1727996379", "cert_serial": "7A70A0F2B47751DB4D29ECF93109A6995274FA40", "timestamp": "2025-11-12T08:22:27.726223Z", "nonce_str": "D0E54573023C47D9949A24596741D976", "event": "Authorization header built", "logger": "app.services.subscription_order_service", "level": "debug", "request_id": "6d8a67de920e66bf", "method": "POST", "path": "/billing/subscription/order", "remote_addr": "**************", "user_agent": "Dalvik/2.1.0 (Linux; U; Android 13; 22041211AC Build/TP1A.220624.014)", "user_id": 10, "date": "2025-11-12", "time": "16:22:27", "timestamp_unix": 1762935747, "log_level": "DEBUG", "application": "footprint_subscription", "version": "1.0.0"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/services/subscription_order_service.py:143]
2025-11-12 16:22:27,726 INFO: {"method": "POST", "url": "https://api.mch.weixin.qq.com/v3/pay/transactions/app", "timestamp": "2025-11-12T08:22:27.726294Z", "nonce_str": "D0E54573023C47D9949A24596741D976", "body_size": 261, "event": "Making WeChat Pay API request", "logger": "app.services.subscription_order_service", "level": "info", "request_id": "6d8a67de920e66bf", "path": "/billing/subscription/order", "remote_addr": "**************", "user_agent": "Dalvik/2.1.0 (Linux; U; Android 13; 22041211AC Build/TP1A.220624.014)", "user_id": 10, "date": "2025-11-12", "time": "16:22:27", "timestamp_unix": 1762935747, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/services/subscription_order_service.py:184]
2025-11-12 16:22:28,249 INFO: {"status_code": 200, "response_size": 52, "event": "WeChat Pay API response received", "logger": "app.services.subscription_order_service", "level": "info", "request_id": "6d8a67de920e66bf", "method": "POST", "path": "/billing/subscription/order", "remote_addr": "**************", "user_agent": "Dalvik/2.1.0 (Linux; U; Android 13; 22041211AC Build/TP1A.220624.014)", "user_id": 10, "date": "2025-11-12", "time": "16:22:28", "timestamp_unix": 1762935748, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T08:22:28.249181Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/services/subscription_order_service.py:200]
2025-11-12 16:22:28,250 INFO: {"user_id": 10, "plan_id": 1, "order_id": 75, "out_trade_no": "FP17629357478a7a8c42", "prepay_id": "wx12162228172821b020912f5b104bd80000", "event": "Subscription order created successfully", "logger": "app.services.subscription_order_service", "level": "info", "request_id": "6d8a67de920e66bf", "method": "POST", "path": "/billing/subscription/order", "remote_addr": "**************", "user_agent": "Dalvik/2.1.0 (Linux; U; Android 13; 22041211AC Build/TP1A.220624.014)", "date": "2025-11-12", "time": "16:22:28", "timestamp_unix": 1762935748, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T08:22:28.250258Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/services/subscription_order_service.py:299]
2025-11-12 16:22:28,250 INFO: {"prepay_id": "wx12162228172821b020912f5b104bd80000", "timestamp": "2025-11-12T08:22:28.250552Z", "nonce_str": "F6FEEDA1DE2E4E96976E7E533B13EE28", "event": "Client payment parameters generated successfully", "logger": "app.services.subscription_order_service", "level": "info", "request_id": "6d8a67de920e66bf", "method": "POST", "path": "/billing/subscription/order", "remote_addr": "**************", "user_agent": "Dalvik/2.1.0 (Linux; U; Android 13; 22041211AC Build/TP1A.220624.014)", "user_id": 10, "date": "2025-11-12", "time": "16:22:28", "timestamp_unix": 1762935748, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/services/subscription_order_service.py:493]
2025-11-12 16:22:28,250 INFO: {"user_id": 10, "plan_id": 1, "order_id": 75, "event": "Subscription order created successfully", "logger": "app.routes.billing", "level": "info", "request_id": "6d8a67de920e66bf", "method": "POST", "path": "/billing/subscription/order", "remote_addr": "**************", "user_agent": "Dalvik/2.1.0 (Linux; U; Android 13; 22041211AC Build/TP1A.220624.014)", "date": "2025-11-12", "time": "16:22:28", "timestamp_unix": 1762935748, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T08:22:28.250760Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/routes/billing.py:524]
2025-11-12 17:01:19,456 INFO: {"event": "Starting expired subscriptions processing task", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "17:01:19", "timestamp_unix": 1762938079, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T09:01:19.456388Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:21]
2025-11-12 17:01:19,525 INFO: {"total_found": 0, "processed": 0, "event": "Processed expired subscriptions", "logger": "app.services.subscription_service", "level": "info", "date": "2025-11-12", "time": "17:01:19", "timestamp_unix": 1762938079, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T09:01:19.525115Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/services/subscription_service.py:250]
2025-11-12 17:01:19,525 INFO: {"processed_count": 0, "event": "Expired subscriptions processing completed", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "17:01:19", "timestamp_unix": 1762938079, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T09:01:19.525425Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:25]
2025-11-12 17:18:14,338 WARNING: Redis connection failed: Error 61 connecting to localhost:6379. Connection refused. [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:39]
2025-11-12 17:18:14,342 WARNING: Redis connection failed: Error 61 connecting to localhost:6379. Connection refused. [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:39]
2025-11-12 17:18:14,353 INFO: {"event": "Initializing scheduler tasks", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "17:18:14", "timestamp_unix": 1762939094, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T09:18:14.353628Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:119]
2025-11-12 17:18:14,353 INFO: {"event": "Scheduler tasks initialized successfully", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "17:18:14", "timestamp_unix": 1762939094, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T09:18:14.353755Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:124]
2025-11-12 17:18:14,353 INFO: {"event": "Initializing scheduler tasks", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "17:18:14", "timestamp_unix": 1762939094, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T09:18:14.353882Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:119]
2025-11-12 17:18:14,354 INFO: {"event": "Scheduler tasks initialized successfully", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "17:18:14", "timestamp_unix": 1762939094, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T09:18:14.354032Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:124]
2025-11-12 17:18:24,242 WARNING: Redis connection failed: Error 61 connecting to localhost:6379. Connection refused. [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:39]
2025-11-12 17:18:24,253 WARNING: Redis connection failed: Error 61 connecting to localhost:6379. Connection refused. [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:39]
2025-11-12 17:18:24,256 INFO: {"event": "Initializing scheduler tasks", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "17:18:24", "timestamp_unix": 1762939104, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T09:18:24.256855Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:119]
2025-11-12 17:18:24,256 INFO: {"event": "Scheduler tasks initialized successfully", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "17:18:24", "timestamp_unix": 1762939104, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T09:18:24.256983Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:124]
2025-11-12 17:18:24,264 INFO: {"event": "Initializing scheduler tasks", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "17:18:24", "timestamp_unix": 1762939104, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T09:18:24.264332Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:119]
2025-11-12 17:18:24,264 INFO: {"event": "Scheduler tasks initialized successfully", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "17:18:24", "timestamp_unix": 1762939104, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T09:18:24.264445Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:124]
2025-11-12 17:18:52,437 WARNING: Redis connection failed: Error 61 connecting to localhost:6379. Connection refused. [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:39]
2025-11-12 17:18:52,443 WARNING: Redis connection failed: Error 61 connecting to localhost:6379. Connection refused. [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:39]
2025-11-12 17:18:52,452 INFO: {"event": "Initializing scheduler tasks", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "17:18:52", "timestamp_unix": 1762939132, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T09:18:52.452194Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:119]
2025-11-12 17:18:52,452 INFO: {"event": "Scheduler tasks initialized successfully", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "17:18:52", "timestamp_unix": 1762939132, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T09:18:52.452313Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:124]
2025-11-12 17:18:52,454 INFO: {"event": "Initializing scheduler tasks", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "17:18:52", "timestamp_unix": 1762939132, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T09:18:52.454161Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:119]
2025-11-12 17:18:52,454 INFO: {"event": "Scheduler tasks initialized successfully", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "17:18:52", "timestamp_unix": 1762939132, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T09:18:52.454271Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:124]
2025-11-12 17:20:22,341 WARNING: Redis connection failed: Error 61 connecting to localhost:6379. Connection refused. [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:39]
2025-11-12 17:20:22,346 WARNING: Redis connection failed: Error 61 connecting to localhost:6379. Connection refused. [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:39]
2025-11-12 17:20:22,357 INFO: {"event": "Initializing scheduler tasks", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "17:20:22", "timestamp_unix": 1762939222, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T09:20:22.357174Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:119]
2025-11-12 17:20:22,357 INFO: {"event": "Scheduler tasks initialized successfully", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "17:20:22", "timestamp_unix": 1762939222, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T09:20:22.357312Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:124]
2025-11-12 17:20:22,357 INFO: {"event": "Initializing scheduler tasks", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "17:20:22", "timestamp_unix": 1762939222, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T09:20:22.357433Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:119]
2025-11-12 17:20:22,357 INFO: {"event": "Scheduler tasks initialized successfully", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "17:20:22", "timestamp_unix": 1762939222, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T09:20:22.357579Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:124]
2025-11-12 18:16:01,564 WARNING: Redis connection failed: Error 61 connecting to localhost:6379. Connection refused. [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:39]
2025-11-12 18:16:01,569 WARNING: Redis connection failed: Error 61 connecting to localhost:6379. Connection refused. [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:39]
2025-11-12 18:16:01,581 INFO: {"event": "Initializing scheduler tasks", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "18:16:01", "timestamp_unix": 1762942561, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T10:16:01.580991Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:119]
2025-11-12 18:16:01,581 INFO: {"event": "Scheduler tasks initialized successfully", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "18:16:01", "timestamp_unix": 1762942561, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T10:16:01.581143Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:124]
2025-11-12 18:16:01,581 INFO: {"event": "Initializing scheduler tasks", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "18:16:01", "timestamp_unix": 1762942561, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T10:16:01.581498Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:119]
2025-11-12 18:16:01,581 INFO: {"event": "Scheduler tasks initialized successfully", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "18:16:01", "timestamp_unix": 1762942561, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T10:16:01.581642Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:124]
2025-11-12 22:12:38,034 WARNING: Redis connection failed: Error 61 connecting to localhost:6379. Connection refused. [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:39]
2025-11-12 22:12:38,038 WARNING: Redis connection failed: Error 61 connecting to localhost:6379. Connection refused. [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:39]
2025-11-12 22:12:38,050 INFO: {"event": "Initializing scheduler tasks", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "22:12:38", "timestamp_unix": 1762956758, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T14:12:38.050298Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:119]
2025-11-12 22:12:38,050 INFO: {"event": "Initializing scheduler tasks", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "22:12:38", "timestamp_unix": 1762956758, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T14:12:38.050335Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:119]
2025-11-12 22:12:38,050 INFO: {"event": "Scheduler tasks initialized successfully", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "22:12:38", "timestamp_unix": 1762956758, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T14:12:38.050427Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:124]
2025-11-12 22:12:38,050 INFO: {"event": "Scheduler tasks initialized successfully", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "22:12:38", "timestamp_unix": 1762956758, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T14:12:38.050462Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:124]
2025-11-12 22:13:04,231 WARNING: Redis connection failed: Error 61 connecting to localhost:6379. Connection refused. [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:39]
2025-11-12 22:13:04,244 WARNING: Redis connection failed: Error 61 connecting to localhost:6379. Connection refused. [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:39]
2025-11-12 22:13:04,246 INFO: {"event": "Initializing scheduler tasks", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "22:13:04", "timestamp_unix": 1762956784, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T14:13:04.246370Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:119]
2025-11-12 22:13:04,246 INFO: {"event": "Scheduler tasks initialized successfully", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "22:13:04", "timestamp_unix": 1762956784, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T14:13:04.246505Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:124]
2025-11-12 22:13:04,255 INFO: {"event": "Initializing scheduler tasks", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "22:13:04", "timestamp_unix": 1762956784, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T14:13:04.255358Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:119]
2025-11-12 22:13:04,255 INFO: {"event": "Scheduler tasks initialized successfully", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "22:13:04", "timestamp_unix": 1762956784, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T14:13:04.255476Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:124]
2025-11-12 22:14:35,036 WARNING: Redis connection failed: Error 61 connecting to localhost:6379. Connection refused. [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:44]
2025-11-12 22:14:35,036 INFO: Flask-Limiter falling back to in-memory storage [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:49]
2025-11-12 22:14:35,043 WARNING: Redis connection failed: Error 61 connecting to localhost:6379. Connection refused. [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:44]
2025-11-12 22:14:35,043 INFO: Flask-Limiter falling back to in-memory storage [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:49]
2025-11-12 22:14:35,051 INFO: {"event": "Initializing scheduler tasks", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "22:14:35", "timestamp_unix": 1762956875, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T14:14:35.051163Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:119]
2025-11-12 22:14:35,051 INFO: {"event": "Scheduler tasks initialized successfully", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "22:14:35", "timestamp_unix": 1762956875, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T14:14:35.051292Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:124]
2025-11-12 22:14:35,054 INFO: {"event": "Initializing scheduler tasks", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "22:14:35", "timestamp_unix": 1762956875, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T14:14:35.054336Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:119]
2025-11-12 22:14:35,054 INFO: {"event": "Scheduler tasks initialized successfully", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "22:14:35", "timestamp_unix": 1762956875, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T14:14:35.054452Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:124]
2025-11-12 22:14:59,233 WARNING: Redis connection failed: Error 61 connecting to localhost:6379. Connection refused. [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:44]
2025-11-12 22:14:59,233 INFO: Flask-Limiter falling back to in-memory storage [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:49]
2025-11-12 22:14:59,235 WARNING: Redis connection failed: Error 61 connecting to localhost:6379. Connection refused. [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:44]
2025-11-12 22:14:59,235 INFO: Flask-Limiter falling back to in-memory storage [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:49]
2025-11-12 22:14:59,240 WARNING: Redis connection failed: Error 61 connecting to localhost:6379. Connection refused. [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:44]
2025-11-12 22:14:59,240 INFO: Flask-Limiter falling back to in-memory storage [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:49]
2025-11-12 22:14:59,241 WARNING: Redis connection failed: Error 61 connecting to localhost:6379. Connection refused. [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:44]
2025-11-12 22:14:59,241 INFO: Flask-Limiter falling back to in-memory storage [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:49]
2025-11-12 22:14:59,251 INFO: {"event": "Initializing scheduler tasks", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "22:14:59", "timestamp_unix": 1762956899, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T14:14:59.251337Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:119]
2025-11-12 22:14:59,251 INFO: {"event": "Scheduler tasks initialized successfully", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "22:14:59", "timestamp_unix": 1762956899, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T14:14:59.251474Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:124]
2025-11-12 22:14:59,253 INFO: {"event": "Initializing scheduler tasks", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "22:14:59", "timestamp_unix": 1762956899, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T14:14:59.253192Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:119]
2025-11-12 22:14:59,253 INFO: {"event": "Scheduler tasks initialized successfully", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "22:14:59", "timestamp_unix": 1762956899, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T14:14:59.253325Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:124]
2025-11-12 22:15:13,483 WARNING: Redis connection failed: Error 61 connecting to localhost:6379. Connection refused. [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:44]
2025-11-12 22:15:13,483 INFO: Flask-Limiter falling back to in-memory storage [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:49]
2025-11-12 22:15:13,502 INFO: {"event": "Initializing scheduler tasks", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "22:15:13", "timestamp_unix": 1762956913, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T14:15:13.502947Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:119]
2025-11-12 22:15:13,503 WARNING: Redis connection failed: Error 61 connecting to localhost:6379. Connection refused. [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:44]
2025-11-12 22:15:13,503 INFO: Flask-Limiter falling back to in-memory storage [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:49]
2025-11-12 22:15:13,503 INFO: {"event": "Scheduler tasks initialized successfully", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "22:15:13", "timestamp_unix": 1762956913, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T14:15:13.503453Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:124]
2025-11-12 22:15:13,517 INFO: {"event": "Initializing scheduler tasks", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "22:15:13", "timestamp_unix": 1762956913, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T14:15:13.517813Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:119]
2025-11-12 22:15:13,517 INFO: {"event": "Scheduler tasks initialized successfully", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "22:15:13", "timestamp_unix": 1762956913, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T14:15:13.517972Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:124]
2025-11-12 22:16:47,219 WARNING: Redis connection failed: Error 61 connecting to localhost:6379. Connection refused. [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:44]
2025-11-12 22:16:47,219 INFO: Flask-Limiter falling back to in-memory storage [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:49]
2025-11-12 22:16:47,235 INFO: {"event": "Initializing scheduler tasks", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "22:16:47", "timestamp_unix": 1762957007, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T14:16:47.235624Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:119]
2025-11-12 22:16:47,235 INFO: {"event": "Scheduler tasks initialized successfully", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "22:16:47", "timestamp_unix": 1762957007, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T14:16:47.235771Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:124]
2025-11-12 22:16:47,342 WARNING: Redis connection failed: Error 61 connecting to localhost:6379. Connection refused. [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:44]
2025-11-12 22:16:47,342 WARNING: Redis connection failed: Error 61 connecting to localhost:6379. Connection refused. [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:44]
2025-11-12 22:16:47,342 INFO: Flask-Limiter falling back to in-memory storage [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:49]
2025-11-12 22:16:47,342 INFO: Flask-Limiter falling back to in-memory storage [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:49]
2025-11-12 22:16:47,349 WARNING: Redis connection failed: Error 61 connecting to localhost:6379. Connection refused. [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:44]
2025-11-12 22:16:47,349 WARNING: Redis connection failed: Error 61 connecting to localhost:6379. Connection refused. [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:44]
2025-11-12 22:16:47,349 WARNING: Redis connection failed: Error 61 connecting to localhost:6379. Connection refused. [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:44]
2025-11-12 22:16:47,350 INFO: Flask-Limiter falling back to in-memory storage [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:49]
2025-11-12 22:16:47,350 INFO: Flask-Limiter falling back to in-memory storage [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:49]
2025-11-12 22:16:47,350 INFO: Flask-Limiter falling back to in-memory storage [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:49]
2025-11-12 22:16:47,350 WARNING: Scheduler initialization failed: Scheduler is already running [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/__init__.py:107]
2025-11-12 22:16:47,350 WARNING: Scheduler initialization failed: Scheduler is already running [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/__init__.py:107]
2025-11-12 22:16:47,350 WARNING: Scheduler initialization failed: Scheduler is already running [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/__init__.py:107]
2025-11-12 22:16:47,667 WARNING: Redis connection failed: Error 61 connecting to localhost:6379. Connection refused. [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:44]
2025-11-12 22:16:47,667 INFO: Flask-Limiter falling back to in-memory storage [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:49]
2025-11-12 22:16:47,678 INFO: {"event": "Initializing scheduler tasks", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "22:16:47", "timestamp_unix": 1762957007, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T14:16:47.678964Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:119]
2025-11-12 22:16:47,679 INFO: {"event": "Scheduler tasks initialized successfully", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "22:16:47", "timestamp_unix": 1762957007, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T14:16:47.679098Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:124]
2025-11-12 22:17:01,708 WARNING: Redis connection failed: Error 61 connecting to localhost:6379. Connection refused. [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:44]
2025-11-12 22:17:01,708 INFO: Flask-Limiter falling back to in-memory storage [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:49]
2025-11-12 22:17:01,712 INFO: {"event": "Initializing scheduler tasks", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "22:17:01", "timestamp_unix": 1762957021, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T14:17:01.712241Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:119]
2025-11-12 22:17:01,712 INFO: {"event": "Scheduler tasks initialized successfully", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "22:17:01", "timestamp_unix": 1762957021, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T14:17:01.712352Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:124]
2025-11-12 22:17:01,716 WARNING: Redis connection failed: Error 61 connecting to localhost:6379. Connection refused. [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:44]
2025-11-12 22:17:01,716 INFO: Flask-Limiter falling back to in-memory storage [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:49]
2025-11-12 22:17:01,719 INFO: {"event": "Initializing scheduler tasks", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "22:17:01", "timestamp_unix": 1762957021, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T14:17:01.719057Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:119]
2025-11-12 22:17:01,719 INFO: {"event": "Scheduler tasks initialized successfully", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "22:17:01", "timestamp_unix": 1762957021, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T14:17:01.719155Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:124]
2025-11-12 22:17:11,596 WARNING: Redis connection failed: Error 61 connecting to localhost:6379. Connection refused. [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:44]
2025-11-12 22:17:11,597 INFO: Flask-Limiter falling back to in-memory storage [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:49]
2025-11-12 22:17:11,600 INFO: {"event": "Initializing scheduler tasks", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "22:17:11", "timestamp_unix": 1762957031, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T14:17:11.600344Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:119]
2025-11-12 22:17:11,600 INFO: {"event": "Scheduler tasks initialized successfully", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "22:17:11", "timestamp_unix": 1762957031, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T14:17:11.600443Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:124]
2025-11-12 22:17:11,672 WARNING: Redis connection failed: Error 61 connecting to localhost:6379. Connection refused. [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:44]
2025-11-12 22:17:11,672 WARNING: Redis connection failed: Error 61 connecting to localhost:6379. Connection refused. [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:44]
2025-11-12 22:17:11,672 INFO: Flask-Limiter falling back to in-memory storage [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:49]
2025-11-12 22:17:11,672 INFO: Flask-Limiter falling back to in-memory storage [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:49]
2025-11-12 22:18:01,826 WARNING: Redis connection failed: Error 61 connecting to localhost:6379. Connection refused. [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:44]
2025-11-12 22:18:01,826 INFO: Flask-Limiter falling back to in-memory storage [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:49]
2025-11-12 22:18:01,829 INFO: {"event": "Initializing scheduler tasks", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "22:18:01", "timestamp_unix": 1762957081, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T14:18:01.829596Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:119]
2025-11-12 22:18:01,829 INFO: {"event": "Scheduler tasks initialized successfully", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "22:18:01", "timestamp_unix": 1762957081, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T14:18:01.829773Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:124]
2025-11-12 22:18:01,831 WARNING: Redis connection failed: Error 61 connecting to localhost:6379. Connection refused. [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:44]
2025-11-12 22:18:01,831 INFO: Flask-Limiter falling back to in-memory storage [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:49]
2025-11-12 22:18:01,834 INFO: {"event": "Initializing scheduler tasks", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "22:18:01", "timestamp_unix": 1762957081, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T14:18:01.834037Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:119]
2025-11-12 22:18:01,834 INFO: {"event": "Scheduler tasks initialized successfully", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "22:18:01", "timestamp_unix": 1762957081, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T14:18:01.834162Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:124]
2025-11-12 22:18:17,796 WARNING: Redis connection failed: Error 61 connecting to localhost:6379. Connection refused. [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:44]
2025-11-12 22:18:17,796 INFO: Flask-Limiter falling back to in-memory storage [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:49]
2025-11-12 22:18:17,799 INFO: {"event": "Initializing scheduler tasks", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "22:18:17", "timestamp_unix": 1762957097, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T14:18:17.799372Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:119]
2025-11-12 22:18:17,799 INFO: {"event": "Scheduler tasks initialized successfully", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "22:18:17", "timestamp_unix": 1762957097, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T14:18:17.799481Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:124]
2025-11-12 22:18:17,805 WARNING: Redis connection failed: Error 61 connecting to localhost:6379. Connection refused. [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:44]
2025-11-12 22:18:17,805 INFO: Flask-Limiter falling back to in-memory storage [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:49]
2025-11-12 22:18:17,807 INFO: {"event": "Initializing scheduler tasks", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "22:18:17", "timestamp_unix": 1762957097, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T14:18:17.807937Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:119]
2025-11-12 22:18:17,808 INFO: {"event": "Scheduler tasks initialized successfully", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "22:18:17", "timestamp_unix": 1762957097, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T14:18:17.808043Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:124]
2025-11-12 22:18:27,648 WARNING: Redis connection failed: Error 61 connecting to localhost:6379. Connection refused. [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:44]
2025-11-12 22:18:27,648 INFO: Flask-Limiter falling back to in-memory storage [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:49]
2025-11-12 22:18:27,650 INFO: {"event": "Initializing scheduler tasks", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "22:18:27", "timestamp_unix": 1762957107, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T14:18:27.650759Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:119]
2025-11-12 22:18:27,650 INFO: {"event": "Scheduler tasks initialized successfully", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "22:18:27", "timestamp_unix": 1762957107, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T14:18:27.650924Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:124]
2025-11-12 22:18:27,670 WARNING: Redis connection failed: Error 61 connecting to localhost:6379. Connection refused. [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:44]
2025-11-12 22:18:27,670 INFO: Flask-Limiter falling back to in-memory storage [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:49]
2025-11-12 22:18:27,675 INFO: {"event": "Initializing scheduler tasks", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "22:18:27", "timestamp_unix": 1762957107, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T14:18:27.675761Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:119]
2025-11-12 22:18:27,675 INFO: {"event": "Scheduler tasks initialized successfully", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "22:18:27", "timestamp_unix": 1762957107, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T14:18:27.675904Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:124]
2025-11-12 22:18:47,607 WARNING: Redis connection failed: Error 61 connecting to localhost:6379. Connection refused. [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:44]
2025-11-12 22:18:47,607 INFO: Flask-Limiter falling back to in-memory storage [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:49]
2025-11-12 22:18:47,610 INFO: {"event": "Initializing scheduler tasks", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "22:18:47", "timestamp_unix": 1762957127, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T14:18:47.610632Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:119]
2025-11-12 22:18:47,610 INFO: {"event": "Scheduler tasks initialized successfully", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "22:18:47", "timestamp_unix": 1762957127, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T14:18:47.610734Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:124]
2025-11-12 22:18:47,694 WARNING: Redis connection failed: Error 61 connecting to localhost:6379. Connection refused. [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:44]
2025-11-12 22:18:47,694 WARNING: Redis connection failed: Error 61 connecting to localhost:6379. Connection refused. [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:44]
2025-11-12 22:18:47,694 INFO: Flask-Limiter falling back to in-memory storage [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:49]
2025-11-12 22:18:47,694 INFO: Flask-Limiter falling back to in-memory storage [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:49]
2025-11-12 22:19:17,742 WARNING: Redis connection failed: Error 61 connecting to localhost:6379. Connection refused. [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:44]
2025-11-12 22:19:17,742 INFO: Flask-Limiter falling back to in-memory storage [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:49]
2025-11-12 22:19:17,745 INFO: {"event": "Initializing scheduler tasks", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "22:19:17", "timestamp_unix": 1762957157, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T14:19:17.745942Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:119]
2025-11-12 22:19:17,746 INFO: {"event": "Scheduler tasks initialized successfully", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "22:19:17", "timestamp_unix": 1762957157, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T14:19:17.746066Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:124]
2025-11-12 22:19:17,835 WARNING: Redis connection failed: Error 61 connecting to localhost:6379. Connection refused. [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:44]
2025-11-12 22:19:17,835 WARNING: Redis connection failed: Error 61 connecting to localhost:6379. Connection refused. [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:44]
2025-11-12 22:19:17,836 INFO: Flask-Limiter falling back to in-memory storage [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:49]
2025-11-12 22:19:17,836 INFO: Flask-Limiter falling back to in-memory storage [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:49]
2025-11-12 22:19:27,788 WARNING: Redis connection failed: Error 61 connecting to localhost:6379. Connection refused. [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:44]
2025-11-12 22:19:27,788 INFO: Flask-Limiter falling back to in-memory storage [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:49]
2025-11-12 22:19:27,792 INFO: {"event": "Initializing scheduler tasks", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "22:19:27", "timestamp_unix": 1762957167, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T14:19:27.792104Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:119]
2025-11-12 22:19:27,792 INFO: {"event": "Scheduler tasks initialized successfully", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "22:19:27", "timestamp_unix": 1762957167, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T14:19:27.792207Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:124]
2025-11-12 22:19:27,875 WARNING: Redis connection failed: Error 61 connecting to localhost:6379. Connection refused. [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:44]
2025-11-12 22:19:27,875 WARNING: Redis connection failed: Error 61 connecting to localhost:6379. Connection refused. [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:44]
2025-11-12 22:19:27,875 INFO: Flask-Limiter falling back to in-memory storage [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:49]
2025-11-12 22:19:27,875 INFO: Flask-Limiter falling back to in-memory storage [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:49]
2025-11-12 22:19:34,385 WARNING: Redis connection failed: Error 61 connecting to localhost:6379. Connection refused. [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:44]
2025-11-12 22:19:34,385 INFO: Flask-Limiter falling back to in-memory storage [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:49]
2025-11-12 22:19:34,388 INFO: {"event": "Initializing scheduler tasks", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "22:19:34", "timestamp_unix": 1762957174, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T14:19:34.388937Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:119]
2025-11-12 22:19:34,389 INFO: {"event": "Scheduler tasks initialized successfully", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "22:19:34", "timestamp_unix": 1762957174, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T14:19:34.389052Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:124]
2025-11-12 22:19:34,472 WARNING: Redis connection failed: Error 61 connecting to localhost:6379. Connection refused. [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:44]
2025-11-12 22:19:34,472 WARNING: Redis connection failed: Error 61 connecting to localhost:6379. Connection refused. [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:44]
2025-11-12 22:19:34,472 INFO: Flask-Limiter falling back to in-memory storage [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:49]
2025-11-12 22:19:34,472 INFO: Flask-Limiter falling back to in-memory storage [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:49]
2025-11-12 22:20:31,704 WARNING: Redis connection failed: Error 61 connecting to localhost:6379. Connection refused. [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:44]
2025-11-12 22:20:31,704 INFO: Flask-Limiter falling back to in-memory storage [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:49]
2025-11-12 22:20:31,709 INFO: {"event": "Initializing scheduler tasks", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "22:20:31", "timestamp_unix": 1762957231, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T14:20:31.708994Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:119]
2025-11-12 22:20:31,709 INFO: {"event": "Scheduler tasks initialized successfully", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "22:20:31", "timestamp_unix": 1762957231, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T14:20:31.709239Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:124]
2025-11-12 22:20:31,808 WARNING: Redis connection failed: Error 61 connecting to localhost:6379. Connection refused. [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:44]
2025-11-12 22:20:31,808 WARNING: Redis connection failed: Error 61 connecting to localhost:6379. Connection refused. [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:44]
2025-11-12 22:20:31,808 INFO: Flask-Limiter falling back to in-memory storage [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:49]
2025-11-12 22:20:31,808 INFO: Flask-Limiter falling back to in-memory storage [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:49]
2025-11-12 22:20:31,816 WARNING: Redis connection failed: Error 61 connecting to localhost:6379. Connection refused. [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:44]
2025-11-12 22:20:31,816 WARNING: Redis connection failed: Error 61 connecting to localhost:6379. Connection refused. [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:44]
2025-11-12 22:20:31,816 WARNING: Redis connection failed: Error 61 connecting to localhost:6379. Connection refused. [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:44]
2025-11-12 22:20:31,816 INFO: Flask-Limiter falling back to in-memory storage [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:49]
2025-11-12 22:20:31,816 INFO: Flask-Limiter falling back to in-memory storage [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:49]
2025-11-12 22:20:31,816 INFO: Flask-Limiter falling back to in-memory storage [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:49]
2025-11-12 22:20:31,816 INFO: {"event": "Initializing scheduler tasks", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "22:20:31", "timestamp_unix": 1762957231, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T14:20:31.816823Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:119]
2025-11-12 22:20:31,816 INFO: {"event": "Initializing scheduler tasks", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "22:20:31", "timestamp_unix": 1762957231, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T14:20:31.816823Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:119]
2025-11-12 22:20:31,816 INFO: {"event": "Initializing scheduler tasks", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "22:20:31", "timestamp_unix": 1762957231, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T14:20:31.816823Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:119]
2025-11-12 22:20:31,816 INFO: {"event": "Scheduler tasks initialized successfully", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "22:20:31", "timestamp_unix": 1762957231, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T14:20:31.816983Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:124]
2025-11-12 22:20:31,816 INFO: {"event": "Scheduler tasks initialized successfully", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "22:20:31", "timestamp_unix": 1762957231, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T14:20:31.816983Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:124]
2025-11-12 22:20:31,816 INFO: {"event": "Scheduler tasks initialized successfully", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "22:20:31", "timestamp_unix": 1762957231, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T14:20:31.816983Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:124]
2025-11-12 22:20:32,120 WARNING: Redis connection failed: Error 61 connecting to localhost:6379. Connection refused. [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:44]
2025-11-12 22:20:32,120 INFO: Flask-Limiter falling back to in-memory storage [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:49]
2025-11-12 22:20:32,122 INFO: {"event": "Initializing scheduler tasks", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "22:20:32", "timestamp_unix": 1762957232, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T14:20:32.122870Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:119]
2025-11-12 22:20:32,123 INFO: {"event": "Scheduler tasks initialized successfully", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "22:20:32", "timestamp_unix": 1762957232, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T14:20:32.123015Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:124]
2025-11-12 22:28:25,565 WARNING: Redis connection failed: Error 61 connecting to localhost:6379. Connection refused. [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:44]
2025-11-12 22:28:25,566 INFO: Flask-Limiter falling back to in-memory storage [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:49]
2025-11-12 22:28:25,569 INFO: {"event": "Initializing scheduler tasks", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "22:28:25", "timestamp_unix": 1762957705, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T14:28:25.569548Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:119]
2025-11-12 22:28:25,569 INFO: {"event": "Scheduler tasks initialized successfully", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "22:28:25", "timestamp_unix": 1762957705, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T14:28:25.569628Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:124]
2025-11-12 22:28:32,370 WARNING: Redis connection failed: Error 61 connecting to localhost:6379. Connection refused. [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:44]
2025-11-12 22:28:32,370 INFO: Flask-Limiter falling back to in-memory storage [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:49]
2025-11-12 22:28:32,373 INFO: {"event": "Initializing scheduler tasks", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "22:28:32", "timestamp_unix": 1762957712, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T14:28:32.373616Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:119]
2025-11-12 22:28:32,373 INFO: {"event": "Scheduler tasks initialized successfully", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "22:28:32", "timestamp_unix": 1762957712, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T14:28:32.373710Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:124]
2025-11-12 22:28:32,462 WARNING: Redis connection failed: Error 61 connecting to localhost:6379. Connection refused. [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:44]
2025-11-12 22:28:32,462 WARNING: Redis connection failed: Error 61 connecting to localhost:6379. Connection refused. [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:44]
2025-11-12 22:28:32,462 INFO: Flask-Limiter falling back to in-memory storage [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:49]
2025-11-12 22:28:32,462 INFO: Flask-Limiter falling back to in-memory storage [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:49]
2025-11-12 22:30:42,491 WARNING: Redis connection failed: Error 61 connecting to localhost:6379. Connection refused. [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:44]
2025-11-12 22:30:42,491 INFO: Flask-Limiter falling back to in-memory storage [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:49]
2025-11-12 22:30:42,495 INFO: {"event": "Initializing scheduler tasks", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "22:30:42", "timestamp_unix": 1762957842, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T14:30:42.495016Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:119]
2025-11-12 22:30:42,495 INFO: {"event": "Scheduler tasks initialized successfully", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "22:30:42", "timestamp_unix": 1762957842, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T14:30:42.495112Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:124]
2025-11-12 22:30:42,893 WARNING: Redis connection failed: Error 61 connecting to localhost:6379. Connection refused. [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:44]
2025-11-12 22:30:42,893 INFO: Flask-Limiter falling back to in-memory storage [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:49]
2025-11-12 22:30:42,894 INFO: {"event": "Initializing scheduler tasks", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "22:30:42", "timestamp_unix": 1762957842, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T14:30:42.894851Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:119]
2025-11-12 22:30:42,894 INFO: {"event": "Scheduler tasks initialized successfully", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "22:30:42", "timestamp_unix": 1762957842, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T14:30:42.894945Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:124]
