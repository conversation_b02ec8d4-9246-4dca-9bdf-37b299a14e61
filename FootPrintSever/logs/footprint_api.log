{"event_type": "api_request", "endpoint": "/api***MASKED***ions", "method": "POST", "user_id": "user_123", "request_size": 1024, "client_ip": "*************", "event": "API request", "logger": "api", "level": "info", "date": "2025-11-12", "time": "15:57:15", "timestamp_unix": 1762934235, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T07:57:15.120554Z"}
{"event_type": "api_response", "endpoint": "/api***MASKED***ions", "method": "POST", "status_code": 201, "user_id": "user_123", "response_time_ms": 150, "subscription_id": "sub_456", "event": "API response", "logger": "api", "level": "info", "date": "2025-11-12", "time": "15:57:15", "timestamp_unix": 1762934235, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T07:57:15.120659Z"}
2025-11-12 16:01:29,724 INFO: {"event_type": "api_request", "endpoint": "/api***MASKED***ions", "method": "POST", "user_id": "user_123", "request_size": 1024, "client_ip": "*************", "event": "API request", "logger": "api", "level": "info", "date": "2025-11-12", "time": "16:01:29", "timestamp_unix": 1762934489, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T08:01:29.724304Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/venv/lib/python3.9/site-packages/structlog/_base.py:223]
2025-11-12 16:01:29,724 INFO: {"event_type": "api_response", "endpoint": "/api***MASKED***ions", "method": "POST", "status_code": 201, "user_id": "user_123", "response_time_ms": 150, "subscription_id": "sub_456", "event": "API response", "logger": "api", "level": "info", "date": "2025-11-12", "time": "16:01:29", "timestamp_unix": 1762934489, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T08:01:29.724384Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/venv/lib/python3.9/site-packages/structlog/_base.py:223]
