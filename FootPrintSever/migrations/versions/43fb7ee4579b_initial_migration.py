"""Initial migration

Revision ID: 43fb7ee4579b
Revises: ca0b3151aa9d
Create Date: 2025-10-15 15:34:34.327190

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '43fb7ee4579b'
down_revision = 'ca0b3151aa9d'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    # Safely drop indexes if they exist
    from sqlalchemy import text

    connection = op.get_bind()

    # List of indexes to drop with their table names
    indexes_to_drop = [
        ('idx_orders_status_created', 'orders'),
        ('idx_orders_user_created', 'orders'),
        ('idx_payment_events_processed_created', 'payment_events'),
        ('idx_subscriptions_end_at_desc', 'subscriptions'),
        ('idx_subscriptions_status_end_at', 'subscriptions'),
        ('idx_subscriptions_user_status', 'subscriptions'),
        ('idx_users_created_at_desc', 'users'),
        ('idx_users_nickname_search', 'users'),
        ('idx_users_updated_at_desc', 'users')
    ]

    for index_name, table_name in indexes_to_drop:
        try:
            # Check if index exists before dropping
            result = connection.execute(text(
                "SELECT COUNT(*) FROM information_schema.statistics "
                "WHERE table_schema = DATABASE() AND table_name = :table_name AND index_name = :index_name"
            ), {"table_name": table_name, "index_name": index_name})

            if result.scalar() > 0:
                op.drop_index(op.f(index_name), table_name=table_name)
                print(f"Dropped index {index_name} from table {table_name}")
            else:
                print(f"Index {index_name} does not exist in table {table_name}, skipping")
        except Exception as e:
            print(f"Error dropping index {index_name}: {e}")
            # Continue with other indexes
            pass
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_index(op.f('idx_users_updated_at_desc'), 'users', ['updated_at'], unique=False)
    op.create_index(op.f('idx_users_nickname_search'), 'users', ['nickname'], unique=False)
    op.create_index(op.f('idx_users_created_at_desc'), 'users', ['created_at'], unique=False)
    op.create_index(op.f('idx_subscriptions_user_status'), 'subscriptions', ['user_id', 'status'], unique=False)
    op.create_index(op.f('idx_subscriptions_status_end_at'), 'subscriptions', ['status', 'end_at'], unique=False)
    op.create_index(op.f('idx_subscriptions_end_at_desc'), 'subscriptions', ['end_at'], unique=False)
    op.create_index(op.f('idx_payment_events_processed_created'), 'payment_events', ['processed', 'created_at'], unique=False)
    op.create_index(op.f('idx_orders_user_created'), 'orders', ['user_id', 'created_at'], unique=False)
    op.create_index(op.f('idx_orders_status_created'), 'orders', ['status', 'created_at'], unique=False)
    # ### end Alembic commands ###
