"""add app_version to users table

Revision ID: d4e6f7a8b9c0
Revises: 20251102200032
Create Date: 2024-11-02 12:00:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'd4e6f7a8b9c0'
down_revision = '20251102200032'
branch_labels = None
depends_on = None


def upgrade():
    # Add app_version column to users table
    op.add_column('users', sa.Column('app_version', sa.String(length=32), nullable=True))


def downgrade():
    # Remove app_version column from users table
    op.drop_column('users', 'app_version')
