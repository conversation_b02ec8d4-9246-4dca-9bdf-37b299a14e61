"""create app_settings table

Revision ID: 20251102200032
Revises: c9d5e3f2a6b7
Create Date: 2025-11-02 20:00:32.000000

"""
from alembic import op
import sqlalchemy as sa
from datetime import datetime


# revision identifiers, used by Alembic.
revision = '20251102200032'
down_revision = 'c9d5e3f2a6b7'
branch_labels = None
depends_on = None


def upgrade():
    # 创建 app_settings 表
    op.create_table('app_settings',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('latest_version_code', sa.Integer(), nullable=False, comment='最新版本号'),
        sa.Column('min_version_code', sa.Integer(), nullable=False, comment='最低可用版本号'),
        sa.Column('download_url', sa.String(length=512), nullable=False, comment='新版本下载地址'),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), nullable=False),
        sa.PrimaryKeyConstraint('id')
    )
    
    # 插入默认配置
    op.execute(
        f"""
        INSERT INTO app_settings (latest_version_code, min_version_code, download_url, created_at, updated_at)
        VALUES (1, 1, '', '{datetime.utcnow().isoformat()}', '{datetime.utcnow().isoformat()}')
        """
    )


def downgrade():
    op.drop_table('app_settings')
