#!/usr/bin/env python3
"""
FootPrint 订阅服务 - 宝塔面板启动文件
简化版启动脚本，适用于宝塔 Python 项目管理
"""
import os
import sys
from dotenv import load_dotenv

# 添加项目根目录到 Python 路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

# 加载环境变量
load_dotenv()

# 只在开发环境加载 .env.local
flask_env = os.environ.get('FLASK_ENV', 'development')
if flask_env == 'development':
    load_dotenv('.env.local')

# 清除代理环境变量
proxy_vars = ['http_proxy', 'https_proxy', 'all_proxy', 'HTTP_PROXY', 'HTTPS_PROXY', 'ALL_PROXY']
for var in proxy_vars:
    if var in os.environ:
        del os.environ[var]

from app import create_app
from app.config import config

# 创建应用实例
config_name = os.environ.get('FLASK_ENV', 'development')
app = create_app(config[config_name])

# 获取端口配置
port = int(os.environ.get('PORT', 5000))

if __name__ == '__main__':
    # 打印启动信息
    print(f"🚀 FootPrint 订阅服务启动")
    print(f"🔧 环境: {config_name}")
    print(f"📍 端口: {port}")
    print("=" * 40)
    
    # 根据环境选择启动方式
    if config_name == 'production':
        # 生产环境：使用 Gunicorn (如果可用)
        try:
            import gunicorn.app.wsgiapp as wsgi
            sys.argv = [
                'gunicorn',
                '--bind', f'0.0.0.0:{port}',
                '--workers', '4',
                '--timeout', '120',
                'run:app'
            ]
            wsgi.run()
        except ImportError:
            # 回退到 Flask 开发服务器
            app.run(host='0.0.0.0', port=port, debug=False)
    else:
        # 开发环境：使用 Flask 开发服务器
        app.run(host='127.0.0.1', port=port, debug=True)
