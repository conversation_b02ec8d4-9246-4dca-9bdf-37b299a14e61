# 版本更新"不再提示"功能实现

## 功能概述

为 Android 应用的版本更新对话框添加"不再提示此版本"功能。当用户选择忽略某个版本后，该版本将不再弹出更新提示，直到有新的版本发布。

## 实现内容

### 1. 数据存储

**文件**: `FootPrint/app/src/main/java/com/lfb/android/footprint/prefs/AppPrefs.kt`

添加了新的偏好设置属性：
```kotlin
var ignoredVersionCode by Preference(context, "ignored_version_code", 0)  // 被忽略的版本号
```

### 2. 版本检查逻辑

**文件**: `FootPrint/app/src/main/java/com/lfb/android/footprint/Manager/VersionManager.kt`

修改 `checkUpdate()` 方法，添加 `ignoredVersionCode` 参数：
- 在可选更新场景下，检查最新版本是否被用户忽略
- 如果被忽略，返回 `NoUpdate` 结果，不显示更新提示
- 强制更新不受忽略设置影响

### 3. 对话框UI

**文件**: `FootPrint/app/src/main/java/com/lfb/android/footprint/ui/components/VersionUpdateDialog.kt`

修改对话框布局：
- 添加 `onIgnore` 回调参数
- 在可选更新模式下，显示三个按钮：
  - "前去更新"（主按钮，全宽）
  - "取消"（次要按钮，左侧）
  - "不再提示"（次要按钮，右侧）
- 强制更新模式下仍然只显示"前去更新"按钮

### 4. 全局对话框管理器

**文件**: `FootPrint/app/src/main/java/com/lfb/android/footprint/Manager/GlobalVersionUpdateDialog.kt`

添加 `onIgnore` 参数支持，传递给 `VersionUpdateDialogManager`。

### 5. 应用集成

**文件**: `FootPrint/app/src/main/java/com/lfb/android/footprint/MyApplication.kt`

在 `checkMembershipStatus()` 方法的版本检查回调中：
- 从 `AppPrefs` 读取已忽略的版本号
- 传递给 `VersionManager.checkUpdate()`
- 在可选更新场景下，添加 `onIgnore` 回调：
  - 保存被忽略的版本号到 `AppPrefs`
  - 关闭对话框

## 使用流程

### 用户操作流程

1. 应用启动或恢复时，自动检查版本更新
2. 如果有新版本且不是强制更新：
   - 显示版本更新对话框
   - 用户可以选择：
     - **前去更新**: 打开浏览器下载新版本
     - **取消**: 关闭对话框，下次启动仍会提示
     - **不再提示**: 记录该版本号，后续不再提示此版本
3. 如果用户选择"不再提示"：
   - 该版本号被保存到本地
   - 下次启动时，该版本不会再弹出提示
   - 当有更新的版本发布时，会重新提示

### 版本忽略逻辑

- **可选更新**: 可以被忽略
  - 当前版本 < 最新版本
  - 当前版本 >= 最低版本
  - 用户点击"不再提示"后，记录最新版本号
  
- **强制更新**: 不能被忽略
  - 当前版本 < 最低版本
  - 必须更新才能继续使用
  - 不显示"不再提示"按钮

- **忽略清除**: 
  - 当有新的版本发布时，之前忽略的版本号失效
  - 例如：用户忽略了版本2，当版本3发布时，会重新提示

## 技术细节

### 版本比较逻辑

```kotlin
when {
    currentVersion < minVersionCode -> {
        // 强制更新（不可忽略）
        ForceUpdate(...)
    }
    currentVersion < latestVersionCode -> {
        // 检查是否被忽略
        if (latestVersionCode == ignoredVersionCode) {
            NoUpdate(...)  // 已忽略，不提示
        } else {
            OptionalUpdate(...)  // 可选更新
        }
    }
    else -> {
        // 无需更新
        NoUpdate(...)
    }
}
```

### UI布局变化

**可选更新模式**:
```
┌─────────────────────────┐
│   发现新版本             │
│   当前版本：1            │
│   最新版本：2            │
│   建议您更新...          │
│                         │
│  ┌───────────────────┐  │
│  │    前去更新        │  │  ← 主按钮（全宽）
│  └───────────────────┘  │
│  ┌─────────┐ ┌────────┐ │
│  │  取消   │ │不再提示 │ │  ← 次要按钮（并排）
│  └─────────┘ └────────┘ │
└─────────────────────────┘
```

**强制更新模式**:
```
┌─────────────────────────┐
│ 发现新版本（强制更新）   │
│   当前版本：1            │
│   最新版本：3            │
│   您的版本过低...        │
│                         │
│  ┌───────────────────┐  │
│  │    前去更新        │  │  ← 只有一个按钮
│  └───────────────────┘  │
└─────────────────────────┘
```

## 测试建议

### 测试场景

1. **可选更新 + 不再提示**:
   - 后台设置：latest=2, min=1
   - 应用版本：versionCode=1
   - 操作：点击"不再提示"
   - 预期：对话框关闭，重启应用不再提示版本2

2. **新版本发布后重新提示**:
   - 后台设置：latest=3, min=1
   - 应用版本：versionCode=1
   - 已忽略：版本2
   - 预期：显示版本3的更新提示

3. **强制更新不受影响**:
   - 后台设置：latest=3, min=2
   - 应用版本：versionCode=1
   - 已忽略：版本3
   - 预期：仍然显示强制更新对话框（忽略设置无效）

4. **取消按钮**:
   - 点击"取消"
   - 预期：对话框关闭，重启应用仍会提示

## 相关文件清单

### 修改的文件
- `FootPrint/app/src/main/java/com/lfb/android/footprint/prefs/AppPrefs.kt` - 添加忽略版本号存储
- `FootPrint/app/src/main/java/com/lfb/android/footprint/Manager/VersionManager.kt` - 添加忽略版本检查逻辑
- `FootPrint/app/src/main/java/com/lfb/android/footprint/ui/components/VersionUpdateDialog.kt` - 添加"不再提示"按钮
- `FootPrint/app/src/main/java/com/lfb/android/footprint/Manager/GlobalVersionUpdateDialog.kt` - 添加 onIgnore 参数支持
- `FootPrint/app/src/main/java/com/lfb/android/footprint/MyApplication.kt` - 集成忽略版本功能

## 完成状态

✅ 数据存储（AppPrefs）
✅ 版本检查逻辑（VersionManager）
✅ 对话框UI（VersionUpdateDialog）
✅ 全局管理器（GlobalVersionUpdateDialog）
✅ 应用集成（MyApplication）
✅ 代码语法检查通过

功能已完整实现，可以进行测试。
